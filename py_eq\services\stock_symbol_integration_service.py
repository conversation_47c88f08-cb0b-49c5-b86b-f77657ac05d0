"""
Stock Symbol Integration Service

This service integrates stocks_to_monitor.csv with MongoDB symbol mappings.
It provides a unified interface for getting stock symbols and their corresponding tokens.
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Set
import pandas as pd

from services.stock_monitor import StockMonitor, MonitoredStock
from services.centralized_mapping_client import CentralizedMappingClient


class StockSymbolIntegrationService:
    """
    Service that integrates stocks_to_monitor.csv with MongoDB symbol mappings.
    Provides methods to get stock symbols and their corresponding tokens.
    """

    def __init__(self, data_directory: str = "data", logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.data_directory = data_directory
        
        # Initialize stock monitor for reading stocks_to_monitor.csv
        self.stock_monitor = StockMonitor(data_directory=data_directory, logger=logger)
        
        # Initialize centralized mapping client for MongoDB symbol mappings
        self.mapping_client = CentralizedMappingClient(logger=logger)
        
        # Cache for symbol tokens to reduce MongoDB queries
        self._token_cache: Dict[str, Tuple[str, str]] = {}
        
        # Initialize
        self.logger.info("🔄 Initializing Stock Symbol Integration Service")
        self._initialize()
    
    def _initialize(self):
        """Initialize the service by loading stocks and preloading tokens"""
        # Load monitored stocks
        self.monitored_stocks = self.stock_monitor.get_monitored_stocks(enabled_only=True)
        self.logger.info(f"📋 Loaded {len(self.monitored_stocks)} stocks from stocks_to_monitor.csv")
        
        # Preload tokens for all monitored stocks
        self._preload_tokens()
    
    def _preload_tokens(self):
        """Preload tokens for all monitored stocks to reduce MongoDB queries"""
        symbols = [stock.symbol for stock in self.monitored_stocks]
        
        # Check MongoDB connection
        if not self.mapping_client.is_connected():
            self.logger.warning("⚠️ MongoDB not connected. Symbol tokens will be fetched on demand.")
            return
        
        # Get tokens for all symbols
        tokens_loaded = 0
        for symbol in symbols:
            token_info = self.mapping_client.get_smartapi_token(symbol, 'EQ')
            if token_info:
                self._token_cache[symbol] = token_info
                tokens_loaded += 1
        
        self.logger.info(f"📊 Preloaded {tokens_loaded}/{len(symbols)} symbol tokens from MongoDB")
    
    def get_monitored_stocks(self, strategy: Optional[str] = None, enabled_only: bool = True) -> List[MonitoredStock]:
        """Get list of monitored stocks from stocks_to_monitor.csv"""
        return self.stock_monitor.get_monitored_stocks(strategy=strategy, enabled_only=enabled_only)
    
    def get_monitored_symbols(self, strategy: Optional[str] = None, enabled_only: bool = True) -> List[str]:
        """Get list of monitored symbols from stocks_to_monitor.csv"""
        stocks = self.get_monitored_stocks(strategy=strategy, enabled_only=enabled_only)
        return [stock.symbol for stock in stocks]
    
    def get_symbol_token(self, symbol: str) -> Optional[Tuple[str, str]]:
        """
        Get token and exchange for a symbol using MongoDB
        
        Args:
            symbol: Stock symbol (e.g., 'RELIANCE')
            
        Returns:
            Tuple of (token, exchange) or None if not found
        """
        # Check cache first
        if symbol in self._token_cache:
            return self._token_cache[symbol]
        
        # Get from MongoDB
        token_info = self.mapping_client.get_smartapi_token(symbol, 'EQ')
        
        # Cache the result
        if token_info:
            self._token_cache[symbol] = token_info
        
        return token_info
    
    def get_symbol_tokens_batch(self, symbols: List[str]) -> Dict[str, Tuple[str, str]]:
        """
        Get tokens for multiple symbols at once
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to (token, exchange) tuples
        """
        result = {}
        
        for symbol in symbols:
            token_info = self.get_symbol_token(symbol)
            if token_info:
                result[symbol] = token_info
        
        return result
    
    def reload(self):
        """Reload stocks from CSV and clear token cache"""
        self.stock_monitor.reload_stocks()
        self.monitored_stocks = self.stock_monitor.get_monitored_stocks(enabled_only=True)
        self._token_cache.clear()
        self._preload_tokens()
        self.logger.info(f"🔄 Reloaded {len(self.monitored_stocks)} stocks from stocks_to_monitor.csv")
    
    def get_stock_statistics(self) -> Dict:
        """Get statistics about monitored stocks"""
        return self.stock_monitor.get_stock_statistics()
    
    def get_connection_status(self) -> Dict:
        """Get MongoDB connection status"""
        return self.mapping_client.get_connection_status()