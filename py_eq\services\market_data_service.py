"""
Market data service for fetching price data and managing symbol mappings
"""
import logging
import random
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd

from models.candle import Candle
from services.centralized_mapping_client import CentralizedMappingClient


class MarketDataServiceInterface(ABC):
    """Abstract interface for market data services"""

    @abstractmethod
    def get_last_price(self, symbol: str) -> Optional[float]:
        """Get the last traded price for a symbol"""
        pass

    @abstractmethod
    def get_historical_data(self, symbol: str, timeframe: str, days: int = 30) -> List[Candle]:
        """Get historical candle data"""
        pass

    @abstractmethod
    def get_symbol_token(self, symbol: str) -> Optional[Tuple[str, str]]:
        """Get token and exchange for a symbol"""
        pass


class SimulatedMarketDataService(MarketDataServiceInterface):
    """Simulated market data service for testing"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.price_cache: Dict[str, float] = {}
        self.historical_data: Dict[str, List[Candle]] = {}

        # Initialize centralized mapping client
        self.mapping_client = CentralizedMappingClient(logger=logger)

        # Legacy symbol mapping for fallback (will be removed gradually)
        self._legacy_symbol_mapping = {
            'RELIANCE': ('2885', 'NSE'),
            'HDFCBANK': ('1333', 'NSE'),
            'INFY': ('1594', 'NSE'),
            'TCS': ('11536', 'NSE'),
            'ICICIBANK': ('4963', 'NSE'),
            'HDFC': ('1330', 'NSE'),
            'ITC': ('424', 'NSE'),
            'KOTAKBANK': ('492', 'NSE'),
            'LT': ('11483', 'NSE'),
            'SBIN': ('3045', 'NSE'),
            'BHARTIARTL': ('10604', 'NSE'),
            'HINDUNILVR': ('1394', 'NSE'),
            'BAJFINANCE': ('317', 'NSE'),
            'ASIANPAINT': ('236', 'NSE'),
            'MARUTI': ('10999', 'NSE'),
            'AXISBANK': ('5900', 'NSE'),
            'TATASTEEL': ('3499', 'NSE'),
            'SUNPHARMA': ('3351', 'NSE'),
            'TITAN': ('3506', 'NSE'),
            'BAJAJFINSV': ('16675', 'NSE'),
            'WIPRO': ('3787', 'NSE'),
            'HCLTECH': ('7229', 'NSE'),
            'ULTRACEMCO': ('11532', 'NSE'),
            'NTPC': ('11630', 'NSE'),
            'POWERGRID': ('14977', 'NSE'),
            'TATAMOTORS': ('3456', 'NSE'),
            'M&M': ('519', 'NSE'),
            'TECHM': ('13538', 'NSE'),
            'ADANIPORTS': ('15083', 'NSE'),
            'GRASIM': ('1232', 'NSE'),
            'DRREDDY': ('881', 'NSE'),
            'INDUSINDBK': ('5258', 'NSE'),
            'NESTLEIND': ('17963', 'NSE'),
            'COALINDIA': ('20374', 'NSE'),
            'HINDALCO': ('1363', 'NSE'),
            'ONGC': ('2475', 'NSE'),
            'SBILIFE': ('21808', 'NSE'),
            'BRITANNIA': ('140', 'NSE'),
            'DIVISLAB': ('10940', 'NSE'),
            'CIPLA': ('694', 'NSE'),
            'EICHERMOT': ('9541', 'NSE'),
            'HEROMOTOCO': ('1348', 'NSE'),
            'JSWSTEEL': ('11723', 'NSE'),
            'BAJAJ-AUTO': ('16669', 'NSE'),
            'SHREECEM': ('13379', 'NSE'),
            'UPL': ('11287', 'NSE'),
            'IOC': ('1624', 'NSE'),
            'BPCL': ('526', 'NSE'),
            'TATACONSUM': ('3432', 'NSE'),
            'HDFCLIFE': ('119', 'NSE'),
            'IDEA': ('14366', 'NSE'),
            'JINDALSTEL': ('1723', 'NSE'),
            'DELHIVERY': ('5068', 'NSE'),
            'BEL': ('383', 'NSE'),
            'SOLARINDS': ('24209', 'NSE'),
            'INDHOTEL': ('18096', 'NSE'),
            'CUMMINSIND': ('1901', 'NSE'),
            'SIEMENS': ('3150', 'NSE'),
            'AUBANK': ('21238', 'NSE'),
            'APOLLOTYRE': ('163', 'NSE'),
            'NMDC': ('15332', 'NSE'),
            'GODREJPROP': ('17875', 'NSE'),
            'ASHOKLEY': ('212', 'NSE'),
            'GODREJCP': ('1232', 'NSE'),
            'ICICIGI': ('21770', 'NSE')
        }

        # Initialize base prices for simulation (approximate current market prices)
        self.base_prices = {
            'RELIANCE': 2500.0,
            'HDFCBANK': 1600.0,
            'INFY': 1400.0,
            'TCS': 3500.0,
            'ICICIBANK': 900.0,
            'HDFC': 2700.0,
            'ITC': 450.0,
            'KOTAKBANK': 1800.0,
            'LT': 3200.0,
            'SBIN': 600.0,
            'BHARTIARTL': 800.0,
            'HINDUNILVR': 2400.0,
            'BAJFINANCE': 7000.0,
            'ASIANPAINT': 3200.0,
            'MARUTI': 10000.0,
            'AXISBANK': 1000.0,
            'TATASTEEL': 120.0,
            'SUNPHARMA': 1100.0,
            'TITAN': 3200.0,
            'BAJAJFINSV': 1600.0,
            'WIPRO': 400.0,
            'HCLTECH': 1200.0,
            'ULTRACEMCO': 8000.0,
            'NTPC': 350.0,
            'POWERGRID': 250.0,
            'TATAMOTORS': 800.0,
            'M&M': 1800.0,
            'TECHM': 1600.0,
            'ADANIPORTS': 750.0,
            'GRASIM': 2400.0,
            'DRREDDY': 1200.0,
            'INDUSINDBK': 1400.0,
            'NESTLEIND': 22000.0,
            'COALINDIA': 400.0,
            'HINDALCO': 500.0,
            'ONGC': 250.0,
            'SBILIFE': 1400.0,
            'BRITANNIA': 4800.0,
            'DIVISLAB': 5500.0,
            'CIPLA': 1400.0,
            'EICHERMOT': 4800.0,
            'HEROMOTOCO': 4500.0,
            'JSWSTEEL': 900.0,
            'BAJAJ-AUTO': 9000.0,
            'SHREECEM': 24000.0,
            'UPL': 550.0,
            'IOC': 130.0,
            'BPCL': 300.0,
            'TATACONSUM': 900.0,
            'HDFCLIFE': 650.0,
            'IDEA': 12.0,
            'JINDALSTEL': 900.0,
            'DELHIVERY': 350.0,
            'BEL': 280.0,
            'SOLARINDS': 6000.0,
            'INDHOTEL': 500.0,
            'CUMMINSIND': 3200.0,
            'SIEMENS': 6500.0,
            'AUBANK': 600.0,
            'APOLLOTYRE': 480.0,
            'NMDC': 200.0,
            'GODREJPROP': 2700.0,
            'ASHOKLEY': 200.0,
            'GODREJCP': 1200.0,
            'ICICIGI': 1400.0
        }

    def get_last_price(self, symbol: str) -> Optional[float]:
        """Get simulated last price for a symbol"""
        if symbol not in self.base_prices:
            self.logger.warning(f"Symbol {symbol} not found in base prices")
            return None

        # Generate a slightly random price around the base price
        base_price = self.base_prices[symbol]
        variation = random.uniform(-0.02, 0.02)  # ±2% variation
        current_price = base_price * (1 + variation)

        self.price_cache[symbol] = current_price
        return current_price

    def get_historical_data(self, symbol: str, timeframe: str, days: int = 3) -> List[Candle]:
        """Generate simulated historical data"""
        if symbol not in self.base_prices:
            return []

        # Check if we already have historical data for this symbol
        cache_key = f"{symbol}_{timeframe}_{days}"
        if cache_key in self.historical_data:
            return self.historical_data[cache_key]

        candles = []
        base_price = self.base_prices[symbol]
        current_price = base_price

        # Calculate number of candles based on timeframe
        if timeframe == "5min":
            candles_per_day = 75  # 6.25 hours * 12 candles per hour
        else:  # 15min
            candles_per_day = 25   # 6.25 hours * 4 candles per hour

        total_candles = days * candles_per_day

        # Generate historical candles
        start_time = datetime.now() - timedelta(days=days)

        for i in range(total_candles):
            # Calculate timestamp
            if timeframe == "5min":
                timestamp = start_time + timedelta(minutes=i * 5)
            else:
                timestamp = start_time + timedelta(minutes=i * 15)

            # Skip weekends
            if timestamp.weekday() >= 5:
                continue

            # Skip non-market hours (before 9:15 AM or after 3:30 PM)
            if timestamp.hour < 9 or (timestamp.hour == 9 and timestamp.minute < 15):
                continue
            if timestamp.hour > 15 or (timestamp.hour == 15 and timestamp.minute > 30):
                continue

            # Generate OHLCV data
            open_price = current_price

            # Random price movement
            price_change = random.uniform(-0.01, 0.01)  # ±1% per candle
            close_price = open_price * (1 + price_change)

            # High and low based on open and close
            high_price = max(open_price, close_price) * (1 + random.uniform(0, 0.005))
            low_price = min(open_price, close_price) * (1 - random.uniform(0, 0.005))

            # Random volume
            volume = random.uniform(10000, 100000)

            candle = Candle(
                timestamp=timestamp,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=volume
            )

            candles.append(candle)
            current_price = close_price

        # Cache the generated data
        self.historical_data[cache_key] = candles

        self.logger.info(f"Generated {len(candles)} historical candles for {symbol}")
        return candles

    def get_symbol_token(self, symbol: str) -> Optional[Tuple[str, str]]:
        """Get token and exchange for a symbol using centralized mapping"""
        try:
            # Try centralized mapping first
            token_info = self.mapping_client.get_smartapi_token(symbol, 'EQ')
            if token_info:
                return token_info

            # Fallback to legacy mapping
            legacy_result = self._legacy_symbol_mapping.get(symbol)
            if legacy_result:
                self.logger.debug(f"Using legacy mapping for {symbol}: {legacy_result}")
                return legacy_result

            self.logger.warning(f"No token mapping found for symbol: {symbol}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting token for {symbol}: {e}")
            # Fallback to legacy mapping on error
            return self._legacy_symbol_mapping.get(symbol)

    def add_symbol_mapping(self, symbol: str, token: str, exchange: str, base_price: float = None):
        """Add a new symbol mapping to legacy mapping (for backward compatibility)"""
        self._legacy_symbol_mapping[symbol] = (token, exchange)
        if base_price:
            self.base_prices[symbol] = base_price
        self.logger.info(f"Added legacy symbol mapping: {symbol} -> ({token}, {exchange})")

    def update_historical_data(self, symbol: str, candles: List[Candle]):
        """Update historical data for a symbol"""
        cache_key = f"{symbol}_updated"
        self.historical_data[cache_key] = candles
        self.logger.info(f"Updated historical data for {symbol} with {len(candles)} candles")


class RealMarketDataService(MarketDataServiceInterface):
    """Real market data service using SmartAPI"""

    def __init__(self, smart_api, logger: logging.Logger):
        self.smart_api = smart_api
        self.logger = logger

        # Initialize centralized mapping client
        self.mapping_client = CentralizedMappingClient(logger=logger)

        # Legacy symbol mapping for fallback (will be removed gradually)
        self._legacy_symbol_mapping = {
            'RELIANCE': ('2885', 'NSE'),
            'HDFCBANK': ('1333', 'NSE'),
            'INFY': ('1594', 'NSE'),
            'TCS': ('11536', 'NSE'),
            'ICICIBANK': ('4963', 'NSE'),
            'HDFC': ('1330', 'NSE'),
            'ITC': ('424', 'NSE'),
            'KOTAKBANK': ('492', 'NSE'),
            'LT': ('11483', 'NSE'),
            'SBIN': ('3045', 'NSE'),
            'BHARTIARTL': ('10604', 'NSE'),
            'HINDUNILVR': ('1394', 'NSE'),
            'BAJFINANCE': ('317', 'NSE'),
            'ASIANPAINT': ('236', 'NSE'),
            'MARUTI': ('10999', 'NSE'),
            'AXISBANK': ('5900', 'NSE'),
            'TATASTEEL': ('3499', 'NSE'),
            'SUNPHARMA': ('3351', 'NSE'),
            'TITAN': ('3506', 'NSE'),
            'BAJAJFINSV': ('16675', 'NSE'),
            'WIPRO': ('3787', 'NSE'),
            'HCLTECH': ('7229', 'NSE'),
            'ULTRACEMCO': ('11532', 'NSE'),
            # Additional symbols from stocks_to_monitor.csv
            'SIEMENS': ('3150', 'NSE'),
            'AUBANK': ('21238', 'NSE'),
            'APOLLOTYRE': ('163', 'NSE'),
            'NMDC': ('15332', 'NSE'),
            'GODREJPROP': ('17875', 'NSE'),
            'ASHOKLEY': ('212', 'NSE'),
            'GODREJCP': ('1232', 'NSE'),
            'ICICIGI': ('21770', 'NSE'),
            'M&M': ('519', 'NSE'),  # Mahindra & Mahindra
            'TATAMOTORS': ('3456', 'NSE'),
            'NESTLEIND': ('17963', 'NSE'),
            'INDHOTEL': ('1512', 'NSE'),
            'CUMMINSIND': ('1901', 'NSE'),
            'TECHM': ('13538', 'NSE'),
            'NTPC': ('11630', 'NSE'),
            'POWERGRID': ('14977', 'NSE'),
            'ADANIPORTS': ('15083', 'NSE'),
            'GRASIM': ('1232', 'NSE'),
            'DRREDDY': ('881', 'NSE'),
            'INDUSINDBK': ('5258', 'NSE'),
            'NESTLEIND': ('17963', 'NSE'),
            'COALINDIA': ('20374', 'NSE'),
            'HINDALCO': ('1363', 'NSE'),
            'ONGC': ('2475', 'NSE'),
            'SBILIFE': ('21808', 'NSE'),
            'BRITANNIA': ('140', 'NSE'),
            'DIVISLAB': ('10940', 'NSE'),
            'CIPLA': ('694', 'NSE'),
            'EICHERMOT': ('9541', 'NSE'),
            'HEROMOTOCO': ('1348', 'NSE'),
            'JSWSTEEL': ('11723', 'NSE'),
            'BAJAJ-AUTO': ('16669', 'NSE'),
            'SHREECEM': ('13379', 'NSE'),
            'UPL': ('11287', 'NSE'),
            'IOC': ('1624', 'NSE'),
            'BPCL': ('526', 'NSE'),
            'TATACONSUM': ('3432', 'NSE'),
            'HDFCLIFE': ('119', 'NSE'),
            'IDEA': ('14366', 'NSE'),
            'JINDALSTEL': ('1723', 'NSE'),
            'DELHIVERY': ('5068', 'NSE'),
            'BEL': ('383', 'NSE'),
            'SOLARINDS': ('24209', 'NSE'),
            'INDHOTEL': ('18096', 'NSE'),
            'CUMMINSIND': ('1901', 'NSE'),
            'SIEMENS': ('3150', 'NSE'),
            'AUBANK': ('21238', 'NSE'),
            'APOLLOTYRE': ('163', 'NSE'),
            'NMDC': ('15332', 'NSE'),
            'GODREJPROP': ('17875', 'NSE'),
            'ASHOKLEY': ('212', 'NSE'),
            'GODREJCP': ('1232', 'NSE'),
            'ICICIGI': ('21770', 'NSE')
        }

    def get_last_price(self, symbol: str) -> Optional[float]:
        """Get real last price from SmartAPI"""
        try:
            token_info = self.get_symbol_token(symbol)
            if not token_info:
                self.logger.error(f"No token found for symbol: {symbol}")
                return None

            token, exchange = token_info

            # Get LTP data from SmartAPI with correct symbol format
            # SmartAPI requires trading symbol with -EQ suffix for equity
            trading_symbol = f"{symbol}-EQ" if exchange == "NSE" else symbol
            response = self.smart_api.ltpData(exchange, trading_symbol, token)

            if response and response.get('status') and response.get('data'):
                ltp = response['data'].get('ltp')
                if ltp:
                    return float(ltp)

            self.logger.warning(f"No LTP data received for {symbol}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting last price for {symbol}: {e}")
            return None

    def get_historical_data(self, symbol: str, timeframe: str, days: int = 3) -> List[Candle]:
        """Get real historical data from SmartAPI"""
        try:
            token_info = self.get_symbol_token(symbol)
            if not token_info:
                self.logger.error(f"No token found for symbol: {symbol}")
                return []

            token, exchange = token_info

            # Calculate date range
            to_date = datetime.now()
            from_date = to_date - timedelta(days=days)

            # Format dates for SmartAPI
            from_date_str = from_date.strftime("%Y-%m-%d %H:%M")
            to_date_str = to_date.strftime("%Y-%m-%d %H:%M")

            # Map timeframe to SmartAPI format
            interval_map = {
                "5min": "FIVE_MINUTE",
                "15min": "FIFTEEN_MINUTE",
                "1hour": "ONE_HOUR",
                "1day": "ONE_DAY"
            }

            interval = interval_map.get(timeframe, "FIFTEEN_MINUTE")

            # Get historical data from SmartAPI
            response = self.smart_api.getCandleData({
                "exchange": exchange,
                "symboltoken": token,
                "interval": interval,
                "fromdate": from_date_str,
                "todate": to_date_str
            })

            if not response or not response.get('status') or not response.get('data'):
                self.logger.warning(f"No historical data received for {symbol}")
                return []

            # Convert SmartAPI data to Candle objects
            candles = []
            for data_point in response['data']:
                try:
                    # SmartAPI returns: [timestamp, open, high, low, close, volume]
                    timestamp_str = data_point[0]
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S%z")

                    candle = Candle(
                        timestamp=timestamp.replace(tzinfo=None),  # Remove timezone for consistency
                        open=float(data_point[1]),
                        high=float(data_point[2]),
                        low=float(data_point[3]),
                        close=float(data_point[4]),
                        volume=float(data_point[5])
                    )
                    candles.append(candle)

                except Exception as e:
                    self.logger.warning(f"Error parsing candle data: {e}")
                    continue

            self.logger.info(f"Retrieved {len(candles)} historical candles for {symbol}")
            return candles

        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {e}")
            return []

    def get_symbol_token(self, symbol: str) -> Optional[Tuple[str, str]]:
        """Get token and exchange for a symbol using centralized mapping"""
        try:
            # Try centralized mapping first
            token_info = self.mapping_client.get_smartapi_token(symbol, 'EQ')
            if token_info:
                return token_info

            # Fallback to legacy mapping
            legacy_result = self._legacy_symbol_mapping.get(symbol)
            if legacy_result:
                self.logger.debug(f"Using legacy mapping for {symbol}: {legacy_result}")
                return legacy_result

            self.logger.warning(f"No token mapping found for symbol: {symbol}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting token for {symbol}: {e}")
            # Fallback to legacy mapping on error
            return self._legacy_symbol_mapping.get(symbol)
