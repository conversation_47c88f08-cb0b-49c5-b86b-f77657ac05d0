# Python Orders - Trading System with SmartAPI Integration

This Python trading system implements the strategies from the Golang codebase using the SmartAPI Python library for order placement with Angel One.

## Features

- **MA Crossover Strategy**: 9 EMA crossing 20 EMA with RSI filter and trend confirmation
- **Support/Resistance Strategy**: Bounce/rejection at key support and resistance levels
- **SmartAPI Integration**: Real order placement using Angel One's SmartAPI
- **Hybrid Data Service**: Combines MongoDB historical data with SmartAPI live data
- **Technical Indicators**: EMA, RSI, Support/Resistance levels, Volume analysis with real-time calculation
- **Paper Trading & Real Trading**: Both simulated trading for testing and real money trading with Angel One
- **Risk Management**: Quantity calculation using 800/(entry-SL) formula with 4x margin
- **Market Hours Check**: Uses `is_market_open()` function as preferred by user
- **Data Caching**: Intelligent caching for performance optimization
- **MongoDB Integration**: Historical data storage and retrieval

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Copy the environment configuration:
```bash
cp .env.example .env
```

3. Edit `.env` file with your SmartAPI credentials:
```
SMARTAPI_API_KEY=your_api_key_here
SMARTAPI_USERNAME=your_username_here
SMARTAPI_PASSWORD=your_password_here
SMARTAPI_TOTP_TOKEN=your_totp_token_here
```

## Configuration

### SmartAPI Setup
1. Get your API key from Angel One SmartAPI portal
2. Enable TOTP (Time-based One-Time Password) in your Angel One account
3. Get your TOTP secret key for authentication

### Trading Configuration
- `MAX_TRADES_PER_DAY`: Maximum number of trades per day (default: 3)
- `MAX_RISK_PER_TRADE`: Base amount for quantity calculation (default: 800)
- `MARGIN_MULTIPLIER`: Margin multiplier for quantity (default: 4x)
- `PAPER_TRADING`: Enable/disable paper trading (default: true)
- `REAL_TRADING`: Enable real trading with live money (default: false)
- `RISK_REWARD_RATIO`: Risk-reward ratio for targets (default: 3.0)

⚠️ **Important**: Set `REAL_TRADING=true` to trade with real money. See [Real Trading Guide](REAL_TRADING_GUIDE.md) for details.

### Technical Indicators
- `EMA_FAST_PERIOD`: Fast EMA period (default: 9)
- `EMA_SLOW_PERIOD`: Slow EMA period (default: 20)
- `EMA_TREND_PERIOD`: Trend EMA period (default: 50)
- `RSI_PERIOD`: RSI calculation period (default: 14)
- `RSI_OVERSOLD`: RSI oversold level (default: 30)
- `RSI_OVERBOUGHT`: RSI overbought level (default: 70)

## Market Data Services

The system supports three types of market data services:

### 1. Simulated Data Service (Default)
- Uses simulated market data for testing
- No external dependencies
- Perfect for development and testing

### 2. Real SmartAPI Service
- Fetches live data directly from SmartAPI
- Requires SmartAPI credentials
- No historical data persistence

### 3. Hybrid Service (Recommended for Production)
- **Combines MongoDB historical data with SmartAPI live data**
- **Automatically calculates technical indicators on combined dataset**
- **Intelligent data gap filling**
- **Performance optimized with caching**

#### Hybrid Service Features:
- 📊 **Historical Data**: Stored in MongoDB for fast access
- 🔴 **Live Data**: Real-time prices from SmartAPI
- 📈 **Technical Indicators**: Calculated on combined historical + live data
- ⚡ **Smart Caching**: Reduces API calls and improves performance
- 🔄 **Auto Gap Filling**: Identifies and fills missing data automatically
- 💾 **Data Persistence**: New data automatically stored to MongoDB

#### Configuration:
```bash
# Set in .env file
MARKET_DATA_SERVICE=hybrid  # Options: simulated, real, hybrid
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE_NAME=trading_db
```

## Usage

### Paper Trading (Default - Safe)
```bash
# Uses virtual money for testing
python main.py
```

### Real Trading (Live Money)
```bash
# 1. Test your SmartAPI connection first
python test_smartapi_connection.py

# 2. Set REAL_TRADING=true in .env file
# 3. Run the system with real money
python main.py
```

⚠️ **Warning**: Real trading uses actual money from your Angel One account. See [Real Trading Guide](REAL_TRADING_GUIDE.md) for complete setup instructions.

### With Hybrid Data Service
```bash
# 1. Install and start MongoDB
# 2. Set MARKET_DATA_SERVICE=hybrid in .env
# 3. Configure SmartAPI credentials
python main.py
```

### With Custom Signals
Create CSV files with stock symbols:
- `buy_signals.csv` - Symbols for buy signals
- `sell_signals.csv` - Symbols for sell signals

### Programmatic Usage
```python
from models.signal import create_buy_signals, SignalType
from services.order_service import SmartAPIOrderService, PaperTradingOrderService
from services.market_data_service import SimulatedMarketDataService
from strategies.strategy_manager import StrategyManager, StrategyType
from config.config import config

# Create services
market_data_service = SimulatedMarketDataService(logger)
order_service = PaperTradingOrderService(logger)

# Create strategy manager
strategy_manager = StrategyManager(
    logger=logger,
    market_data_service=market_data_service,
    order_service=order_service,
    config=config.trading
)

# Create signals
signals = create_buy_signals(['RELIANCE', 'TCS', 'HDFCBANK'])

# Process signals
orders = strategy_manager.process_signals_batch(signals)
```

## Strategies

### MA Crossover Strategy
- **Entry Conditions**:
  - 9 EMA crosses above/below 20 EMA
  - Price above/below 50 EMA (trend filter)
  - RSI above/below 50 (momentum filter)
  - Volume above average (confirmation)

- **Risk Management**:
  - Stop-loss: Below/above recent swing low/high
  - Target: 3x risk-reward ratio
  - Quantity: 800/(entry-SL) * 4x margin

### Support/Resistance Strategy
- **Entry Conditions**:
  - Price bounces off support level (bullish)
  - Price rejects at resistance level (bearish)
  - RSI oversold/overbought confirmation
  - Volume above average (confirmation)

- **Risk Management**:
  - Stop-loss: 1% below support / 1% above resistance
  - Target: 3x risk-reward ratio
  - Quantity: 800/(entry-SL) * 4x margin

## File Structure

```
python_orders/
├── models/
│   ├── candle.py          # Candle data model
│   ├── order.py           # Order and Position models
│   └── signal.py          # Signal model and CSV reading
├── services/
│   ├── order_service.py   # SmartAPI and paper trading services
│   └── market_data_service.py  # Market data providers
├── strategies/
│   ├── ma_crossover_strategy.py      # MA Crossover implementation
│   ├── support_resistance_strategy.py # Support/Resistance implementation
│   └── strategy_manager.py           # Strategy coordination
├── utils/
│   └── helpers.py         # Helper functions and indicators
├── config/
│   └── config.py          # Configuration management
├── main.py               # Main execution script
├── requirements.txt      # Python dependencies
├── .env.example         # Environment configuration template
└── README.md            # This file
```

## SmartAPI Order Placement

The system uses the SmartAPI Python library with the following syntax:

```python
order_params = {
    "variety": "NORMAL",
    "tradingsymbol": f"{symbol}-EQ",
    "symboltoken": token,
    "transactiontype": "BUY",  # or "SELL"
    "exchange": "NSE",
    "ordertype": "MARKET",
    "producttype": "INTRADAY",
    "duration": "DAY",
    "price": "0",  # Market order
    "squareoff": "0",
    "stoploss": "0",
    "quantity": str(quantity)
}

response = smart_api.placeOrderFullResponse(order_params)
```

## Testing

### Paper Trading (Virtual Money)
Set `PAPER_TRADING=true` in your `.env` file to enable paper trading mode. This will simulate order placement without actual trades.

### Real Trading (Live Money)
1. **Test Connection**: Run `python test_smartapi_connection.py` to verify your SmartAPI setup
2. **Configure**: Set `REAL_TRADING=true` in your `.env` file
3. **Start Small**: Begin with smaller position sizes
4. **Monitor**: Watch your trades closely during market hours

📖 **Complete Guide**: See [REAL_TRADING_GUIDE.md](REAL_TRADING_GUIDE.md) for detailed instructions on switching to real trading.

### Market Hours Override
For testing outside market hours, set:
```
OVERRIDE_MARKET_OPEN=true
OVERRIDE_ENTRY_WINDOW=true
```

### Sample Data
The system includes simulated market data for testing with common Indian stocks like RELIANCE, TCS, HDFCBANK, etc.

## Risk Management

- **Position Sizing**: Uses 800/(entry-SL) formula with 4x margin
- **Stop Loss**: Automatic stop-loss calculation based on technical levels
- **Risk-Reward**: 3:1 risk-reward ratio for all trades
- **Daily Limits**: Maximum 3 trades per day (configurable)
- **Stock Limits**: Each stock can only be traded once per day

## Logging

The system provides comprehensive logging:
- Order placement details
- Technical indicator values
- Entry/exit conditions
- Error handling and warnings

## Error Handling

- Authentication failures with SmartAPI
- Network connectivity issues
- Invalid symbol tokens
- Insufficient market data
- Order placement failures

## Future Enhancements

- Real-time market data integration
- WebSocket support for live price feeds
- Position monitoring and exit management
- Advanced risk management features
- Backtesting capabilities
- Performance analytics and reporting

## Support

For issues related to:
- SmartAPI: Check Angel One SmartAPI documentation
- Strategy logic: Refer to the original Golang implementation
- Configuration: Review the `.env.example` file
