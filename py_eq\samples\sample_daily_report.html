<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Trading Report - 2025-01-06</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        header h2 {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .label {
            font-weight: 600;
            color: #7f8c8d;
        }
        
        .value {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
        }
        
        .positive {
            color: #27ae60 !important;
        }
        
        .negative {
            color: #e74c3c !important;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: white;
        }
        
        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            height: 400px;
        }
        
        .trades-section {
            padding: 30px;
            background: white;
        }
        
        .trades-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .trades-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }
        
        .trades-table th {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .trades-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .trades-table tr:hover {
            background: #f8f9fa;
        }
        
        .trades-table tr.win {
            border-left: 4px solid #27ae60;
        }
        
        .trades-table tr.loss {
            border-left: 4px solid #e74c3c;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📊 Daily Trading Report</h1>
            <h2>Monday, January 06, 2025</h2>
        </header>
        
        <div class="summary-cards">
            <div class="card">
                <h3>💰 Balance Summary</h3>
                <div class="metric">
                    <span class="label">Opening Balance:</span>
                    <span class="value">₹320,000.00</span>
                </div>
                <div class="metric">
                    <span class="label">Closing Balance:</span>
                    <span class="value positive">₹322,450.00</span>
                </div>
                <div class="metric">
                    <span class="label">Daily P&L:</span>
                    <span class="value positive">₹2,450.00</span>
                </div>
            </div>
            
            <div class="card">
                <h3>📈 Trading Performance</h3>
                <div class="metric">
                    <span class="label">Total Trades:</span>
                    <span class="value">3</span>
                </div>
                <div class="metric">
                    <span class="label">Win Rate:</span>
                    <span class="value">66.7%</span>
                </div>
                <div class="metric">
                    <span class="label">Profit Factor:</span>
                    <span class="value">2.45</span>
                </div>
            </div>
            
            <div class="card">
                <h3>🎯 Risk Metrics</h3>
                <div class="metric">
                    <span class="label">Largest Win:</span>
                    <span class="value positive">₹1,850.00</span>
                </div>
                <div class="metric">
                    <span class="label">Largest Loss:</span>
                    <span class="value negative">₹1,200.00</span>
                </div>
                <div class="metric">
                    <span class="label">Expectancy:</span>
                    <span class="value">₹816.67</span>
                </div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="chart-container">
                <canvas id="pnlChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="strategyChart"></canvas>
            </div>
        </div>
        
        <div class="trades-section">
            <h3>📋 Today's Trades</h3>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Symbol</th>
                            <th>Strategy</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Quantity</th>
                            <th>Duration</th>
                            <th>P&L</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="win">
                            <td>09:45:23</td>
                            <td>RELIANCE</td>
                            <td>MA_CROSSOVER</td>
                            <td>₹2,450.00</td>
                            <td>₹2,485.50</td>
                            <td>50</td>
                            <td>45m</td>
                            <td class="positive">₹1,775.00</td>
                            <td>✅ Win</td>
                        </tr>
                        <tr class="loss">
                            <td>11:20:15</td>
                            <td>TCS</td>
                            <td>SUPPORT_RESISTANCE</td>
                            <td>₹3,890.00</td>
                            <td>₹3,860.00</td>
                            <td>40</td>
                            <td>32m</td>
                            <td class="negative">₹1,200.00</td>
                            <td>❌ Loss</td>
                        </tr>
                        <tr class="win">
                            <td>14:15:42</td>
                            <td>HDFC</td>
                            <td>GAP_AND_GO</td>
                            <td>₹1,680.00</td>
                            <td>₹1,705.50</td>
                            <td>75</td>
                            <td>28m</td>
                            <td class="positive">₹1,912.50</td>
                            <td>✅ Win</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <footer>
            <p>Generated on 2025-01-06 15:30:45 | Trading System v2.0</p>
        </footer>
    </div>
    
    <script>
        // P&L Chart
        const pnlCtx = document.getElementById('pnlChart').getContext('2d');
        new Chart(pnlCtx, {
            type: 'bar',
            data: {
                labels: ['Gross Profit', 'Gross Loss', 'Net P&L'],
                datasets: [{
                    label: 'Amount (₹)',
                    data: [3687.5, -1200, 2487.5],
                    backgroundColor: ['#4CAF50', '#f44336', '#4CAF50'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Daily P&L Breakdown'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Strategy Performance Chart
        const strategyCtx = document.getElementById('strategyChart').getContext('2d');
        new Chart(strategyCtx, {
            type: 'doughnut',
            data: {
                labels: ['MA_CROSSOVER', 'SUPPORT_RESISTANCE', 'GAP_AND_GO'],
                datasets: [{
                    label: 'P&L (₹)',
                    data: [1775, -1200, 1912.5],
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Strategy Performance'
                    }
                }
            }
        });
    </script>
</body>
</html>
