import logging
import os
from dotenv import load_dotenv
from SmartApi import SmartConnect
import pyotp

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Retrieve credentials from environment variables
api_key = os.getenv('SMARTAPI_API_KEY')
client_code = os.getenv('SMARTAPI_USERNAME')
pin = os.getenv('SMARTAPI_PASSWORD')
totp_secret = os.getenv('SMARTAPI_TOTP_TOKEN')

# Validate environment variables
if not all([api_key, client_code, pin, totp_secret]):
    logger.error("Missing required environment variables in .env file")
    exit(1)

# Initialize SmartConnect with API key
smartApi = SmartConnect(api_key)

try:
    # Generate TOTP for authentication
    totp = pyotp.TOTP(totp_secret).now()

    # Generate session (login)
    data = smartApi.generateSession(client_code, pin, totp)
    if data['status'] is False:
        logger.error(f"Login failed: {data['message']}")
        exit(1)
    logger.info("Successfully authenticated with SmartAPI")

    # Fetch current positions
    positions = smartApi.position()
    if positions['status'] is False:
        logger.error(f"Failed to fetch positions: {positions['message']}")
        exit(1)

    # Display open positions details
    print("Open Positions Details:")
    print("-----------------------")
    if not positions['data']:
        print("No open positions.")
    else:
        for pos in positions['data']:
            if pos.get('netqty', '0') != '0':  # Filter for open positions (non-zero net quantity)
                try:
                    stock_name = pos.get('tradingsymbol', 'UNKNOWN')
                    qty = int(pos.get('netqty', 0))  # Convert netqty to integer
                    atp = float(pos['buyavgprice']) if qty > 0 else float(pos['sellavgprice'])  # ATP based on position type
                    # Try to get unrealized P/L; fallback to manual calculation
                    try:
                        pnl = float(pos.get('unrealised', 0))
                    except (TypeError, ValueError):
                        # Calculate P/L manually: (LTP - ATP) * Quantity for long, (ATP - LTP) * Quantity for short
                        ltp = float(pos.get('ltp', 0))
                        if ltp == 0:
                            logger.warning(f"No LTP available for {stock_name}, P/L set to 0")
                            pnl = 0.0
                        else:
                            pnl = (ltp - atp) * qty if qty > 0 else (atp - ltp) * qty
                    logger.info(f"Processed position: {stock_name}, Qty: {qty}, ATP: {atp:.2f}, P/L: {pnl:.2f}")
                    print(f"Stock: {stock_name}, Qty: {qty}, ATP: {atp:.2f}, P/L: {pnl:.2f}")
                except (KeyError, ValueError) as e:
                    logger.error(f"Error processing position for {pos.get('tradingsymbol', 'UNKNOWN')}: {e}")
                    continue

except Exception as e:
    logger.error(f"Unexpected error: {e}")
    exit(1)

finally:
    # Logout to clean up session
    try:
        smartApi.terminateSession(client_code)
        logger.info("Successfully logged out from SmartAPI")
    except Exception as e:
        logger.error(f"Error during logout: {e}")