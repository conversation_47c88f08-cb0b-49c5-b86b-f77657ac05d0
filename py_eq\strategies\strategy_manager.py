"""
Strategy Manager for coordinating multiple trading strategies
Based on the Golang implementation
"""
import logging
from enum import Enum
from typing import Dict, List, Optional, Set
from threading import Lock

from models.signal import Signal
from models.order import Order
from services.order_service import OrderServiceInterface
from services.market_data_service import MarketDataServiceInterface
from config.config import TradingConfig
from strategies.ma_crossover_strategy import MACrossoverStrategy
from strategies.support_resistance_strategy import SupportResistanceStrategy


class StrategyType(Enum):
    """Strategy type enumeration"""
    MA_CROSSOVER = "MA_CROSSOVER"
    SUPPORT_RESISTANCE = "SUPPORT_RESISTANCE"


class StrategyManager:
    """Manages multiple trading strategies"""

    def __init__(
        self,
        logger: logging.Logger,
        market_data_service: MarketDataServiceInterface,
        order_service: OrderServiceInterface,
        config: TradingConfig,
        trading_logger=None
    ):
        self.logger = logger
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config
        self.trading_logger = trading_logger

        # Initialize strategies
        self.ma_crossover_strategy = MACrossoverStrategy(
            logger, market_data_service, order_service, config
        )

        self.support_resistance_strategy = SupportResistanceStrategy(
            logger, market_data_service, order_service, config
        )

        # Track traded stocks to prevent the same stock from being traded in both strategies
        self.traded_stocks: Set[str] = set()
        self.traded_stocks_lock = Lock()

        # Track trades count
        self.trades_count = 0
        self.trades_lock = Lock()

        # Track orders
        self.orders: List[Order] = []
        self.orders_lock = Lock()

    def process_signal(self, signal: Signal, strategy_type: StrategyType) -> Optional[Order]:
        """
        Process a signal using the specified strategy

        Args:
            signal: Trading signal to process
            strategy_type: Type of strategy to use

        Returns:
            Order object if successful, None otherwise
        """
        # Check if we've reached the maximum number of trades for the day
        with self.trades_lock:
            if self.trades_count >= self.config.max_trades_per_day:
                self.logger.warning(
                    f"Maximum number of trades for the day ({self.config.max_trades_per_day}) reached"
                )
                return None

        # Check if this stock has already been traded
        with self.traded_stocks_lock:
            if signal.symbol in self.traded_stocks:
                self.logger.info(f"Skipping {signal.symbol} as it has already been traded today")
                return None

        # Process signal with the specified strategy
        order = None
        try:
            if strategy_type == StrategyType.MA_CROSSOVER:
                order = self.ma_crossover_strategy.process_signal(signal)
            elif strategy_type == StrategyType.SUPPORT_RESISTANCE:
                order = self.support_resistance_strategy.process_signal(signal)
            else:
                self.logger.error(f"Unknown strategy type: {strategy_type}")
                return None

        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.symbol} with {strategy_type.value}: {e}")
            return None

        # If the signal was processed successfully, mark the stock as traded
        if order:
            with self.traded_stocks_lock:
                self.traded_stocks.add(signal.symbol)

            with self.trades_lock:
                self.trades_count += 1

            with self.orders_lock:
                self.orders.append(order)

            # Log to trading logger
            if self.trading_logger:
                self.trading_logger.log_order_placed(
                    order=order,
                    strategy=strategy_type.value,
                    notes=f"Processed via {strategy_type.value} strategy"
                )

            self.logger.info(
                f"Marked {signal.symbol} as traded. Total trades today: "
                f"{self.trades_count}/{self.config.max_trades_per_day}"
            )

        return order

    def process_signal_with_all_strategies(self, signal: Signal) -> Optional[Order]:
        """
        Process a signal using all available strategies (try MA Crossover first, then Support/Resistance)

        Args:
            signal: Trading signal to process

        Returns:
            Order object if successful, None otherwise
        """
        # Check if we've reached the maximum number of trades for the day
        with self.trades_lock:
            if self.trades_count >= self.config.max_trades_per_day:
                self.logger.warning(
                    f"Maximum number of trades for the day ({self.config.max_trades_per_day}) reached"
                )
                return None

        # Check if this stock has already been traded
        with self.traded_stocks_lock:
            if signal.symbol in self.traded_stocks:
                self.logger.info(f"Skipping {signal.symbol} as it has already been traded today")
                return None

        # Try MA Crossover Strategy first
        try:
            order = self.ma_crossover_strategy.process_signal(signal)
            if order:
                # MA Crossover Strategy succeeded
                with self.traded_stocks_lock:
                    self.traded_stocks.add(signal.symbol)

                with self.trades_lock:
                    self.trades_count += 1

                with self.orders_lock:
                    self.orders.append(order)

                # Log to trading logger
                if self.trading_logger:
                    self.trading_logger.log_order_placed(
                        order=order,
                        strategy="MA_CROSSOVER",
                        notes=f"Processed via MA Crossover strategy"
                    )

                self.logger.info(
                    f"Processed {signal.symbol} with MA Crossover Strategy. "
                    f"Total trades today: {self.trades_count}/{self.config.max_trades_per_day}"
                )
                return order

        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.symbol} with MA Crossover: {e}")

        # If MA Crossover Strategy failed, try Support/Resistance Strategy
        try:
            order = self.support_resistance_strategy.process_signal(signal)
            if order:
                # Support/Resistance Strategy succeeded
                with self.traded_stocks_lock:
                    self.traded_stocks.add(signal.symbol)

                with self.trades_lock:
                    self.trades_count += 1

                with self.orders_lock:
                    self.orders.append(order)

                # Log to trading logger
                if self.trading_logger:
                    self.trading_logger.log_order_placed(
                        order=order,
                        strategy="SUPPORT_RESISTANCE",
                        notes=f"Processed via Support/Resistance strategy"
                    )

                self.logger.info(
                    f"Processed {signal.symbol} with Support/Resistance Strategy. "
                    f"Total trades today: {self.trades_count}/{self.config.max_trades_per_day}"
                )
                return order

        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.symbol} with Support/Resistance: {e}")

        # Both strategies failed
        self.logger.info(f"Failed to process {signal.symbol} with either strategy")
        return None

    def process_signals_batch(self, signals: List[Signal], strategy_type: Optional[StrategyType] = None) -> List[Order]:
        """
        Process a batch of signals

        Args:
            signals: List of signals to process
            strategy_type: Specific strategy to use, or None to try all strategies

        Returns:
            List of successfully placed orders
        """
        orders = []

        for signal in signals:
            # Check if we've reached the maximum number of trades
            with self.trades_lock:
                if self.trades_count >= self.config.max_trades_per_day:
                    self.logger.info(f"Maximum trades reached. Stopping batch processing.")
                    break

            try:
                if strategy_type:
                    order = self.process_signal(signal, strategy_type)
                else:
                    order = self.process_signal_with_all_strategies(signal)

                if order:
                    orders.append(order)

            except Exception as e:
                self.logger.error(f"Error processing signal {signal.symbol}: {e}")
                continue

        self.logger.info(f"Batch processing completed. Placed {len(orders)} orders out of {len(signals)} signals.")
        return orders

    def get_traded_stocks(self) -> Set[str]:
        """Get the set of stocks that have been traded today"""
        with self.traded_stocks_lock:
            return self.traded_stocks.copy()

    def get_trades_count(self) -> int:
        """Get the current number of trades for the day"""
        with self.trades_lock:
            return self.trades_count

    def get_orders(self) -> List[Order]:
        """Get all orders placed today"""
        with self.orders_lock:
            return self.orders.copy()

    def reset_daily_counters(self):
        """Reset daily counters (call this at the start of each trading day)"""
        with self.traded_stocks_lock:
            self.traded_stocks.clear()

        with self.trades_lock:
            self.trades_count = 0

        with self.orders_lock:
            self.orders.clear()

        self.logger.info("Daily counters reset")

    def get_strategy_statistics(self) -> Dict[str, any]:
        """Get statistics about strategy performance"""
        with self.orders_lock:
            total_orders = len(self.orders)
            buy_orders = sum(1 for order in self.orders if order.is_buy_order)
            sell_orders = sum(1 for order in self.orders if order.is_sell_order)

        with self.trades_lock:
            trades_remaining = self.config.max_trades_per_day - self.trades_count

        return {
            'total_orders': total_orders,
            'buy_orders': buy_orders,
            'sell_orders': sell_orders,
            'trades_count': self.trades_count,
            'max_trades_per_day': self.config.max_trades_per_day,
            'trades_remaining': trades_remaining,
            'traded_stocks_count': len(self.traded_stocks)
        }

    def restore_trading_state(self, traded_stocks: Set[str], trades_count: int):
        """
        Restore trading state from logs after restart/power failure

        Args:
            traded_stocks: Set of symbols already traded today
            trades_count: Number of trades taken today
        """
        with self.traded_stocks_lock:
            self.traded_stocks.update(traded_stocks)

        with self.trades_lock:
            self.trades_count = trades_count

        self.logger.info(f"Trading state restored: {len(traded_stocks)} traded stocks, {trades_count} trades")
