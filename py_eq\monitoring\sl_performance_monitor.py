"""
Stop-Loss Performance Monitor
Real-time monitoring and analysis of SL execution performance
"""
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from collections import deque
import json
import os


@dataclass
class ExecutionMetric:
    """Individual execution performance metric"""
    timestamp: datetime
    symbol: str
    execution_time_ms: float
    slippage_percent: float
    exit_reason: str
    success: bool
    retry_count: int = 0
    priority: str = "NORMAL"


@dataclass
class PerformanceSnapshot:
    """Performance snapshot at a point in time"""
    timestamp: datetime
    total_executions: int
    successful_executions: int
    failed_executions: int
    average_execution_time: float
    average_slippage: float
    fastest_execution: float
    slowest_execution: float
    success_rate: float
    critical_failures: int = 0
    emergency_exits: int = 0


class SLPerformanceMonitor:
    """Monitor and analyze stop-loss execution performance"""
    
    def __init__(self, logger: logging.Logger, max_history: int = 1000):
        self.logger = logger
        self.max_history = max_history
        
        # Performance data storage
        self.execution_history: deque = deque(maxlen=max_history)
        self.performance_snapshots: deque = deque(maxlen=100)  # Keep 100 snapshots
        
        # Real-time metrics
        self.current_metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_execution_time': 0.0,
            'total_slippage': 0.0,
            'fastest_execution': float('inf'),
            'slowest_execution': 0.0,
            'critical_failures': 0,
            'emergency_exits': 0
        }
        
        # Performance thresholds
        self.thresholds = {
            'max_execution_time_ms': 500.0,
            'max_slippage_percent': 0.5,
            'min_success_rate': 95.0,
            'max_critical_failures_per_hour': 5
        }
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread = None
        self._stop_event = threading.Event()
        
        # Alert tracking
        self.last_alert_times = {}
        self.alert_cooldown_seconds = 300  # 5 minutes
        
        self.logger.info("📊 SL Performance Monitor initialized")

    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self._stop_event.clear()
        
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("🔍 SL Performance monitoring started")

    def stop_monitoring(self):
        """Stop performance monitoring"""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        self._stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("⏹️ SL Performance monitoring stopped")

    def record_execution(self, symbol: str, execution_time_ms: float, slippage_percent: float,
                        exit_reason: str, success: bool, retry_count: int = 0, priority: str = "NORMAL"):
        """Record an execution for performance analysis"""
        try:
            # Create execution metric
            metric = ExecutionMetric(
                timestamp=datetime.now(),
                symbol=symbol,
                execution_time_ms=execution_time_ms,
                slippage_percent=slippage_percent,
                exit_reason=exit_reason,
                success=success,
                retry_count=retry_count,
                priority=priority
            )
            
            # Add to history
            self.execution_history.append(metric)
            
            # Update current metrics
            self._update_current_metrics(metric)
            
            # Check for performance issues
            self._check_performance_alerts(metric)
            
            # Log execution details
            status = "✅" if success else "❌"
            self.logger.info(f"{status} SL Execution: {symbol} | {execution_time_ms:.1f}ms | "
                           f"{slippage_percent:.2f}% slippage | {exit_reason} | Priority: {priority}")
            
            if retry_count > 0:
                self.logger.warning(f"⚠️ Required {retry_count} retries for {symbol}")
                
        except Exception as e:
            self.logger.error(f"Error recording execution metric: {e}")

    def _update_current_metrics(self, metric: ExecutionMetric):
        """Update current performance metrics"""
        self.current_metrics['total_executions'] += 1
        
        if metric.success:
            self.current_metrics['successful_executions'] += 1
            self.current_metrics['total_execution_time'] += metric.execution_time_ms
            self.current_metrics['total_slippage'] += metric.slippage_percent
            
            # Update fastest/slowest
            self.current_metrics['fastest_execution'] = min(
                self.current_metrics['fastest_execution'], metric.execution_time_ms
            )
            self.current_metrics['slowest_execution'] = max(
                self.current_metrics['slowest_execution'], metric.execution_time_ms
            )
        else:
            self.current_metrics['failed_executions'] += 1
            
            # Track critical failures
            if metric.priority in ['EMERGENCY', 'CRITICAL']:
                self.current_metrics['critical_failures'] += 1
        
        # Track emergency exits
        if metric.exit_reason in ['EMERGENCY_EXIT', 'EMERGENCY_SQUARE_OFF']:
            self.current_metrics['emergency_exits'] += 1

    def _check_performance_alerts(self, metric: ExecutionMetric):
        """Check for performance issues and generate alerts"""
        alerts = []
        
        # Check execution time
        if metric.execution_time_ms > self.thresholds['max_execution_time_ms']:
            alerts.append(f"Slow execution: {metric.execution_time_ms:.1f}ms > {self.thresholds['max_execution_time_ms']}ms")
        
        # Check slippage
        if metric.slippage_percent > self.thresholds['max_slippage_percent']:
            alerts.append(f"High slippage: {metric.slippage_percent:.2f}% > {self.thresholds['max_slippage_percent']}%")
        
        # Check for failed critical exits
        if not metric.success and metric.priority in ['EMERGENCY', 'CRITICAL']:
            alerts.append(f"CRITICAL: Failed {metric.priority} exit for {metric.symbol}")
        
        # Send alerts with cooldown
        for alert in alerts:
            self._send_alert(alert, metric.symbol)

    def _send_alert(self, message: str, symbol: str):
        """Send alert with cooldown to prevent spam"""
        alert_key = f"{symbol}_{hash(message)}"
        current_time = time.time()
        
        if alert_key in self.last_alert_times:
            if current_time - self.last_alert_times[alert_key] < self.alert_cooldown_seconds:
                return  # Skip due to cooldown
        
        self.last_alert_times[alert_key] = current_time
        self.logger.warning(f"🚨 PERFORMANCE ALERT: {message}")

    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active and not self._stop_event.is_set():
            try:
                # Create performance snapshot every minute
                self._create_performance_snapshot()
                
                # Check overall performance health
                self._check_overall_health()
                
                # Clean old data
                self._cleanup_old_data()
                
                # Wait for next cycle
                if self._stop_event.wait(timeout=60):  # Check every minute
                    break
                    
            except Exception as e:
                self.logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(60)

    def _create_performance_snapshot(self):
        """Create a performance snapshot"""
        try:
            if self.current_metrics['total_executions'] == 0:
                return
            
            # Calculate averages
            successful = self.current_metrics['successful_executions']
            avg_execution_time = (
                self.current_metrics['total_execution_time'] / successful 
                if successful > 0 else 0
            )
            avg_slippage = (
                self.current_metrics['total_slippage'] / successful 
                if successful > 0 else 0
            )
            success_rate = (successful / self.current_metrics['total_executions']) * 100
            
            # Create snapshot
            snapshot = PerformanceSnapshot(
                timestamp=datetime.now(),
                total_executions=self.current_metrics['total_executions'],
                successful_executions=successful,
                failed_executions=self.current_metrics['failed_executions'],
                average_execution_time=avg_execution_time,
                average_slippage=avg_slippage,
                fastest_execution=self.current_metrics['fastest_execution'],
                slowest_execution=self.current_metrics['slowest_execution'],
                success_rate=success_rate,
                critical_failures=self.current_metrics['critical_failures'],
                emergency_exits=self.current_metrics['emergency_exits']
            )
            
            self.performance_snapshots.append(snapshot)
            
            # Log snapshot
            self.logger.info(f"📊 Performance Snapshot: {successful}/{self.current_metrics['total_executions']} "
                           f"successful ({success_rate:.1f}%), avg: {avg_execution_time:.1f}ms, "
                           f"slippage: {avg_slippage:.2f}%")
            
        except Exception as e:
            self.logger.error(f"Error creating performance snapshot: {e}")

    def _check_overall_health(self):
        """Check overall system health"""
        if not self.performance_snapshots:
            return
        
        latest = self.performance_snapshots[-1]
        
        # Check success rate
        if latest.success_rate < self.thresholds['min_success_rate']:
            self._send_alert(f"Low success rate: {latest.success_rate:.1f}%", "SYSTEM")
        
        # Check critical failures in last hour
        hour_ago = datetime.now() - timedelta(hours=1)
        recent_failures = sum(
            1 for metric in self.execution_history 
            if metric.timestamp > hour_ago and not metric.success and metric.priority in ['EMERGENCY', 'CRITICAL']
        )
        
        if recent_failures > self.thresholds['max_critical_failures_per_hour']:
            self._send_alert(f"Too many critical failures: {recent_failures} in last hour", "SYSTEM")

    def _cleanup_old_data(self):
        """Clean up old performance data"""
        # Remove old alert times (older than 24 hours)
        cutoff_time = time.time() - (24 * 3600)
        self.last_alert_times = {
            k: v for k, v in self.last_alert_times.items() 
            if v > cutoff_time
        }

    def get_performance_summary(self) -> Dict:
        """Get current performance summary"""
        if self.current_metrics['total_executions'] == 0:
            return {'status': 'No executions recorded'}
        
        successful = self.current_metrics['successful_executions']
        avg_execution_time = (
            self.current_metrics['total_execution_time'] / successful 
            if successful > 0 else 0
        )
        avg_slippage = (
            self.current_metrics['total_slippage'] / successful 
            if successful > 0 else 0
        )
        success_rate = (successful / self.current_metrics['total_executions']) * 100
        
        return {
            'total_executions': self.current_metrics['total_executions'],
            'successful_executions': successful,
            'failed_executions': self.current_metrics['failed_executions'],
            'success_rate_percent': success_rate,
            'average_execution_time_ms': avg_execution_time,
            'average_slippage_percent': avg_slippage,
            'fastest_execution_ms': self.current_metrics['fastest_execution'],
            'slowest_execution_ms': self.current_metrics['slowest_execution'],
            'critical_failures': self.current_metrics['critical_failures'],
            'emergency_exits': self.current_metrics['emergency_exits'],
            'health_status': self._get_health_status(success_rate, avg_execution_time)
        }

    def _get_health_status(self, success_rate: float, avg_execution_time: float) -> str:
        """Determine overall health status"""
        if success_rate < 90:
            return "CRITICAL"
        elif success_rate < 95 or avg_execution_time > self.thresholds['max_execution_time_ms']:
            return "WARNING"
        elif success_rate >= 98 and avg_execution_time < self.thresholds['max_execution_time_ms'] * 0.5:
            return "EXCELLENT"
        else:
            return "GOOD"

    def get_recent_executions(self, limit: int = 50) -> List[Dict]:
        """Get recent execution details"""
        recent = list(self.execution_history)[-limit:]
        return [
            {
                'timestamp': metric.timestamp.isoformat(),
                'symbol': metric.symbol,
                'execution_time_ms': metric.execution_time_ms,
                'slippage_percent': metric.slippage_percent,
                'exit_reason': metric.exit_reason,
                'success': metric.success,
                'retry_count': metric.retry_count,
                'priority': metric.priority
            }
            for metric in recent
        ]

    def export_performance_data(self, filepath: str):
        """Export performance data to JSON file"""
        try:
            data = {
                'summary': self.get_performance_summary(),
                'recent_executions': self.get_recent_executions(100),
                'snapshots': [
                    {
                        'timestamp': snapshot.timestamp.isoformat(),
                        'total_executions': snapshot.total_executions,
                        'success_rate': snapshot.success_rate,
                        'average_execution_time': snapshot.average_execution_time,
                        'average_slippage': snapshot.average_slippage,
                        'critical_failures': snapshot.critical_failures,
                        'emergency_exits': snapshot.emergency_exits
                    }
                    for snapshot in list(self.performance_snapshots)
                ],
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info(f"📁 Performance data exported to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")

    def reset_metrics(self):
        """Reset all performance metrics"""
        self.current_metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_execution_time': 0.0,
            'total_slippage': 0.0,
            'fastest_execution': float('inf'),
            'slowest_execution': 0.0,
            'critical_failures': 0,
            'emergency_exits': 0
        }
        self.execution_history.clear()
        self.performance_snapshots.clear()
        self.logger.info("🔄 Performance metrics reset")
