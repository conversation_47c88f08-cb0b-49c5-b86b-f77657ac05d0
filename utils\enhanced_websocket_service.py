"""
Enhanced WebSocket Service for Market Monitoring Agent
Based on the working py_eq implementation with improvements for the current system.

This service provides:
- Real-time tick data processing
- Proper candle formation for multiple timeframes
- Symbol-to-token mapping
- Robust error handling and reconnection
"""

import logging
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict, deque
import pytz
from dataclasses import dataclass

try:
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False
    SmartWebSocketV2 = None

logger = logging.getLogger(__name__)


@dataclass
class TickData:
    """Real-time tick data"""
    symbol: str
    token: str
    price: float
    volume: int
    timestamp: datetime
    high: float = 0.0
    low: float = 0.0
    open: float = 0.0


@dataclass
class CandleBuilder:
    """Builds candles from tick data for specific timeframes"""
    symbol: str
    timeframe: str
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    start_time: Optional[datetime] = None
    tick_count: int = 0
    
    def add_tick(self, tick: TickData):
        """Add a tick to the current candle"""
        if self.start_time is None:
            self.start_time = self._get_candle_start_time(tick.timestamp)
            self.open_price = tick.price
            self.high_price = tick.price
            self.low_price = tick.price
        
        self.close_price = tick.price
        self.high_price = max(self.high_price, tick.price)
        self.low_price = min(self.low_price, tick.price)
        self.volume += tick.volume
        self.tick_count += 1
    
    def _get_candle_start_time(self, timestamp: datetime) -> datetime:
        """Get the start time for the current candle based on timeframe"""
        if self.timeframe == '5min':
            minutes = (timestamp.minute // 5) * 5
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        elif self.timeframe == '15min':
            minutes = (timestamp.minute // 15) * 15
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        elif self.timeframe == '30min':
            minutes = (timestamp.minute // 30) * 30
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        elif self.timeframe == '1hr':
            return timestamp.replace(minute=0, second=0, microsecond=0)
        else:
            return timestamp.replace(second=0, microsecond=0)
    
    def is_candle_complete(self, current_time: datetime) -> bool:
        """Check if the current candle is complete"""
        if self.start_time is None:
            return False
        
        if self.timeframe == '5min':
            next_candle_time = self.start_time + timedelta(minutes=5)
        elif self.timeframe == '15min':
            next_candle_time = self.start_time + timedelta(minutes=15)
        elif self.timeframe == '30min':
            next_candle_time = self.start_time + timedelta(minutes=30)
        elif self.timeframe == '1hr':
            next_candle_time = self.start_time + timedelta(hours=1)
        else:
            next_candle_time = self.start_time + timedelta(minutes=1)
        
        return current_time >= next_candle_time


class EnhancedWebSocketService:
    """Enhanced WebSocket service for market data with proper candle formation"""
    
    def __init__(self, auth_token: str, api_key: str, username: str, feed_token: str, selected_symbols: Dict[str, str] = None):
        self.auth_token = auth_token
        self.api_key = api_key
        self.username = username
        self.feed_token = feed_token

        # WebSocket connection
        self.websocket = None
        self.is_connected = False
        self.is_running = False

        # Symbol management - use selected symbols if provided, otherwise use default mapping
        if selected_symbols:
            self.symbol_tokens = selected_symbols
            logger.info(f"Enhanced WebSocket initialized with {len(selected_symbols)} selected symbols: {list(selected_symbols.keys())}")
        else:
            self.symbol_tokens = self._get_symbol_token_mapping()
            logger.info(f"Enhanced WebSocket initialized with {len(self.symbol_tokens)} default symbols")

        self.token_symbols = {v: k for k, v in self.symbol_tokens.items()}
        self.subscribed_symbols: Set[str] = set()
        
        # Candle builders for different timeframes
        self.candle_builders = {
            '5min': {},
            '15min': {},
            '30min': {},
            '1hr': {}
        }
        
        # Completed candles storage
        self.candles = {
            '5min': defaultdict(lambda: deque(maxlen=200)),
            '15min': defaultdict(lambda: deque(maxlen=200)),
            '30min': defaultdict(lambda: deque(maxlen=200)),
            '1hr': defaultdict(lambda: deque(maxlen=200))
        }
        
        # Callbacks
        self.tick_callbacks: List[Callable] = []
        self.candle_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            'ticks_received': 0,
            'candles_formed': 0,
            'last_tick_time': None,
            'connection_time': None,
            'reconnection_count': 0
        }

        # Volume tracking for proper tick volume calculation
        self.last_volumes = {}  # symbol -> last cumulative volume
        
        # IST timezone
        self.ist_timezone = pytz.timezone('Asia/Kolkata')
        
        logger.info(f"Enhanced WebSocket service initialized with {len(self.symbol_tokens)} symbol mappings")

    def update_selected_symbols(self, selected_symbols: Dict[str, str]):
        """Update the symbol-token mapping with selected symbols"""
        try:
            self.symbol_tokens = selected_symbols
            self.token_symbols = {v: k for k, v in selected_symbols.items()}

            # Reset candle builders for new symbols
            for timeframe in ['5min', '15min', '30min', '1hr']:
                self.candle_builders[timeframe] = {}
                for symbol in selected_symbols.keys():
                    self.candle_builders[timeframe][symbol] = CandleBuilder(symbol, timeframe)

            # Clear existing candles
            for timeframe in ['5min', '15min', '30min', '1hr']:
                self.candles[timeframe] = defaultdict(lambda: deque(maxlen=200))

            logger.info(f"Updated Enhanced WebSocket with {len(selected_symbols)} selected symbols: {list(selected_symbols.keys())}")

        except Exception as e:
            logger.error(f"Error updating selected symbols: {e}")

    def _get_symbol_token_mapping(self) -> Dict[str, str]:
        """Get comprehensive symbol to token mapping"""
        return {
            'RELIANCE': '2885',
            'HDFCBANK': '1333',
            'INFY': '1594',
            'TCS': '11536',
            'ICICIBANK': '4963',
            'SIEMENS': '3150',
            'AUBANK': '21238',
            'APOLLOTYRE': '163',
            'NMDC': '15332',
            'GODREJPROP': '17875',
            'ASHOKLEY': '212',
            'GODREJCP': '1232',
            'ICICIGI': '21770',
            'ASIANPAINT': '236',
            'BHARTIARTL': '10604',
            'M&M': '519',
            'TATAMOTORS': '3456',
            'NESTLEIND': '17963',
            'INDHOTEL': '1512',
            'CUMMINSIND': '1901',
            'TECHM': '13538',
            'HDFC': '1330',
            'ITC': '424',
            'KOTAKBANK': '492',
            'LT': '11483',
            'SBIN': '3045',
            'HINDUNILVR': '1394',
            'BAJFINANCE': '317',
            'MARUTI': '10999',
            'AXISBANK': '5900',
            'TATASTEEL': '3499',
            'SUNPHARMA': '3351',
            'TITAN': '3506',
            'BAJAJFINSV': '16675',
            'WIPRO': '3787',
            'HCLTECH': '7229',
            'ULTRACEMCO': '11532'
        }
    
    def add_tick_callback(self, callback: Callable):
        """Add callback for new ticks"""
        self.tick_callbacks.append(callback)
    
    def add_candle_callback(self, callback: Callable):
        """Add callback for new candles"""
        self.candle_callbacks.append(callback)
    
    async def initialize(self) -> bool:
        """Initialize the WebSocket service"""
        try:
            if not SMARTAPI_AVAILABLE:
                logger.error("SmartAPI WebSocket not available")
                return False
            
            # Initialize WebSocket
            self._initialize_websocket()
            
            logger.info("✅ Enhanced WebSocket service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced WebSocket service: {e}")
            return False
    
    def _initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.websocket = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token,
                max_retry_attempt=5
            )
            
            # Set up callbacks
            self.websocket.on_open = self._on_open
            self.websocket.on_data = self._on_data
            self.websocket.on_error = self._on_error
            self.websocket.on_close = self._on_close
            
            logger.info("Enhanced WebSocket initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Enhanced WebSocket: {e}")
            raise

    async def start(self):
        """Start the WebSocket service"""
        try:
            if not self.websocket:
                raise Exception("WebSocket not initialized")

            self.is_running = True

            # Start WebSocket in a separate thread
            websocket_thread = threading.Thread(target=self._run_websocket, daemon=True)
            websocket_thread.start()

            # Start candle formation loop
            asyncio.create_task(self._candle_formation_loop())

            logger.info("✅ Enhanced WebSocket service started")

        except Exception as e:
            logger.error(f"Failed to start Enhanced WebSocket service: {e}")
            raise

    def _run_websocket(self):
        """Run WebSocket in separate thread"""
        try:
            self.websocket.connect()
        except Exception as e:
            logger.error(f"Enhanced WebSocket connection error: {e}")

    async def _candle_formation_loop(self):
        """Main loop for candle formation"""
        logger.info("Starting candle formation loop...")
        loop_count = 0
        while self.is_running:
            try:
                current_time = datetime.now(self.ist_timezone)
                loop_count += 1

                # Log every 60 seconds to show the loop is running
                if loop_count % 60 == 0:
                    logger.info(f"[CANDLE_LOOP] Running... Current time: {current_time.strftime('%H:%M:%S')}")

                # Check and complete candles for all timeframes
                for timeframe in ['5min', '15min', '30min', '1hr']:
                    await self._check_and_complete_candles(timeframe, current_time)

                # Sleep for 1 second
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"Error in candle formation loop: {e}")
                await asyncio.sleep(5)

    async def _check_and_complete_candles(self, timeframe: str, current_time: datetime):
        """Check and complete candles for a specific timeframe"""
        completed_candles = []
        builders = self.candle_builders[timeframe]

        for symbol, builder in list(builders.items()):
            if builder.start_time and builder.is_candle_complete(current_time):
                # Complete the candle
                candle_data = {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'timestamp': builder.start_time,
                    'open': builder.open_price,
                    'high': builder.high_price,
                    'low': builder.low_price,
                    'close': builder.close_price,
                    'volume': builder.volume
                }

                self.candles[timeframe][symbol].append(candle_data)
                completed_candles.append(candle_data)

                # Reset builder for next candle
                builders[symbol] = CandleBuilder(symbol, timeframe)

                # Update statistics
                self.stats['candles_formed'] += 1

                # Log candle formation with volume details
                logger.info(f"[CANDLE_FORMED] {symbol} {timeframe}: O={candle_data['open']:.2f} H={candle_data['high']:.2f} L={candle_data['low']:.2f} C={candle_data['close']:.2f} V={candle_data['volume']} (ticks: {builder.tick_count})")

        # Notify callbacks about new candles
        for candle in completed_candles:
            for callback in self.candle_callbacks:
                try:
                    await callback(candle)
                except Exception as e:
                    logger.error(f"Error in candle callback: {e}")

    def _on_open(self, ws):
        """WebSocket opened callback"""
        self.is_connected = True
        self.stats['connection_time'] = datetime.now(self.ist_timezone)
        logger.info("✅ Enhanced WebSocket connection opened")

        # Subscribe to symbols
        self._subscribe_to_symbols()

    def _on_data(self, ws, message):
        """WebSocket data received callback"""
        try:
            # Parse the message and create tick data
            tick = self._parse_tick_data(message)
            if tick:
                self._process_tick(tick)
        except Exception as e:
            logger.error(f"Error processing Enhanced WebSocket data: {e}")

    def _on_error(self, ws, error):
        """WebSocket error callback"""
        logger.error(f"Enhanced WebSocket error: {error}")
        self.is_connected = False

    def _on_close(self, ws):
        """WebSocket closed callback"""
        logger.warning("Enhanced WebSocket connection closed")
        self.is_connected = False
        self.stats['reconnection_count'] += 1

        # Attempt reconnection if still running
        if self.is_running:
            logger.info("Attempting to reconnect Enhanced WebSocket...")
            threading.Timer(5.0, self._reconnect).start()

    def _reconnect(self):
        """Reconnect WebSocket"""
        try:
            if self.is_running and not self.is_connected:
                self.websocket.connect()
        except Exception as e:
            logger.error(f"Enhanced WebSocket reconnection failed: {e}")

    def _subscribe_to_symbols(self):
        """Subscribe to all available symbols"""
        try:
            if not self.symbol_tokens:
                logger.warning("No symbol tokens available for subscription")
                return

            # Prepare subscription data
            token_list = list(self.symbol_tokens.values())

            # Subscribe to symbols (SmartAPI WebSocket format)
            subscription_data = {
                "action": 1,  # Subscribe
                "mode": 1,    # LTP mode
                "tokenList": [
                    {
                        "exchangeType": 1,  # NSE
                        "tokens": token_list
                    }
                ]
            }

            # Use the correct SmartAPI WebSocket subscription method
            self.websocket.subscribe("correlation_id", 1, subscription_data["tokenList"])
            self.subscribed_symbols.update(self.symbol_tokens.keys())

            # Initialize candle builders for all symbols and timeframes
            for symbol in self.symbol_tokens.keys():
                for timeframe in ['5min', '15min', '30min', '1hr']:
                    self.candle_builders[timeframe][symbol] = CandleBuilder(symbol, timeframe)

            logger.info(f"Enhanced WebSocket subscribed to {len(token_list)} symbols")

        except Exception as e:
            logger.error(f"Error subscribing to symbols: {e}")

    def _parse_tick_data(self, message) -> Optional[TickData]:
        """Parse WebSocket message to TickData with proper volume handling"""
        try:
            if isinstance(message, dict):
                token = str(message.get('token', ''))
                if not token:
                    return None

                # Find symbol by token
                symbol = self.token_symbols.get(token)
                if not symbol:
                    return None

                # Extract price data - handle both paise and rupee formats
                ltp = float(message.get('last_traded_price', 0))
                if ltp > 10000:  # Convert from paise to rupees
                    ltp = ltp / 100

                # Extract volume data with multiple fallback options
                volume = 0
                
                # Try multiple volume fields in order of preference
                volume_fields = ['volume_traded_today', 'volume_traded', 'volume', 'v', 'total_traded_volume']
                for field in volume_fields:
                    if field in message and message[field] is not None:
                        volume = int(message[field])
                        break
                
                # If still 0, use last traded quantity as fallback
                if volume == 0 and 'last_traded_quantity' in message:
                    volume = int(message.get('last_traded_quantity', 0))

                if ltp <= 0:
                    return None

                # Calculate tick volume properly
                tick_volume = 0
                current_volume = volume
                
                if symbol in self.last_volumes:
                    # Calculate difference from last known volume
                    if current_volume > self.last_volumes[symbol]:
                        tick_volume = current_volume - self.last_volumes[symbol]
                    elif current_volume < self.last_volumes[symbol]:
                        # Reset or new day - use last traded quantity
                        tick_volume = int(message.get('last_traded_quantity', 1))
                    else:
                        # No change - use minimum volume
                        tick_volume = int(message.get('last_traded_quantity', 1))
                else:
                    # First tick - use last traded quantity
                    tick_volume = int(message.get('last_traded_quantity', 1))

                # Update last volume
                self.last_volumes[symbol] = current_volume

                # Extract OHLC data
                high = float(message.get('high_price_of_day', ltp))
                low = float(message.get('low_price_of_day', ltp))
                open_price = float(message.get('open_price_of_day', ltp))
                
                # Convert from paise if needed
                if high > 10000: high = high / 100
                if low > 10000: low = low / 100
                if open_price > 10000: open_price = open_price / 100

                tick = TickData(
                    symbol=symbol,
                    token=token,
                    price=ltp,
                    volume=tick_volume,
                    timestamp=datetime.now(self.ist_timezone),
                    high=high,
                    low=low,
                    open=open_price
                )

                return tick

        except Exception as e:
            logger.error(f"Error parsing tick data: {e}")
            return None

    def _process_tick(self, tick: TickData):
        """Process incoming tick data"""
        try:
            # Update statistics
            self.stats['ticks_received'] += 1
            self.stats['last_tick_time'] = tick.timestamp

            # Add tick to candle builders for all timeframes
            for timeframe in ['5min', '15min', '30min', '1hr']:
                if tick.symbol in self.candle_builders[timeframe]:
                    self.candle_builders[timeframe][tick.symbol].add_tick(tick)

            # Notify tick callbacks
            for callback in self.tick_callbacks:
                try:
                    callback(tick)
                except Exception as e:
                    logger.error(f"Error in tick callback: {e}")

        except Exception as e:
            logger.error(f"Error processing tick: {e}")

    def get_latest_candles(self, symbol: str, timeframe: str, count: int = 10) -> List[Dict]:
        """Get latest candles for a symbol and timeframe"""
        try:
            candles = list(self.candles[timeframe].get(symbol, []))
            return candles[-count:] if candles else []
        except Exception as e:
            logger.error(f"Error getting latest candles: {e}")
            return []

    def get_statistics(self) -> Dict:
        """Get service statistics"""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'is_running': self.is_running,
            'subscribed_symbols': len(self.subscribed_symbols),
            'available_symbols': len(self.symbol_tokens)
        }

    async def stop(self):
        """Stop the WebSocket service"""
        try:
            self.is_running = False

            if self.websocket and self.is_connected:
                self.websocket.close()

            logger.info("Enhanced WebSocket service stopped")

        except Exception as e:
            logger.error(f"Error stopping Enhanced WebSocket service: {e}")
