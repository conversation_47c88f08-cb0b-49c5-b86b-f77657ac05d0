"""
Order State Manager - Comprehensive order and position state management
Prevents duplicate orders and ensures proper position tracking
"""

import json
import os
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from models.order import Order, OrderStatus, TransactionType


class PositionStatus(Enum):
    """Position status enumeration"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIAL = "PARTIAL"
    MONITORING = "MONITORING"


@dataclass
class PositionRecord:
    """Position tracking record"""
    position_id: str
    symbol: str
    order_id: str
    entry_time: str
    entry_price: float
    quantity: int
    stop_loss: float
    target: float
    status: PositionStatus
    strategy: str
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    exit_time: Optional[str] = None
    exit_price: Optional[float] = None
    exit_reason: Optional[str] = None
    notes: str = ""

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['status'] = self.status.value
        return data

    @classmethod
    def from_dict(cls, data: Dict) -> 'PositionRecord':
        """Create from dictionary"""
        data['status'] = PositionStatus(data['status'])
        return cls(**data)


class OrderStateManager:
    """
    Comprehensive order and position state management
    
    Features:
    - Track all orders and their states
    - Prevent duplicate orders for same symbol
    - Monitor open positions
    - Persist state across restarts
    - Integration with broker API for real-time updates
    """

    def __init__(self, logger: logging.Logger, order_service, data_dir: str = "data"):
        self.logger = logger
        self.order_service = order_service
        self.data_dir = data_dir
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Current state
        self.current_date = date.today()
        self.open_positions: Dict[str, PositionRecord] = {}  # symbol -> PositionRecord
        self.order_history: Dict[str, Dict] = {}  # order_id -> order_data
        self.symbol_order_map: Dict[str, str] = {}  # symbol -> latest_order_id
        
        # Load existing state
        self._load_state()
        
        # Sync with broker
        self._sync_with_broker()
        
        self.logger.info(f"📊 Order State Manager initialized: {len(self.open_positions)} open positions")

    def _get_state_file_path(self) -> str:
        """Get the path to the state file"""
        return os.path.join(self.data_dir, f"order_state_{self.current_date.isoformat()}.json")

    def _load_state(self):
        """Load state from file"""
        state_file = self._get_state_file_path()
        
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                # Load open positions
                positions_data = state_data.get('open_positions', {})
                for symbol, pos_data in positions_data.items():
                    try:
                        position = PositionRecord.from_dict(pos_data)
                        self.open_positions[symbol] = position
                    except Exception as e:
                        self.logger.error(f"Error loading position for {symbol}: {e}")
                
                # Load order history
                self.order_history = state_data.get('order_history', {})
                self.symbol_order_map = state_data.get('symbol_order_map', {})
                
                self.logger.info(f"📄 Loaded order state: {len(self.open_positions)} positions, {len(self.order_history)} orders")
                
            except Exception as e:
                self.logger.error(f"Error loading order state: {e}")
                self._initialize_empty_state()
        else:
            self._initialize_empty_state()

    def _initialize_empty_state(self):
        """Initialize empty state"""
        self.open_positions = {}
        self.order_history = {}
        self.symbol_order_map = {}
        self.logger.info("📅 Initialized empty order state")

    def _save_state(self):
        """Save current state to file"""
        try:
            state_data = {
                'date': self.current_date.isoformat(),
                'open_positions': {symbol: pos.to_dict() for symbol, pos in self.open_positions.items()},
                'order_history': self.order_history,
                'symbol_order_map': self.symbol_order_map,
                'last_updated': datetime.now().isoformat()
            }
            
            state_file = self._get_state_file_path()
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Error saving order state: {e}")

    def _sync_with_broker(self):
        """Sync state with broker's actual positions"""
        try:
            self.logger.info("🔄 Syncing order state with broker...")
            
            # Get real positions from broker
            broker_positions = self.order_service.get_open_positions()
            
            # Track symbols found in broker
            broker_symbols = set()
            
            for broker_order in broker_positions:
                symbol = broker_order.symbol
                broker_symbols.add(symbol)
                
                # Check if we have this position in our records
                if symbol not in self.open_positions:
                    # New position found in broker but not in our records
                    self.logger.warning(f"⚠️ Found untracked position in broker: {symbol}")
                    
                    # Create position record from broker data
                    position = PositionRecord(
                        position_id=f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        symbol=symbol,
                        order_id=getattr(broker_order, 'order_id', 'UNKNOWN'),
                        entry_time=datetime.now().isoformat(),
                        entry_price=getattr(broker_order, 'entry_price', broker_order.price),
                        quantity=broker_order.quantity,
                        stop_loss=getattr(broker_order, 'stop_loss', 0.0),
                        target=getattr(broker_order, 'target', 0.0),
                        status=PositionStatus.MONITORING,
                        strategy=getattr(broker_order, 'strategy', 'UNKNOWN'),
                        notes="Position found in broker sync"
                    )
                    
                    self.open_positions[symbol] = position
                    self.logger.info(f"📈 Added untracked position: {symbol}")
                else:
                    # Update existing position with broker data
                    position = self.open_positions[symbol]
                    position.current_price = broker_order.price
                    
                    # Calculate unrealized PnL
                    if position.entry_price > 0:
                        price_diff = broker_order.price - position.entry_price
                        position.unrealized_pnl = price_diff * position.quantity
            
            # Check for positions in our records that are not in broker
            our_symbols = set(self.open_positions.keys())
            missing_symbols = our_symbols - broker_symbols
            
            for symbol in missing_symbols:
                self.logger.warning(f"⚠️ Position {symbol} in our records but not found in broker")
                # Mark as potentially closed
                position = self.open_positions[symbol]
                if position.status == PositionStatus.OPEN:
                    position.status = PositionStatus.MONITORING
                    position.notes += " | Not found in broker sync"
            
            # Save updated state
            self._save_state()
            
            self.logger.info(f"✅ Broker sync complete: {len(broker_positions)} broker positions, {len(self.open_positions)} tracked positions")
            
        except Exception as e:
            self.logger.error(f"Error syncing with broker: {e}")

    def can_place_order(self, symbol: str) -> Tuple[bool, str]:
        """
        Check if an order can be placed for the given symbol
        
        Returns:
            Tuple[bool, str]: (can_place, reason)
        """
        # Check if symbol already has an open position
        if symbol in self.open_positions:
            position = self.open_positions[symbol]
            if position.status in [PositionStatus.OPEN, PositionStatus.MONITORING]:
                return False, f"Open position exists for {symbol}"
        
        # Check if there's a recent order for this symbol
        if symbol in self.symbol_order_map:
            order_id = self.symbol_order_map[symbol]
            if order_id in self.order_history:
                order_data = self.order_history[order_id]
                order_time = datetime.fromisoformat(order_data.get('timestamp', '2000-01-01T00:00:00'))
                
                # Check if order was placed recently (within last hour)
                time_diff = datetime.now() - order_time
                if time_diff.total_seconds() < 3600:  # 1 hour
                    return False, f"Recent order exists for {symbol} (placed {time_diff.total_seconds():.0f}s ago)"
        
        return True, "Order allowed"

    def record_order_placement(self, order: Order) -> bool:
        """Record order placement"""
        try:
            order_data = {
                'order_id': order.order_id,
                'symbol': order.symbol,
                'timestamp': datetime.now().isoformat(),
                'transaction_type': order.transaction_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'stop_loss': order.stop_loss,
                'target': order.target,
                'status': order.status.value,
                'strategy': getattr(order, 'strategy', 'UNKNOWN')
            }
            
            # Store in order history
            self.order_history[order.order_id] = order_data
            
            # Update symbol mapping
            self.symbol_order_map[order.symbol] = order.order_id
            
            # If order is executed, create position record
            if order.status == OrderStatus.COMPLETED:
                self._create_position_from_order(order)
            
            # Save state
            self._save_state()
            
            self.logger.info(f"📝 Order recorded: {order.symbol} {order.order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error recording order: {e}")
            return False

    def _create_position_from_order(self, order: Order):
        """Create position record from executed order"""
        position = PositionRecord(
            position_id=f"{order.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=order.symbol,
            order_id=order.order_id,
            entry_time=datetime.now().isoformat(),
            entry_price=order.price,
            quantity=order.quantity,
            stop_loss=order.stop_loss,
            target=order.target,
            status=PositionStatus.OPEN,
            strategy=getattr(order, 'strategy', 'UNKNOWN'),
            notes="Position created from order execution"
        )
        
        self.open_positions[order.symbol] = position
        self.logger.info(f"📈 Position created: {order.symbol} @ ₹{order.price}")

    def close_position(self, symbol: str, exit_price: float, exit_reason: str) -> bool:
        """Close a position"""
        if symbol not in self.open_positions:
            self.logger.warning(f"Position {symbol} not found for closing")
            return False
        
        position = self.open_positions[symbol]
        position.status = PositionStatus.CLOSED
        position.exit_time = datetime.now().isoformat()
        position.exit_price = exit_price
        position.exit_reason = exit_reason
        
        # Calculate final PnL
        if position.entry_price > 0:
            price_diff = exit_price - position.entry_price
            position.unrealized_pnl = price_diff * position.quantity
        
        # Remove from open positions
        del self.open_positions[symbol]
        
        # Save state
        self._save_state()
        
        self.logger.info(f"📉 Position closed: {symbol} @ ₹{exit_price} (Reason: {exit_reason})")
        return True

    def get_open_positions(self) -> List[PositionRecord]:
        """Get all open positions"""
        return list(self.open_positions.values())

    def get_position_summary(self) -> Dict:
        """Get summary of current positions"""
        open_positions = [p for p in self.open_positions.values() if p.status == PositionStatus.OPEN]
        monitoring_positions = [p for p in self.open_positions.values() if p.status == PositionStatus.MONITORING]
        
        total_unrealized_pnl = sum(p.unrealized_pnl for p in self.open_positions.values())
        
        return {
            'total_positions': len(self.open_positions),
            'open_positions': len(open_positions),
            'monitoring_positions': len(monitoring_positions),
            'symbols': list(self.open_positions.keys()),
            'total_unrealized_pnl': total_unrealized_pnl,
            'last_sync': datetime.now().isoformat()
        }
