"""
Order service for placing and managing orders using SmartAPI
"""
import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from datetime import datetime, time
import pyotp
from SmartApi import SmartConnect

from models.order import Order, OrderStatus, TransactionType, OrderType, ProductType
from config.config import SmartAPIConfig
from utils.helpers import is_market_open


class TransactionCosts:
    """Realistic transaction cost calculation for equity trading (Angel One rates)"""

    # Brokerage rates
    BROKERAGE_RATE = 0.0003  # 0.03% or ₹20 per order, whichever is lower
    MAX_BROKERAGE_PER_ORDER = 20.0  # Maximum ₹20 per order

    # Government taxes and charges
    STT_RATE = 0.001  # 0.1% on both buy and sell for delivery, 0.025% for intraday sell
    STT_INTRADAY_RATE = 0.00025  # 0.025% on sell side for intraday

    # Exchange charges
    EXCHANGE_CHARGES_RATE = 0.0000345  # NSE: 0.00345% of turnover

    # SEBI charges
    SEBI_CHARGES_RATE = 0.000001  # ₹1 per crore of turnover

    # Stamp duty
    STAMP_DUTY_RATE = 0.00015  # 0.015% on buy side only

    # GST
    GST_RATE = 0.18  # 18% on brokerage + exchange charges + SEBI charges

    @staticmethod
    def calculate_total_cost(entry_price: float, exit_price: float, quantity: int,
                           is_intraday: bool = True) -> Dict[str, float]:
        """
        Calculate total transaction cost for equity trading

        Args:
            entry_price: Entry price per share
            exit_price: Exit price per share
            quantity: Number of shares
            is_intraday: True for intraday, False for delivery

        Returns:
            Dictionary with detailed cost breakdown
        """
        # Calculate turnover
        entry_value = entry_price * quantity
        exit_value = exit_price * quantity
        total_turnover = entry_value + exit_value

        # 1. Brokerage calculation
        entry_brokerage = min(entry_value * TransactionCosts.BROKERAGE_RATE,
                             TransactionCosts.MAX_BROKERAGE_PER_ORDER)
        exit_brokerage = min(exit_value * TransactionCosts.BROKERAGE_RATE,
                            TransactionCosts.MAX_BROKERAGE_PER_ORDER)
        total_brokerage = entry_brokerage + exit_brokerage

        # 2. STT (Securities Transaction Tax)
        if is_intraday:
            # Intraday: STT only on sell side at 0.025%
            stt = exit_value * TransactionCosts.STT_INTRADAY_RATE
        else:
            # Delivery: STT on both buy and sell at 0.1%
            stt = total_turnover * TransactionCosts.STT_RATE

        # 3. Exchange charges (NSE)
        exchange_charges = total_turnover * TransactionCosts.EXCHANGE_CHARGES_RATE

        # 4. SEBI charges
        sebi_charges = total_turnover * TransactionCosts.SEBI_CHARGES_RATE

        # 5. Stamp duty (only on buy side)
        stamp_duty = entry_value * TransactionCosts.STAMP_DUTY_RATE

        # 6. GST (18% on brokerage + exchange charges + SEBI charges)
        gst_base = total_brokerage + exchange_charges + sebi_charges
        gst = gst_base * TransactionCosts.GST_RATE

        # Total transaction cost
        total_cost = total_brokerage + stt + exchange_charges + sebi_charges + stamp_duty + gst

        return {
            'entry_brokerage': round(entry_brokerage, 2),
            'exit_brokerage': round(exit_brokerage, 2),
            'total_brokerage': round(total_brokerage, 2),
            'stt': round(stt, 2),
            'exchange_charges': round(exchange_charges, 2),
            'sebi_charges': round(sebi_charges, 2),
            'stamp_duty': round(stamp_duty, 2),
            'gst': round(gst, 2),
            'total_cost': round(total_cost, 2),
            'entry_value': round(entry_value, 2),
            'exit_value': round(exit_value, 2),
            'total_turnover': round(total_turnover, 2),
            'cost_percentage': round((total_cost / total_turnover) * 100, 4) if total_turnover > 0 else 0
        }

    @staticmethod
    def calculate_entry_cost_only(entry_price: float, quantity: int, is_intraday: bool = True) -> float:
        """Calculate only entry-side transaction costs for position opening"""
        entry_value = entry_price * quantity

        # Entry brokerage
        entry_brokerage = min(entry_value * TransactionCosts.BROKERAGE_RATE,
                             TransactionCosts.MAX_BROKERAGE_PER_ORDER)

        # STT on entry (only for delivery)
        entry_stt = 0
        if not is_intraday:
            entry_stt = entry_value * TransactionCosts.STT_RATE

        # Exchange charges (half of total, assuming equal entry/exit values)
        entry_exchange_charges = entry_value * TransactionCosts.EXCHANGE_CHARGES_RATE

        # SEBI charges (half of total)
        entry_sebi_charges = entry_value * TransactionCosts.SEBI_CHARGES_RATE

        # Stamp duty (only on buy side)
        stamp_duty = entry_value * TransactionCosts.STAMP_DUTY_RATE

        # GST on entry costs
        gst_base = entry_brokerage + entry_exchange_charges + entry_sebi_charges
        entry_gst = gst_base * TransactionCosts.GST_RATE

        total_entry_cost = entry_brokerage + entry_stt + entry_exchange_charges + entry_sebi_charges + stamp_duty + entry_gst

        return round(total_entry_cost, 2)


class OrderServiceInterface(ABC):
    """Abstract interface for order services"""

    @abstractmethod
    def place_order(
        self,
        symbol: str,
        token: str,
        exchange: str,
        transaction_type: TransactionType,
        entry_price: float,
        stop_loss: float,
        target: float,
        quantity: int
    ) -> Optional[Order]:
        """Place an order"""
        pass

    @abstractmethod
    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get order status"""
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        pass

    @abstractmethod
    def get_open_positions(self) -> List[Order]:
        """Get all open positions"""
        pass

    @abstractmethod
    def square_off_position(self, order: Order, exit_price: float, exit_reason: str) -> bool:
        """Square off a position"""
        pass


class SmartAPIOrderService(OrderServiceInterface):
    """SmartAPI implementation of order service"""

    def __init__(self, config: SmartAPIConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.smart_api: Optional[SmartConnect] = None
        self.auth_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.feed_token: Optional[str] = None
        self._authenticated = False

        # Rate limiting to prevent "exceeding access rate" errors
        self.last_api_call = 0
        self.api_call_delay = 2.0  # 2 seconds between API calls
        self.max_retries = 3
        self.retry_delay = 5.0  # 5 seconds between retries

        # ORDER TIMING CONTROLS: Prevent multiple orders within seconds
        self.last_order_time = 0.0  # Timestamp of last order placement
        self.min_order_gap_seconds = 3.0  # Minimum 3 seconds between orders
        self.order_placement_lock = False  # Lock to prevent concurrent order placement
        self.recent_orders = {}  # Track recent orders by symbol to prevent duplicates

    def authenticate(self) -> bool:
        """Authenticate with SmartAPI"""
        try:
            # Initialize SmartConnect
            self.smart_api = SmartConnect(api_key=self.config.api_key)

            # Generate TOTP
            totp = pyotp.TOTP(self.config.totp_token).now()

            # Generate session
            data = self.smart_api.generateSession(
                self.config.username,
                self.config.password,
                totp
            )

            if not data.get('status', False):
                self.logger.error(f"Authentication failed: {data}")
                return False

            # Extract tokens
            self.auth_token = data['data']['jwtToken']
            self.refresh_token = data['data']['refreshToken']
            self.feed_token = self.smart_api.getfeedToken()

            # Generate refresh token
            self.smart_api.generateToken(self.refresh_token)

            self._authenticated = True
            self.logger.info("SmartAPI authentication successful")
            return True

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return False

    def _wait_for_rate_limit(self):
        """Wait for rate limit to prevent API errors"""
        import time
        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call

        if time_since_last_call < self.api_call_delay:
            sleep_time = self.api_call_delay - time_since_last_call
            self.logger.debug(f"Rate limiting: waiting {sleep_time:.2f} seconds")
            time.sleep(sleep_time)

        self.last_api_call = time.time()

    def _make_api_call_with_retry(self, api_func, *args, **kwargs):
        """Make API call with retry logic and rate limiting"""
        import time

        for attempt in range(self.max_retries):
            try:
                # Apply rate limiting
                self._wait_for_rate_limit()

                # Make the API call
                result = api_func(*args, **kwargs)
                return result

            except Exception as e:
                error_msg = str(e).lower()

                # Check for rate limiting errors
                is_rate_limit_error = any(phrase in error_msg for phrase in [
                    "access denied", "rate", "exceeding access rate",
                    "too many requests", "quota exceeded", "throttled"
                ])

                if is_rate_limit_error and attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2 ** attempt)  # Exponential backoff
                    self.logger.warning(f"Rate limit error, retrying in {wait_time} seconds (attempt {attempt + 1}/{self.max_retries})")
                    time.sleep(wait_time)
                    continue
                else:
                    # Re-raise the exception if it's not a rate limit error or we've exhausted retries
                    raise e

        return None

    def place_order(
        self,
        symbol: str,
        token: str,
        exchange: str,
        transaction_type: TransactionType,
        entry_price: float,
        stop_loss: float,
        target: float,
        quantity: int
    ) -> Optional[Order]:
        """Place an order using SmartAPI with market hours validation and timing controls"""
        import time

        # Enhanced market hours validation with detailed logging
        self.logger.info(f"ORDER_VALIDATION: Attempting to place order for {symbol} {getattr(transaction_type, 'value', transaction_type)}")
        self.logger.info(f"ORDER_DETAILS: Price: {entry_price}, Qty: {quantity}, SL: {stop_loss}, Target: {target}")

        if not is_market_open():
            self.logger.error(f"❌ ORDER_REJECTED: Cannot place order for {symbol}: Market is closed!")
            self.logger.error("⚠️ ORDER_REJECTION_REASON: Orders can only be placed during market hours (9:15 AM - 3:30 PM IST, Monday-Friday)")
            self.logger.error(f"ORDER_REJECTION_DETAILS: Symbol: {symbol}, Type: {getattr(transaction_type, 'value', transaction_type)}, Price: {entry_price}")
            return None

        # TIMING CONTROL: Check if order placement is locked
        if self.order_placement_lock:
            self.logger.warning(f"⏳ ORDER_REJECTED: Order placement locked for {symbol}")
            return None

        # TIMING CONTROL: Check minimum gap between orders
        current_time = time.time()
        if self.last_order_time > 0:
            time_since_last_order = current_time - self.last_order_time
            if time_since_last_order < self.min_order_gap_seconds:
                remaining_wait = self.min_order_gap_seconds - time_since_last_order
                self.logger.warning(f"⏳ ORDER_REJECTED: Must wait {remaining_wait:.1f}s before placing next order for {symbol}")
                return None

        # DUPLICATE PREVENTION: Check for recent orders for the same symbol
        if symbol in self.recent_orders:
            recent_order_time = self.recent_orders[symbol]
            time_since_recent = current_time - recent_order_time
            if time_since_recent < 60.0:  # 60 seconds cooldown per symbol
                self.logger.warning(f"⏳ ORDER_REJECTED: Recent order for {symbol} placed {time_since_recent:.1f}s ago (60s cooldown)")
                return None

        self.logger.info(f"✅ ORDER_VALIDATION_PASSED: Market is open and timing checks passed, proceeding with order placement for {symbol}")

        # Lock order placement
        self.order_placement_lock = True

        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before placing order")
                # Unlock order placement on authentication failure
                self.order_placement_lock = False
                return None

        try:
            # Create order object
            order = Order(
                symbol=symbol,
                symbol_token=token,
                exchange=exchange,
                transaction_type=transaction_type,
                order_type=OrderType.MARKET,  # Using market orders for immediate execution
                product_type=ProductType.INTRADAY,
                quantity=quantity,
                price=entry_price,
                stop_loss=stop_loss,
                target=target,
                strategy="SMARTAPI_ORDER"  # Default strategy for direct API orders
            )

            # Prepare order parameters for SmartAPI
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": f"{symbol}-EQ",
                "symboltoken": token,
                "transactiontype": getattr(transaction_type, 'value', transaction_type),
                "exchange": exchange,
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",  # Market order, so price is 0
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(quantity)
            }

            self.logger.info(f"Placing order: {order_params}")

            # Place the order
            response = self.smart_api.placeOrderFullResponse(order_params)

            if response.get('status') and response.get('data'):
                order_id = response['data'].get('orderid')
                order.order_id = order_id
                order.status = OrderStatus.OPEN

                # Record successful order placement timing
                self.last_order_time = current_time
                self.recent_orders[symbol] = current_time

                self.logger.info(f"✅ Order placed successfully: {order_id}")

                # ENHANCED FIX: Use comprehensive method to get actual executed price for market orders
                # This prevents the "Invalid entry_price: 0.0" error when positions are fetched later
                executed_price = self._get_executed_price_comprehensive(order_id, symbol, max_retries=3)
                if executed_price and executed_price > 0:
                    order.price = executed_price
                    order.entry_price = executed_price

                    # ENHANCED: Recalculate SL and target based on actual executed price
                    original_sl_distance = abs(entry_price - stop_loss)
                    original_target_distance = abs(target - entry_price)

                    if order.transaction_type == TransactionType.BUY:
                        # For BUY orders: SL below executed price, target above
                        new_stop_loss = executed_price - original_sl_distance
                        new_target = executed_price + original_target_distance
                    else:
                        # For SELL orders: SL above executed price, target below
                        new_stop_loss = executed_price + original_sl_distance
                        new_target = executed_price - original_target_distance

                    order.stop_loss = new_stop_loss
                    order.target = new_target

                    self.logger.info(f"✅ Order execution summary for {symbol}:")
                    self.logger.info(f"   📈 Executed Price: ₹{executed_price:.2f} (Original: ₹{entry_price:.2f})")
                    self.logger.info(f"   🛑 Stop Loss: ₹{new_stop_loss:.2f} (Original: ₹{stop_loss:.2f})")
                    self.logger.info(f"   🎯 Target: ₹{new_target:.2f} (Original: ₹{target:.2f})")
                    self.logger.info(f"   💰 Risk: ₹{abs(executed_price - new_stop_loss) * quantity:.2f}")
                    self.logger.info(f"   💎 Reward: ₹{abs(new_target - executed_price) * quantity:.2f}")

                    # Calculate and log risk-reward ratio
                    risk_amount = abs(executed_price - new_stop_loss) * quantity
                    reward_amount = abs(new_target - executed_price) * quantity
                    rr_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
                    self.logger.info(f"   ⚖️ Risk-Reward Ratio: 1:{rr_ratio:.2f}")
                else:
                    self.logger.warning(f"⚠️ Could not fetch executed price for {symbol}, using original price: ₹{entry_price}")
                    self.logger.warning(f"⚠️ SL and target calculations may be inaccurate")
                    self.logger.warning(f"⚠️ Manual verification recommended for order {order.order_id}")
                    self.logger.warning(f"⚠️ Consider checking trade book manually for accurate execution price")

                self.logger.info(f"⏱️ Order timing recorded - next order allowed in {self.min_order_gap_seconds}s")
                return order
            else:
                self.logger.error(f"❌ Order placement failed: {response}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error placing order: {e}")
            return None
        finally:
            # Always unlock order placement
            self.order_placement_lock = False

    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get order status from SmartAPI"""
        if not self._authenticated:
            return None

        try:
            # Get order book
            order_book = self.smart_api.orderBook()

            if order_book.get('status') and order_book.get('data'):
                for order in order_book['data']:
                    if order.get('orderid') == order_id:
                        status = order.get('status', '').upper()
                        if status == 'COMPLETE':
                            return OrderStatus.COMPLETED
                        elif status == 'CANCELLED':
                            return OrderStatus.CANCELLED
                        elif status == 'REJECTED':
                            return OrderStatus.REJECTED
                        else:
                            return OrderStatus.OPEN

            return None

        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        if not self._authenticated:
            return False

        try:
            response = self.smart_api.cancelOrder(order_id, "NORMAL")

            if response.get('status'):
                self.logger.info(f"Order cancelled successfully: {order_id}")
                return True
            else:
                self.logger.error(f"Order cancellation failed: {response}")
                return False

        except Exception as e:
            self.logger.error(f"Error cancelling order: {e}")
            return False

    def get_profile(self) -> Optional[Dict[str, Any]]:
        """Get user profile"""
        if not self._authenticated:
            return None

        try:
            response = self.smart_api.getProfile(self.refresh_token)
            return response.get('data') if response.get('status') else None
        except Exception as e:
            self.logger.error(f"Error getting profile: {e}")
            return None

    def logout(self) -> bool:
        """Logout from SmartAPI"""
        if not self._authenticated:
            return True

        try:
            response = self.smart_api.terminateSession(self.config.username)
            self._authenticated = False
            self.logger.info("Logged out successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error during logout: {e}")
            return False

    def get_real_balance(self) -> Optional[Dict[str, float]]:
        """Get real account balance and limits from SmartAPI with rate limiting"""
        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before getting balance")
                return None

        try:
            # Get RMS data with rate limiting and retry logic
            rms_response = self._make_api_call_with_retry(self.smart_api.rmsLimit)

            if rms_response and rms_response.get('status') and rms_response.get('data'):
                rms_data = rms_response['data']

                # Extract balance information
                balance_info = {
                    'available_cash': float(rms_data.get('availablecash', 0)),
                    'available_margin': float(rms_data.get('availablemargin', 0)),
                    'collateral': float(rms_data.get('collateral', 0)),
                    'margin_used': float(rms_data.get('marginused', 0)),
                    'net_available': float(rms_data.get('net', 0)),
                    'total_collateral_value': float(rms_data.get('totalcollateralvalue', 0))
                }

                self.logger.info(f"✅ Real balance fetched: Available Cash: ₹{balance_info['available_cash']:,.2f}, "
                               f"Available Margin: ₹{balance_info['available_margin']:,.2f}")
                return balance_info
            else:
                self.logger.error(f"Failed to get RMS data: {rms_response}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting real balance: {e}")
            return None

    def get_real_positions(self) -> List[Dict]:
        """Get real positions from SmartAPI with rate limiting"""
        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before getting positions")
                return []

        try:
            # Get positions with rate limiting and retry logic
            positions_response = self._make_api_call_with_retry(self.smart_api.position)

            if positions_response and positions_response.get('status'):
                if positions_response.get('data'):
                    positions = positions_response['data']
                    self.logger.info(f"✅ Retrieved {len(positions)} real positions")
                    return positions
                else:
                    # This is a normal case - no positions found
                    self.logger.info("No open positions found in the account")
                    return []
            else:
                # This is an actual API error
                self.logger.warning(f"API error when getting positions: {positions_response}")
                return []

        except Exception as e:
            self.logger.error(f"Error getting real positions: {e}")
            return []

    def _get_current_market_price(self, symbol: str) -> Optional[float]:
        """Get current market price for a symbol"""
        try:
            # Remove -EQ suffix if present
            clean_symbol = symbol.replace('-EQ', '')

            # Try to get quote from SmartAPI
            if hasattr(self, 'smart_api') and self.smart_api:
                # Get symbol token first
                symbol_token = self._get_symbol_token(clean_symbol)
                if symbol_token:
                    quote_data = self.smart_api.ltpData("NSE", clean_symbol, symbol_token)
                    if quote_data and 'data' in quote_data:
                        ltp = quote_data['data'].get('ltp')
                        if ltp:
                            return float(ltp)

            return None

        except Exception as e:
            self.logger.debug(f"Error getting market price for {symbol}: {e}")
            return None

    def _get_symbol_token(self, symbol: str) -> Optional[str]:
        """Get symbol token for a symbol"""
        try:
            # This is a simplified version - in production you'd use a proper symbol lookup
            # For now, return None to skip this method
            return None
        except Exception:
            return None

    def get_open_positions(self) -> List[Order]:
        """Get all open positions from SmartAPI"""
        self.logger.info("🔍 Fetching open positions from SmartAPI...")
        real_positions = self.get_real_positions()

        if not real_positions:
            self.logger.info("ℹ️ No positions returned from SmartAPI")
            return []

        self.logger.info(f"📊 Processing {len(real_positions)} positions from SmartAPI...")

        # Convert SmartAPI positions to Order objects
        orders = []
        for i, pos in enumerate(real_positions):
            try:
                # Log raw position data for debugging
                self.logger.debug(f"📋 Position {i+1}: {pos}")

                # Only include positions with non-zero quantity
                net_qty_str = pos.get('netqty', '0')
                net_qty = int(net_qty_str) if net_qty_str != '' else 0

                self.logger.debug(f"📊 Position {i+1}: Symbol={pos.get('tradingsymbol', 'N/A')}, NetQty={net_qty}")

                if net_qty == 0:
                    self.logger.debug(f"⏭️ Skipping position with zero quantity: {pos.get('tradingsymbol', 'N/A')}")
                    continue

                # ENHANCED FIX: Use correct average price based on position type
                symbol_name = pos.get('tradingsymbol', 'UNKNOWN')
                order_id = pos.get('orderid', '')

                # Determine transaction type first to get correct average price
                transaction_type = TransactionType.BUY if net_qty > 0 else TransactionType.SELL

                # ENHANCED FIX: Try multiple methods to get valid entry price
                valid_price = None

                # Method 1: Use buyavgprice for long positions, sellavgprice for short positions (CORRECT METHOD)
                if transaction_type == TransactionType.BUY:
                    avg_price = pos.get('buyavgprice')
                    price_source = "buyavgprice"
                else:
                    avg_price = pos.get('sellavgprice')
                    price_source = "sellavgprice"

                self.logger.info(f"💰 Price retrieval for {symbol_name} (Qty: {net_qty}):")
                self.logger.info(f"   Position Type: {transaction_type.value}")
                self.logger.info(f"   Price Source: {price_source}")
                self.logger.info(f"   Raw Price Value: {avg_price}")

                if avg_price is not None and avg_price != '' and avg_price != 0 and avg_price != '0':
                    try:
                        price_float = float(avg_price)
                        if price_float > 0:
                            valid_price = price_float
                            self.logger.info(f"✅ Successfully using {price_source} for {symbol_name}: ₹{valid_price}")
                        else:
                            self.logger.warning(f"⚠️ {price_source} is zero or negative for {symbol_name}: {avg_price}")
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"⚠️ Invalid {price_source} value for {symbol_name}: {avg_price} - Error: {e}")
                        pass
                else:
                    self.logger.warning(f"⚠️ {price_source} not available for {symbol_name}: {avg_price}")

                # Method 1.5: Fallback to generic averageprice if buy/sell specific prices not available
                if not valid_price:
                    fallback_avg_price = pos.get('averageprice')
                    self.logger.info(f"🔄 Trying fallback averageprice for {symbol_name}: {fallback_avg_price}")

                    if fallback_avg_price is not None and fallback_avg_price != '' and fallback_avg_price != 0 and fallback_avg_price != '0':
                        try:
                            price_float = float(fallback_avg_price)
                            if price_float > 0:
                                valid_price = price_float
                                self.logger.info(f"✅ Using fallback averageprice for {symbol_name}: ₹{valid_price}")
                        except (ValueError, TypeError) as e:
                            self.logger.warning(f"⚠️ Invalid fallback averageprice for {symbol_name}: {fallback_avg_price} - Error: {e}")

                # Method 2: Try comprehensive price lookup (only if we have order_id and no valid price yet)
                if not valid_price and order_id and order_id.strip():
                    self.logger.info(f"🔄 Average price not available for {symbol_name}, attempting comprehensive price lookup...")
                    executed_price = self._get_executed_price_comprehensive(order_id, symbol_name, max_retries=3)
                    if executed_price and executed_price > 0:
                        valid_price = executed_price
                        self.logger.info(f"✅ Retrieved executed price via comprehensive lookup for {symbol_name}: ₹{valid_price}")

                # Method 3: Try to get current market price as fallback
                if not valid_price:
                    self.logger.warning(f"⚠️ Entry price not available for {symbol_name}, trying current market price...")

                    try:
                        # Try to get current market price from market data
                        current_price = self._get_current_market_price(symbol_name)
                        if current_price and current_price > 0:
                            valid_price = current_price
                            self.logger.info(f"✅ Using current market price for {symbol_name}: ₹{valid_price}")
                            self.logger.warning(f"⚠️ Note: Using current price as entry price - actual entry may differ")
                        else:
                            # Final emergency fallback
                            self.logger.warning(f"⚠️ EMERGENCY FALLBACK: Cannot get market price for {symbol_name}")
                            self.logger.warning(f"⚠️ Using emergency fallback price of ₹100 for {symbol_name}")
                            self.logger.warning(f"⚠️ MANUAL INTERVENTION REQUIRED: Check actual entry price for {symbol_name}")
                            valid_price = 100.0  # Emergency fallback - requires manual intervention
                    except Exception as e:
                        self.logger.error(f"❌ Error getting market price for {symbol_name}: {e}")
                        self.logger.warning(f"⚠️ Using emergency fallback price of ₹100 for {symbol_name}")
                        valid_price = 100.0

                # Final validation
                if not valid_price or valid_price <= 0:
                    self.logger.error(f"❌ CRITICAL: Invalid price for {symbol_name}: {valid_price} - skipping position")
                    continue

                avg_price_float = valid_price

                # Calculate stop loss and target based on actual executed price
                # (transaction_type already determined above)

                # Use intelligent SL and target calculation
                if transaction_type == TransactionType.BUY:
                    # For BUY: SL below entry, target above entry
                    # Use 1.5% SL and 3% target as default (2:1 risk-reward)
                    sl_percentage = 0.015  # 1.5% stop loss
                    target_percentage = 0.03  # 3% target
                    calculated_sl = avg_price_float * (1 - sl_percentage)
                    calculated_target = avg_price_float * (1 + target_percentage)
                else:
                    # For SELL: SL above entry, target below entry
                    sl_percentage = 0.015  # 1.5% stop loss
                    target_percentage = 0.03  # 3% target
                    calculated_sl = avg_price_float * (1 + sl_percentage)
                    calculated_target = avg_price_float * (1 - target_percentage)

                # Create Order object from position data
                order = Order(
                    order_id=pos.get('orderid', ''),
                    symbol=pos.get('tradingsymbol', '').replace('-EQ', ''),  # Remove -EQ suffix
                    symbol_token=pos.get('symboltoken', ''),
                    exchange=pos.get('exchange', ''),
                    transaction_type=transaction_type,
                    order_type=OrderType.MARKET,
                    product_type=ProductType.INTRADAY,
                    quantity=abs(net_qty),
                    price=avg_price_float,
                    stop_loss=calculated_sl,  # Calculated based on entry price
                    target=calculated_target,     # Calculated based on entry price
                    status=OrderStatus.COMPLETED,
                    entry_price=avg_price_float,  # Use validated price
                    strategy="EXISTING_POSITION"  # Strategy for existing positions
                )

                self.logger.info(f"✅ Created order for existing position {order.symbol}:")
                self.logger.info(f"   Entry: ₹{avg_price_float:.2f}, SL: ₹{calculated_sl:.2f}, Target: ₹{calculated_target:.2f}")

                orders.append(order)

            except Exception as e:
                symbol = pos.get('tradingsymbol', 'UNKNOWN')
                self.logger.error(f"Error converting position to order for {symbol}: {e}")
                # Log position data for debugging
                self.logger.debug(f"Position data: {pos}")
                continue

        # Only log if there are positions or at debug level
        if orders:
            self.logger.info(f"✅ Converted {len(orders)} real positions to Order objects")
        else:
            self.logger.debug("No open positions found to convert")
            
        return orders

    def _get_executed_price_from_trade_book(self, order_id: str, symbol: str, max_retries: int = 3) -> float:
        """
        Fetch executed price from trade book for a given order ID
        TradeBook is more reliable than OrderBook for getting executed trade details

        Args:
            order_id: Order ID to look up
            symbol: Symbol name for logging
            max_retries: Maximum number of retry attempts

        Returns:
            Executed price as float, or None if not found
        """
        import time

        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Cannot fetch trade book - authentication failed")
                return None

        # Skip if order_id is empty or None
        if not order_id or order_id.strip() == '':
            self.logger.warning(f"Cannot fetch executed price for {symbol} - order ID is empty")
            return None

        rate_limit_delay = 1.0  # Start with 1 second delay

        for attempt in range(max_retries):
            try:
                # Progressive rate limiting to avoid API limits
                if attempt > 0:
                    wait_time = rate_limit_delay * (2 ** attempt)  # Exponential backoff: 2s, 4s, 8s
                    self.logger.info(f"⏳ Rate limit wait: {wait_time}s for trade book lookup (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                else:
                    # Always wait at least 1 second to respect rate limits
                    time.sleep(rate_limit_delay)

                # Fetch trade book - this contains executed trades with average prices
                trade_book_response = self.smart_api.tradeBook()

                if not trade_book_response:
                    self.logger.warning(f"No response from trade book API on attempt {attempt + 1}")
                    continue

                # Handle rate limit errors specifically
                if trade_book_response.get('status') != True:
                    error_msg = trade_book_response.get('message', 'Unknown error')
                    if 'rate' in error_msg.lower() or 'access denied' in error_msg.lower():
                        self.logger.warning(f"Rate limit hit on attempt {attempt + 1}: {error_msg}")
                        rate_limit_delay *= 2  # Increase delay for next attempt
                        continue
                    else:
                        self.logger.warning(f"Trade book API error on attempt {attempt + 1}: {error_msg}")
                        continue

                trade_book_data = trade_book_response.get('data', [])

                # Look for our trade by order ID
                for trade in trade_book_data:
                    if trade.get('orderid') == order_id:
                        executed_price = trade.get('averageprice')
                        if executed_price and float(executed_price) > 0:
                            self.logger.info(f"✅ Found executed price from trade book for {symbol} (Order: {order_id}): ₹{executed_price}")
                            return float(executed_price)
                        else:
                            self.logger.debug(f"Trade found for order {order_id} but averageprice not available: {executed_price}")

                self.logger.debug(f"Order {order_id} not found in trade book on attempt {attempt + 1}")

            except Exception as e:
                error_str = str(e)
                if 'rate' in error_str.lower() or 'access denied' in error_str.lower():
                    self.logger.warning(f"Rate limit exception on attempt {attempt + 1}: {e}")
                    rate_limit_delay *= 2  # Increase delay for rate limit errors
                else:
                    self.logger.error(f"Error fetching trade book on attempt {attempt + 1}: {e}")

        self.logger.warning(f"Could not retrieve executed price from trade book for order {order_id} after {max_retries} attempts")
        return None

    def _get_executed_price_from_order_book(self, order_id: str, symbol: str, max_retries: int = 3) -> float:
        """
        Fetch executed price from order book for a given order ID
        This is a fallback method when trade book doesn't have the information

        Args:
            order_id: Order ID to look up
            symbol: Symbol name for logging
            max_retries: Maximum number of retry attempts

        Returns:
            Executed price as float, or None if not found
        """
        import time

        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Cannot fetch order book - authentication failed")
                return None

        # Skip if order_id is empty or None
        if not order_id or order_id.strip() == '':
            self.logger.warning(f"Cannot fetch executed price for {symbol} - order ID is empty")
            return None

        rate_limit_delay = 1.0  # Start with 1 second delay

        for attempt in range(max_retries):
            try:
                # Progressive rate limiting to avoid API limits
                if attempt > 0:
                    wait_time = rate_limit_delay * (2 ** attempt)  # Exponential backoff: 2s, 4s, 8s
                    self.logger.info(f"⏳ Rate limit wait: {wait_time}s for order book lookup (attempt {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                else:
                    # Always wait at least 1 second to respect rate limits
                    time.sleep(rate_limit_delay)

                # Fetch order book with rate limit handling
                order_book_response = self.smart_api.orderBook()

                if not order_book_response:
                    self.logger.warning(f"No response from order book API on attempt {attempt + 1}")
                    continue

                # Handle rate limit errors specifically
                if order_book_response.get('status') != True:
                    error_msg = order_book_response.get('message', 'Unknown error')
                    if 'rate' in error_msg.lower() or 'access denied' in error_msg.lower():
                        self.logger.warning(f"Rate limit hit on attempt {attempt + 1}: {error_msg}")
                        rate_limit_delay *= 2  # Increase delay for next attempt
                        continue
                    else:
                        self.logger.warning(f"Order book API error on attempt {attempt + 1}: {error_msg}")
                        continue

                order_book_data = order_book_response.get('data', [])

                # Look for our order
                for order in order_book_data:
                    if order.get('orderid') == order_id:
                        executed_price = order.get('averageprice')
                        if executed_price and float(executed_price) > 0:
                            self.logger.info(f"✅ Found executed price from order book for {symbol} (Order: {order_id}): ₹{executed_price}")
                            return float(executed_price)
                        else:
                            self.logger.debug(f"Order {order_id} found but averageprice still not available: {executed_price}")
                            break  # Order found but price not ready, try again

                self.logger.debug(f"Order {order_id} not found in order book on attempt {attempt + 1}")

            except Exception as e:
                error_str = str(e)
                if 'rate' in error_str.lower() or 'access denied' in error_str.lower():
                    self.logger.warning(f"Rate limit exception on attempt {attempt + 1}: {e}")
                    rate_limit_delay *= 2  # Increase delay for rate limit errors
                else:
                    self.logger.error(f"Error fetching order book on attempt {attempt + 1}: {e}")

        self.logger.warning(f"Could not retrieve executed price from order book for order {order_id} after {max_retries} attempts")
        return None

    def _get_executed_price_comprehensive(self, order_id: str, symbol: str, max_retries: int = 3) -> float:
        """
        Comprehensive method to get executed price using multiple approaches
        1. Try trade book first (most reliable for executed trades)
        2. Fallback to order book
        3. Fallback to current market price if needed

        Args:
            order_id: Order ID to look up
            symbol: Symbol name for logging
            max_retries: Maximum number of retry attempts

        Returns:
            Executed price as float, or None if not found
        """
        self.logger.info(f"🔍 Starting comprehensive price lookup for {symbol} (Order: {order_id})")
        self.logger.info(f"📋 Lookup strategy: TradeBook → OrderBook → MarketPrice")

        # Method 1: Try trade book first (most reliable)
        self.logger.info(f"📊 Method 1: Checking trade book for order {order_id}...")
        executed_price = self._get_executed_price_from_trade_book(order_id, symbol, max_retries)
        if executed_price and executed_price > 0:
            self.logger.info(f"✅ SUCCESS: Retrieved price from trade book: ₹{executed_price}")
            self.logger.info(f"📈 Trade book is the most reliable source for executed prices")
            return executed_price

        # Method 2: Fallback to order book
        self.logger.info(f"🔄 Method 2: Trade book lookup failed, trying order book for {symbol}")
        executed_price = self._get_executed_price_from_order_book(order_id, symbol, max_retries)
        if executed_price and executed_price > 0:
            self.logger.info(f"✅ SUCCESS: Retrieved price from order book: ₹{executed_price}")
            self.logger.info(f"📊 Order book provided the execution price")
            return executed_price

        # Method 3: Last resort - get current market price
        self.logger.warning(f"⚠️ Method 3: Both trade book and order book failed, using current market price for {symbol}")
        self.logger.warning(f"⚠️ This is a fallback and may not reflect actual execution price")
        market_price = self._get_current_market_price(symbol)
        if market_price and market_price > 0:
            self.logger.warning(f"⚠️ FALLBACK: Using current market price: ₹{market_price}")
            self.logger.warning(f"⚠️ Please verify actual execution price manually")
            return market_price

        self.logger.error(f"❌ CRITICAL: All methods failed to retrieve executed price for {symbol} (Order: {order_id})")
        self.logger.error(f"❌ This indicates a serious issue with price retrieval")
        self.logger.error(f"❌ Manual intervention required to determine actual execution price")
        return None

    def recalculate_sl_target_for_order(self, order: Order, original_entry_price: float,
                                       original_sl: float, original_target: float) -> bool:
        """
        Recalculate stop loss and target for an order based on actual executed price

        Args:
            order: Order object to update
            original_entry_price: Original entry price used for calculations
            original_sl: Original stop loss price
            original_target: Original target price

        Returns:
            True if recalculation was successful, False otherwise
        """
        try:
            if not order.entry_price or order.entry_price <= 0:
                self.logger.warning(f"Cannot recalculate SL/target for {order.symbol} - no valid entry price")
                return False

            # Calculate original distances
            original_sl_distance = abs(original_entry_price - original_sl)
            original_target_distance = abs(original_target - original_entry_price)

            # Calculate new SL and target based on actual executed price
            if order.transaction_type == TransactionType.BUY:
                # For BUY orders: SL below executed price, target above
                new_stop_loss = order.entry_price - original_sl_distance
                new_target = order.entry_price + original_target_distance
            else:
                # For SELL orders: SL above executed price, target below
                new_stop_loss = order.entry_price + original_sl_distance
                new_target = order.entry_price - original_target_distance

            # Update order with new values
            order.stop_loss = new_stop_loss
            order.target = new_target

            self.logger.info(f"✅ Recalculated SL/Target for {order.symbol}:")
            self.logger.info(f"   Entry: ₹{order.entry_price:.2f} (was ₹{original_entry_price:.2f})")
            self.logger.info(f"   SL: ₹{new_stop_loss:.2f} (was ₹{original_sl:.2f})")
            self.logger.info(f"   Target: ₹{new_target:.2f} (was ₹{original_target:.2f})")

            return True

        except Exception as e:
            self.logger.error(f"Error recalculating SL/target for {order.symbol}: {e}")
            return False

    def square_off_position(self, order: Order, exit_price: float, exit_reason: str) -> bool:
        """Square off a real position using SmartAPI"""
        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before squaring off position")
                return False

        try:
            # Determine opposite transaction type for square off
            square_off_type = "SELL" if order.transaction_type == TransactionType.BUY else "BUY"

            # Prepare square off order parameters
            square_off_params = {
                "variety": "NORMAL",
                "tradingsymbol": f"{order.symbol}-EQ",
                "symboltoken": order.symbol_token,
                "transactiontype": square_off_type,
                "exchange": order.exchange,
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",  # Market order
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(order.quantity)
            }

            self.logger.info(f"🔄 Squaring off position: {order.symbol} {square_off_type} {order.quantity} @ Market Price")

            # Place square off order
            response = self.smart_api.placeOrderFullResponse(square_off_params)

            if response.get('status') and response.get('data'):
                square_off_order_id = response['data'].get('orderid')
                self.logger.info(f"✅ Square off order placed successfully: {square_off_order_id}")
                self.logger.info(f"📊 Reason: {exit_reason}")
                return True
            else:
                self.logger.error(f"❌ Square off order failed: {response}")
                return False

        except Exception as e:
            self.logger.error(f"Error squaring off position {order.order_id}: {e}")
            return False

    def get_real_holdings(self) -> List[Dict]:
        """Get real holdings from SmartAPI"""
        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before getting holdings")
                return []

        try:
            # Get holdings from SmartAPI
            holdings_response = self.smart_api.holding()

            if holdings_response.get('status') and holdings_response.get('data'):
                holdings = holdings_response['data']
                self.logger.info(f"✅ Retrieved {len(holdings)} real holdings")
                return holdings
            else:
                self.logger.warning(f"No holdings found or API error: {holdings_response}")
                return []

        except Exception as e:
            self.logger.error(f"Error getting real holdings: {e}")
            return []

    def get_account_summary(self) -> Dict[str, Any]:
        """Get comprehensive account summary with real balance, positions, and holdings"""
        if not self._authenticated:
            if not self.authenticate():
                self.logger.error("Failed to authenticate before getting account summary")
                return {}

        try:
            # Get all account data
            balance_info = self.get_real_balance()
            positions = self.get_real_positions()
            holdings = self.get_real_holdings()

            # Calculate portfolio value
            total_position_value = 0.0
            total_holding_value = 0.0

            for pos in positions:
                qty = int(pos.get('netqty', 0))
                avg_price = float(pos.get('avgprice', 0))
                total_position_value += abs(qty) * avg_price

            for holding in holdings:
                qty = int(holding.get('quantity', 0))
                ltp = float(holding.get('ltp', 0))
                total_holding_value += qty * ltp

            summary = {
                'balance': balance_info or {},
                'positions_count': len([p for p in positions if int(p.get('netqty', 0)) != 0]),
                'holdings_count': len(holdings),
                'total_position_value': total_position_value,
                'total_holding_value': total_holding_value,
                'total_portfolio_value': total_position_value + total_holding_value,
                'available_cash': balance_info.get('available_cash', 0) if balance_info else 0,
                'available_margin': balance_info.get('available_margin', 0) if balance_info else 0
            }

            self.logger.info(f"📊 Account Summary: Cash: ₹{summary['available_cash']:,.2f}, "
                           f"Positions: {summary['positions_count']}, Holdings: {summary['holdings_count']}")

            return summary

        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}


class PaperTradingOrderService(OrderServiceInterface):
    """Paper trading implementation with realistic transaction costs and market hours validation"""

    def __init__(self, logger: logging.Logger, initial_balance: float = 80000.0):
        self.logger = logger
        self.order_counter = 1
        self.orders: Dict[str, Order] = {}

        # Virtual balance tracking with realistic transaction costs
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.invested_amount = 0.0  # Amount currently invested in open positions
        self.total_pnl = 0.0  # Total realized P&L
        self.total_transaction_costs = 0.0  # Total transaction costs paid

        # Market hours validation
        self.market_open_time = time(9, 15)  # 9:15 AM
        self.market_close_time = time(15, 30)  # 3:30 PM

        self.logger.info(f"💰 Paper Trading initialized with virtual balance: ₹{self.initial_balance:,.2f}")
        self.logger.info(f"📊 Realistic transaction costs enabled (Angel One rates)")
        self.logger.info(f"⏰ Market hours validation: {self.market_open_time.strftime('%H:%M')} - {self.market_close_time.strftime('%H:%M')} IST")

    def place_order(
        self,
        symbol: str,
        token: str,
        exchange: str,
        transaction_type: TransactionType,
        entry_price: float,
        stop_loss: float,
        target: float,
        quantity: int
    ) -> Optional[Order]:
        """Place a paper trading order with realistic transaction costs and market hours validation"""

        # Enhanced market hours validation with detailed logging
        self.logger.info(f"PAPER_ORDER_VALIDATION: Attempting to place paper order for {symbol} {getattr(transaction_type, 'value', transaction_type)}")
        self.logger.info(f"PAPER_ORDER_DETAILS: Price: {entry_price}, Qty: {quantity}, SL: {stop_loss}, Target: {target}")

        if not is_market_open():
            self.logger.error(f"❌ PAPER_ORDER_REJECTED: Cannot place order for {symbol}: Market is closed!")
            self.logger.error("⚠️ PAPER_ORDER_REJECTION_REASON: Orders can only be placed during market hours (9:15 AM - 3:30 PM IST, Monday-Friday)")
            self.logger.error(f"PAPER_ORDER_REJECTION_DETAILS: Symbol: {symbol}, Type: {getattr(transaction_type, 'value', transaction_type)}, Price: {entry_price}")
            return None

        self.logger.info(f"✅ PAPER_ORDER_VALIDATION_PASSED: Market is open, proceeding with paper order placement for {symbol}")

        # Calculate order value and entry transaction costs
        order_value = entry_price * quantity
        entry_transaction_cost = TransactionCosts.calculate_entry_cost_only(entry_price, quantity, is_intraday=True)
        total_required_capital = order_value + entry_transaction_cost

        # Check if we have sufficient balance for BUY orders
        if transaction_type == TransactionType.BUY:
            available_balance = self.current_balance
            if total_required_capital > available_balance:
                self.logger.error(f"❌ Insufficient balance for {symbol}: Required ₹{total_required_capital:,.2f} "
                                f"(Order: ₹{order_value:,.2f} + Costs: ₹{entry_transaction_cost:.2f}), "
                                f"Available ₹{available_balance:,.2f}")
                return None

        # Create order with simulated order ID
        order_id = f"PAPER_{self.order_counter:06d}"
        self.order_counter += 1

        order = Order(
            order_id=order_id,
            symbol=symbol,
            symbol_token=token,
            exchange=exchange,
            transaction_type=transaction_type,
            order_type=OrderType.MARKET,
            product_type=ProductType.INTRADAY,
            quantity=quantity,
            price=entry_price,
            stop_loss=stop_loss,
            target=target,
            status=OrderStatus.COMPLETED,  # Assume immediate execution in paper trading
            entry_price=entry_price,
            strategy="PAPER_TRADING"  # Strategy for paper trading orders
        )

        # Store entry transaction cost in order for later reference
        setattr(order, 'entry_transaction_cost', entry_transaction_cost)

        # Update balance for BUY orders (deduct order value + transaction costs)
        if transaction_type == TransactionType.BUY:
            self.current_balance -= total_required_capital
            self.invested_amount += order_value  # Only order value, not transaction costs
            self.total_transaction_costs += entry_transaction_cost
        # For SELL orders (short selling), we don't deduct balance but track the position

        self.orders[order_id] = order

        self.logger.info(f"💳 Paper trading order placed: {order_id} - {getattr(transaction_type, 'value', transaction_type)} {quantity} {symbol} @ ₹{entry_price:.2f}")
        self.logger.info(f"💰 Transaction costs: ₹{entry_transaction_cost:.2f} (Entry brokerage, stamp duty, exchange charges, GST)")
        self.logger.info(f"💰 Balance Update: Available ₹{self.current_balance:,.2f}, Invested ₹{self.invested_amount:,.2f}, "
                        f"Total Costs: ₹{self.total_transaction_costs:.2f}")

        return order

    def get_order_status(self, order_id: str) -> Optional[OrderStatus]:
        """Get paper trading order status"""
        order = self.orders.get(order_id)
        return order.status if order else None

    def cancel_order(self, order_id: str) -> bool:
        """Cancel a paper trading order"""
        if order_id in self.orders:
            self.orders[order_id].status = OrderStatus.CANCELLED
            self.logger.info(f"Paper trading order cancelled: {order_id}")
            return True
        return False

    def get_open_positions(self) -> List[Order]:
        """Get all open positions (completed orders that haven't been squared off)"""
        return [order for order in self.orders.values()
                if order.status == OrderStatus.COMPLETED and not (hasattr(order, 'squared_off') and order.squared_off)]

    def square_off_position(self, order: Order, exit_price: float, exit_reason: str) -> bool:
        """Square off a position with realistic transaction costs calculation"""
        try:
            # Check if already squared off
            if hasattr(order, 'squared_off') and order.squared_off:
                self.logger.debug(f"Position {order.order_id} already squared off")
                return False

            # Calculate complete transaction costs for the round trip
            entry_transaction_cost = getattr(order, 'entry_transaction_cost', 0.0)
            cost_breakdown = TransactionCosts.calculate_total_cost(
                order.entry_price, exit_price, order.quantity, is_intraday=True
            )

            # Extract exit-specific costs
            exit_transaction_cost = cost_breakdown['exit_brokerage'] + cost_breakdown['stt']
            total_transaction_cost = cost_breakdown['total_cost']

            # Calculate gross P&L (before transaction costs)
            if order.transaction_type == TransactionType.BUY:
                gross_pnl = (exit_price - order.entry_price) * order.quantity
            else:  # SELL (short selling)
                gross_pnl = (order.entry_price - exit_price) * order.quantity

            # Calculate net P&L (after all transaction costs)
            net_pnl = gross_pnl - total_transaction_cost

            # Calculate the original investment amount
            original_investment = order.entry_price * order.quantity

            # Update balance based on transaction type
            if order.transaction_type == TransactionType.BUY:
                # Return the original investment plus net P&L minus exit costs
                exit_value = exit_price * order.quantity
                self.current_balance += exit_value - exit_transaction_cost
                self.invested_amount -= original_investment
            else:  # SELL (short selling)
                # For short positions, add the net P&L to balance
                self.current_balance += net_pnl

            # Update total realized P&L and transaction costs
            self.total_pnl += net_pnl
            self.total_transaction_costs += exit_transaction_cost

            # Mark as squared off with detailed cost information
            setattr(order, 'squared_off', True)
            setattr(order, 'exit_price', exit_price)
            setattr(order, 'exit_reason', exit_reason)
            setattr(order, 'gross_pnl', gross_pnl)
            setattr(order, 'net_pnl', net_pnl)
            setattr(order, 'exit_transaction_cost', exit_transaction_cost)
            setattr(order, 'total_transaction_cost', total_transaction_cost)
            order.status = OrderStatus.SQUARED_OFF

            self.logger.info(f"💳 Position squared off: {order.order_id} - {order.symbol} @ ₹{exit_price:.2f}")
            self.logger.info(f"📊 P&L Details: Gross: ₹{gross_pnl:.2f}, Net: ₹{net_pnl:.2f} (after ₹{total_transaction_cost:.2f} costs)")
            self.logger.info(f"💰 Transaction Cost Breakdown: Entry: ₹{entry_transaction_cost:.2f}, Exit: ₹{exit_transaction_cost:.2f}")
            self.logger.info(f"💰 Balance Update: Available ₹{self.current_balance:,.2f}, Invested ₹{self.invested_amount:,.2f}, "
                           f"Total Net P&L: ₹{self.total_pnl:,.2f}, Total Costs: ₹{self.total_transaction_costs:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"Error squaring off position {order.order_id}: {e}")
            return False

    def get_balance_info(self) -> Dict[str, float]:
        """Get current balance information with transaction cost details"""
        total_portfolio_value = self.current_balance + self.invested_amount
        return {
            'initial_balance': self.initial_balance,
            'current_balance': self.current_balance,
            'invested_amount': self.invested_amount,
            'total_portfolio_value': total_portfolio_value,
            'total_pnl': self.total_pnl,
            'total_transaction_costs': self.total_transaction_costs,
            'portfolio_return_pct': ((total_portfolio_value - self.initial_balance) / self.initial_balance) * 100,
            'cost_impact_pct': (self.total_transaction_costs / self.initial_balance) * 100 if self.initial_balance > 0 else 0
        }

    def log_balance_summary(self):
        """Log a summary of current balance status with transaction cost impact"""
        balance_info = self.get_balance_info()
        self.logger.info("💰 PAPER TRADING BALANCE SUMMARY (WITH REALISTIC COSTS)")
        self.logger.info("=" * 55)
        self.logger.info(f"Initial Balance:        ₹{balance_info['initial_balance']:>10,.2f}")
        self.logger.info(f"Available Balance:      ₹{balance_info['current_balance']:>10,.2f}")
        self.logger.info(f"Invested Amount:        ₹{balance_info['invested_amount']:>10,.2f}")
        self.logger.info(f"Total Portfolio:        ₹{balance_info['total_portfolio_value']:>10,.2f}")
        self.logger.info(f"Net P&L (after costs):  ₹{balance_info['total_pnl']:>10,.2f}")
        self.logger.info(f"Total Transaction Costs:₹{balance_info['total_transaction_costs']:>10,.2f}")
        self.logger.info(f"Portfolio Return:       {balance_info['portfolio_return_pct']:>9.2f}%")
        self.logger.info(f"Cost Impact:            {balance_info['cost_impact_pct']:>9.2f}%")
        self.logger.info("=" * 55)

    def restore_positions(self, positions_data: List[Dict]):
        """
        Restore open positions from log data after restart/power failure

        Args:
            positions_data: List of position dictionaries from logs
        """
        try:
            from models.order import Order, OrderStatus, TransactionType, OrderType, ProductType

            restored_invested_amount = 0.0

            for pos_data in positions_data:
                # Create Order object from log data
                order = Order(
                    order_id=pos_data['order_id'],
                    symbol=pos_data['symbol'],
                    symbol_token="",  # Will be filled by market data service if needed
                    exchange="NSE",
                    transaction_type=TransactionType.BUY if pos_data.get('action') == 'ORDER_PLACED' else TransactionType.SELL,
                    order_type=OrderType.MARKET,
                    product_type=ProductType.INTRADAY,
                    quantity=pos_data['quantity'],
                    price=pos_data['price'],
                    stop_loss=pos_data['stop_loss'],
                    target=pos_data['target'],
                    status=OrderStatus.COMPLETED,
                    entry_price=pos_data['price'],
                    strategy=pos_data.get('strategy', 'RESTORED_POSITION')  # Use strategy from data or default
                )

                # Calculate invested amount for this position
                if order.transaction_type == TransactionType.BUY:
                    position_investment = order.price * order.quantity
                    restored_invested_amount += position_investment

                # Add to orders dictionary
                self.orders[pos_data['order_id']] = order

                self.logger.info(f"Restored position: {order.symbol} {order.quantity} @ ₹{order.price:.2f}")

            # Update balance to reflect restored positions
            self.invested_amount = restored_invested_amount
            self.current_balance = self.initial_balance - self.invested_amount

            self.logger.info(f"✅ Successfully restored {len(positions_data)} positions")
            self.logger.info(f"💰 Restored balance state: Available ₹{self.current_balance:,.2f}, "
                           f"Invested ₹{self.invested_amount:,.2f}")

        except Exception as e:
            self.logger.error(f"Error restoring positions: {e}")
