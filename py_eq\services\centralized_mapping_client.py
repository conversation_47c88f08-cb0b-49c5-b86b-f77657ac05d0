"""
Centralized Mapping Client
A lightweight client for accessing symbol mappings from MongoDB
This replaces all the distributed symbol mapping dictionaries
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
import pymongo
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class CentralizedMappingClient:
    """Lightweight client for accessing centralized symbol mappings"""

    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

        # MongoDB connection
        self.mongodb_client = None
        self.db = None
        self.mongodb_connected = False

        # Collections
        self.equity_collection = None
        self.option_collection = None
        self.index_collection = None

        # Cache for frequently accessed mappings
        self._equity_cache = {}
        self._option_cache = {}
        self._index_cache = {}

        # Connect to MongoDB
        self._connect_mongodb()

    def _connect_mongodb(self):
        """Connect to MongoDB"""
        try:
            connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017/")
            # Use the same database as the trading system
            database_name = os.getenv("MONGODB_DATABASE_NAME", os.getenv("DB_NAME", "intraday_trading"))

            self.mongodb_client = MongoClient(connection_string)
            self.db = self.mongodb_client[database_name]

            # Test connection
            self.mongodb_client.admin.command('ping')
            self.mongodb_connected = True

            # Initialize collections
            self.equity_collection = self.db['symbol_mappings_equity']
            self.option_collection = self.db['symbol_mappings_options']
            self.index_collection = self.db['symbol_mappings_indices']

            self.logger.debug("✅ Connected to MongoDB for centralized mappings")

        except Exception as e:
            self.logger.warning(f"⚠️ MongoDB connection failed: {e}. Will use fallback mappings.")
            self.mongodb_connected = False

    def get_smartapi_token(self, symbol: str, instrument_type: str = 'EQ') -> Optional[Tuple[str, str]]:
        """
        Get SmartAPI token and exchange for a symbol

        Args:
            symbol: Symbol name (e.g., 'RELIANCE', 'BANKNIFTY26JUN2555000CE')
            instrument_type: 'EQ' for equity, 'OPTION' for options, 'INDEX' for indices

        Returns:
            Tuple of (token, exchange) or None if not found
        """
        if not self.mongodb_connected:
            return self._get_fallback_token(symbol, instrument_type)

        try:
            # Check cache first
            cache_key = f"{symbol}_{instrument_type}"

            if instrument_type == 'EQ':
                if cache_key in self._equity_cache:
                    return self._equity_cache[cache_key]

                result = self.equity_collection.find_one({'symbol': symbol})
                if result:
                    token_info = (result['smartapi_token'], result['exchange'])
                    self._equity_cache[cache_key] = token_info
                    return token_info

            elif instrument_type == 'OPTION':
                if cache_key in self._option_cache:
                    return self._option_cache[cache_key]

                result = self.option_collection.find_one({'symbol': symbol})
                if result:
                    token_info = (result['smartapi_token'], result['exchange'])
                    self._option_cache[cache_key] = token_info
                    return token_info

            elif instrument_type == 'INDEX':
                if cache_key in self._index_cache:
                    return self._index_cache[cache_key]

                result = self.index_collection.find_one({'symbol': symbol})
                if result:
                    token_info = (result['smartapi_token'], result['exchange'])
                    self._index_cache[cache_key] = token_info
                    return token_info

            # If not found in MongoDB, try fallback
            return self._get_fallback_token(symbol, instrument_type)

        except Exception as e:
            self.logger.warning(f"⚠️ Error getting token for {symbol}: {e}")
            return self._get_fallback_token(symbol, instrument_type)

    def get_yahoo_symbol(self, symbol: str) -> Optional[str]:
        """Get Yahoo Finance symbol for equity"""
        if not self.mongodb_connected:
            return f"{symbol}.NS"  # Default fallback

        try:
            # Check cache first
            if symbol in self._equity_cache:
                cached_data = self._equity_cache[symbol]
                if isinstance(cached_data, dict):
                    return cached_data.get('yahoo_symbol')

            result = self.equity_collection.find_one({'symbol': symbol})
            if result:
                yahoo_symbol = result.get('yahoo_symbol', f"{symbol}.NS")
                # Cache the full result
                self._equity_cache[symbol] = result
                return yahoo_symbol

            # Fallback
            return f"{symbol}.NS"

        except Exception as e:
            self.logger.warning(f"⚠️ Error getting Yahoo symbol for {symbol}: {e}")
            return f"{symbol}.NS"

    def get_banknifty_option_token(self, strike: float, option_type: str, expiry_string: str = None) -> Optional[Tuple[str, str]]:
        """Get Bank Nifty option token by strike, type, and expiry"""
        if not self.mongodb_connected:
            return None

        try:
            query = {
                'underlying': 'BANKNIFTY',
                'strike': strike,
                'option_type': option_type
            }

            if expiry_string:
                query['expiry_string'] = expiry_string
            else:
                # Get nearest expiry if not specified
                query = {
                    'underlying': 'BANKNIFTY',
                    'strike': strike,
                    'option_type': option_type
                }

            result = self.option_collection.find_one(query, sort=[('expiry', 1)])
            if result:
                return (result['smartapi_token'], result['exchange'])

            return None

        except Exception as e:
            self.logger.warning(f"⚠️ Error getting option token: {e}")
            return None

    def get_available_strikes_for_expiry(self, expiry_string: str, underlying: str = 'BANKNIFTY') -> List[float]:
        """Get available strikes for a specific expiry"""
        if not self.mongodb_connected:
            return []

        try:
            pipeline = [
                {'$match': {'underlying': underlying, 'expiry_string': expiry_string}},
                {'$group': {'_id': '$strike'}},
                {'$sort': {'_id': 1}}
            ]

            results = list(self.option_collection.aggregate(pipeline))
            strikes = [result['_id'] for result in results]
            return strikes

        except Exception as e:
            self.logger.warning(f"⚠️ Error getting strikes for {expiry_string}: {e}")
            return []

    def get_available_expiries(self, underlying: str = 'BANKNIFTY') -> List[str]:
        """Get available expiries for an underlying"""
        if not self.mongodb_connected:
            return []

        try:
            pipeline = [
                {'$match': {'underlying': underlying}},
                {'$group': {'_id': '$expiry_string'}},
                {'$sort': {'_id': 1}}
            ]

            results = list(self.option_collection.aggregate(pipeline))
            expiries = [result['_id'] for result in results]
            return expiries

        except Exception as e:
            self.logger.warning(f"⚠️ Error getting expiries for {underlying}: {e}")
            return []

    def _get_fallback_token(self, symbol: str, instrument_type: str) -> Optional[Tuple[str, str]]:
        """Fallback token mapping for critical symbols"""
        fallback_mappings = {
            'EQ': {
                'RELIANCE': ('2885', 'NSE'),
                'HDFCBANK': ('1333', 'NSE'),
                'INFY': ('1594', 'NSE'),
                'TCS': ('11536', 'NSE'),
                'ICICIBANK': ('4963', 'NSE'),
                'SBIN': ('3045', 'NSE'),
                'BHARTIARTL': ('10604', 'NSE'),
                'ITC': ('424', 'NSE'),
                'KOTAKBANK': ('492', 'NSE'),
                'LT': ('11483', 'NSE'),
            },
            'INDEX': {
                'BANKNIFTY': ('********', 'NSE'),
                'NIFTY': ('********', 'NSE'),
            }
        }

        return fallback_mappings.get(instrument_type, {}).get(symbol)

    def is_connected(self) -> bool:
        """Check if MongoDB connection is active"""
        return self.mongodb_connected

    def get_connection_status(self) -> Dict:
        """Get detailed connection status"""
        status = {
            'mongodb_connected': self.mongodb_connected,
            'cache_sizes': {
                'equity': len(self._equity_cache),
                'option': len(self._option_cache),
                'index': len(self._index_cache)
            }
        }

        if self.mongodb_connected:
            try:
                status['collections'] = {
                    'equity_count': self.equity_collection.count_documents({}),
                    'option_count': self.option_collection.count_documents({}),
                    'index_count': self.index_collection.count_documents({})
                }
            except Exception as e:
                status['error'] = str(e)

        return status

    def close(self):
        """Close MongoDB connection"""
        if self.mongodb_client:
            self.mongodb_client.close()
            self.logger.debug("✅ MongoDB connection closed")
