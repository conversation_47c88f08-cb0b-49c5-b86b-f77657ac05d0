"""
MongoDB service for storing and retrieving historical market data
"""
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import pymongo
from pymongo import MongoClient
from pymongo.collection import Collection

from models.candle import Candle


class MongoDBService:
    """MongoDB service for historical data storage and retrieval"""

    def __init__(self, connection_string: str, database_name: str, logger: logging.Logger):
        self.connection_string = connection_string
        self.database_name = database_name
        self.logger = logger
        self.client: Optional[MongoClient] = None
        self.db = None
        self.connected = False

    def connect(self) -> bool:
        """Connect to MongoDB"""
        try:
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.database_name]

            # Test connection
            self.client.admin.command('ping')
            self.connected = True
            self.logger.info(f"Connected to MongoDB: {self.database_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to MongoDB: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            self.connected = False
            self.logger.info("Disconnected from MongoDB")

    def get_collection(self, symbol: str, timeframe: str) -> Optional[Collection]:
        """Get collection for a specific symbol and timeframe"""
        if not self.connected or self.db is None:
            return None

        collection_name = f"{symbol}_{timeframe}"
        return self.db[collection_name]

    def store_candles(self, symbol: str, timeframe: str, candles: List[Candle]) -> bool:
        """Store candles in MongoDB"""
        if not self.connected:
            self.logger.error("Not connected to MongoDB")
            return False

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return False

            # Convert candles to documents
            documents = []
            for candle in candles:
                doc = candle.to_dict()
                doc['_id'] = f"{symbol}_{timeframe}_{candle.timestamp.isoformat()}"
                documents.append(doc)

            # Insert with upsert to avoid duplicates
            for doc in documents:
                collection.replace_one(
                    {'_id': doc['_id']},
                    doc,
                    upsert=True
                )

            self.logger.info(f"Stored {len(documents)} candles for {symbol} {timeframe}")
            return True

        except Exception as e:
            self.logger.error(f"Error storing candles: {e}")
            return False

    def get_historical_candles(
        self,
        symbol: str,
        timeframe: str,
        start_time: datetime,
        end_time: datetime
    ) -> List[Candle]:
        """Retrieve historical candles from MongoDB"""
        if not self.connected:
            self.logger.error("Not connected to MongoDB")
            return []

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return []

            # Ensure timezone-naive datetime objects for comparison
            if start_time.tzinfo is not None:
                start_time = start_time.replace(tzinfo=None)
            if end_time.tzinfo is not None:
                end_time = end_time.replace(tzinfo=None)

            # Query documents
            query = {
                'timestamp': {
                    '$gte': start_time.isoformat(),
                    '$lte': end_time.isoformat()
                }
            }

            cursor = collection.find(query).sort('timestamp', 1)

            # Convert documents to candles
            candles = []
            for doc in cursor:
                try:
                    # Ensure timestamp is timezone-naive
                    if 'timestamp' in doc:
                        if isinstance(doc['timestamp'], str):
                            # Parse ISO string and make timezone-naive
                            timestamp = datetime.fromisoformat(doc['timestamp'].replace('Z', '+00:00'))
                            if timestamp.tzinfo is not None:
                                timestamp = timestamp.replace(tzinfo=None)
                            doc['timestamp'] = timestamp
                        elif hasattr(doc['timestamp'], 'tzinfo') and doc['timestamp'].tzinfo is not None:
                            # Make timezone-aware datetime timezone-naive
                            doc['timestamp'] = doc['timestamp'].replace(tzinfo=None)

                    candle = Candle.from_dict(doc)
                    candles.append(candle)
                except Exception as e:
                    self.logger.warning(f"Error converting document to candle: {e}")
                    continue

            self.logger.info(f"Retrieved {len(candles)} candles for {symbol} {timeframe}")
            return candles

        except Exception as e:
            self.logger.error(f"Error retrieving candles: {e}")
            return []

    def get_latest_candle(self, symbol: str, timeframe: str) -> Optional[Candle]:
        """Get the latest candle for a symbol and timeframe"""
        if not self.connected:
            return None

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return None

            # Get the latest document
            doc = collection.find_one(sort=[('timestamp', -1)])

            if doc:
                return Candle.from_dict(doc)

            return None

        except Exception as e:
            self.logger.error(f"Error getting latest candle: {e}")
            return None

    def get_candles_count(self, symbol: str, timeframe: str) -> int:
        """Get the count of candles for a symbol and timeframe"""
        if not self.connected:
            return 0

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return 0

            return collection.count_documents({})

        except Exception as e:
            self.logger.error(f"Error getting candles count: {e}")
            return 0

    def delete_old_candles(self, symbol: str, timeframe: str, days_to_keep: int = 90) -> bool:
        """Delete candles older than specified days"""
        if not self.connected:
            return False

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return False

            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            result = collection.delete_many({
                'timestamp': {'$lt': cutoff_date.isoformat()}
            })

            self.logger.info(f"Deleted {result.deleted_count} old candles for {symbol} {timeframe}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting old candles: {e}")
            return False

    def create_indexes(self, symbol: str, timeframe: str) -> bool:
        """Create indexes for better query performance"""
        if not self.connected:
            return False

        try:
            collection = self.get_collection(symbol, timeframe)
            if collection is None:
                return False

            # Create index on timestamp
            collection.create_index([('timestamp', 1)])

            self.logger.info(f"Created indexes for {symbol} {timeframe}")
            return True

        except Exception as e:
            self.logger.error(f"Error creating indexes: {e}")
            return False

    def get_data_gap(self, symbol: str, timeframe: str, days: int = 30) -> Optional[datetime]:
        """Find the earliest missing data point within the specified days"""
        if not self.connected:
            return None

        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)

            # Get existing candles
            existing_candles = self.get_historical_candles(symbol, timeframe, start_time, end_time)

            if not existing_candles:
                return start_time

            # Sort by timestamp
            existing_candles.sort(key=lambda x: x.timestamp)

            # Calculate expected interval
            if timeframe == "5min":
                interval = timedelta(minutes=5)
            elif timeframe == "15min":
                interval = timedelta(minutes=15)
            else:
                interval = timedelta(minutes=15)  # Default

            # Find gaps
            current_time = start_time
            for candle in existing_candles:
                if candle.timestamp > current_time + interval:
                    # Found a gap
                    return current_time
                current_time = candle.timestamp + interval

            # Check if we need data after the last candle
            if existing_candles and existing_candles[-1].timestamp < end_time - interval:
                return existing_candles[-1].timestamp + interval

            return None

        except Exception as e:
            self.logger.error(f"Error finding data gap: {e}")
            return None
