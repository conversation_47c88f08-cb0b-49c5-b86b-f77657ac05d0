"""
Enhanced Stop-Loss Configuration
Centralized configuration for optimized SL mechanism
"""
from dataclasses import dataclass
from typing import Dict, List, Optional
import os


@dataclass
class StopLossConfig:
    """Enhanced stop-loss configuration"""
    # Execution settings
    max_slippage_percent: float = 0.5  # Maximum allowed slippage
    emergency_exit_threshold: float = 2.0  # Emergency exit if loss exceeds this %
    retry_attempts: int = 3  # Number of retry attempts for failed orders
    retry_delay_ms: int = 100  # Delay between retries in milliseconds
    pre_auth_enabled: bool = True  # Pre-authenticate API sessions
    parallel_execution: bool = True  # Execute multiple exits in parallel
    
    # Performance thresholds
    max_execution_time_ms: float = 500.0  # Alert if execution takes longer
    target_execution_time_ms: float = 200.0  # Target execution time
    max_daily_slippage_percent: float = 1.0  # Alert if daily slippage exceeds
    
    # Risk management
    circuit_breaker_loss_percent: float = 5.0  # Stop all trading if portfolio loss exceeds
    max_positions_at_risk: int = 3  # Maximum positions allowed in HIGH/CRITICAL risk
    volatility_threshold: float = 3.0  # High volatility threshold (%)
    
    # Monitoring settings
    price_update_frequency_ms: int = 100  # How often to check prices
    performance_log_interval: int = 300  # Log performance every N seconds
    risk_assessment_interval: int = 60  # Assess risk every N seconds


@dataclass
class OrderExecutionConfig:
    """Configuration for optimized order execution"""
    max_concurrent_orders: int = 5
    order_timeout_seconds: int = 10
    retry_attempts: int = 3
    retry_delay_ms: int = 100
    pre_auth_interval_minutes: int = 30
    connection_pool_size: int = 3
    
    # API rate limiting
    max_orders_per_second: int = 10
    burst_capacity: int = 20
    
    # Connection management
    connection_timeout_seconds: int = 5
    read_timeout_seconds: int = 10
    max_retries_per_connection: int = 3


@dataclass
class MonitoringConfig:
    """Configuration for enhanced monitoring"""
    enable_real_time_alerts: bool = True
    alert_on_slow_execution: bool = True
    alert_on_high_slippage: bool = True
    alert_on_failed_exits: bool = True
    
    # Logging levels
    log_all_price_updates: bool = False  # Can be very verbose
    log_execution_timing: bool = True
    log_risk_assessments: bool = True
    log_performance_metrics: bool = True
    
    # Dashboard settings
    enable_web_dashboard: bool = False
    dashboard_port: int = 8080
    dashboard_refresh_seconds: int = 5


@dataclass
class AlertConfig:
    """Configuration for alerts and notifications"""
    enable_email_alerts: bool = False
    enable_sms_alerts: bool = False
    enable_webhook_alerts: bool = False
    
    # Alert thresholds
    critical_loss_threshold: float = 3.0  # Send critical alert
    high_slippage_threshold: float = 1.0  # Alert on high slippage
    execution_delay_threshold: float = 1000.0  # Alert if execution > 1 second
    
    # Contact information
    email_recipients: List[str] = None
    sms_recipients: List[str] = None
    webhook_url: Optional[str] = None
    
    def __post_init__(self):
        if self.email_recipients is None:
            self.email_recipients = []
        if self.sms_recipients is None:
            self.sms_recipients = []


class EnhancedSLConfigManager:
    """Centralized configuration manager for enhanced SL mechanism"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.getenv('SL_CONFIG_FILE', 'sl_config.json')
        
        # Default configurations
        self.sl_config = StopLossConfig()
        self.order_config = OrderExecutionConfig()
        self.monitoring_config = MonitoringConfig()
        self.alert_config = AlertConfig()
        
        # Load from file if exists
        self._load_config()
        
        # Apply environment overrides
        self._apply_env_overrides()
    
    def _load_config(self):
        """Load configuration from file"""
        try:
            import json
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update configurations
                if 'stop_loss' in config_data:
                    self._update_dataclass(self.sl_config, config_data['stop_loss'])
                if 'order_execution' in config_data:
                    self._update_dataclass(self.order_config, config_data['order_execution'])
                if 'monitoring' in config_data:
                    self._update_dataclass(self.monitoring_config, config_data['monitoring'])
                if 'alerts' in config_data:
                    self._update_dataclass(self.alert_config, config_data['alerts'])
                    
        except Exception as e:
            print(f"Warning: Could not load config file {self.config_file}: {e}")
    
    def _update_dataclass(self, obj, data: Dict):
        """Update dataclass with dictionary data"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides"""
        # Stop-loss config overrides
        if os.getenv('SL_MAX_SLIPPAGE'):
            self.sl_config.max_slippage_percent = float(os.getenv('SL_MAX_SLIPPAGE'))
        if os.getenv('SL_EMERGENCY_THRESHOLD'):
            self.sl_config.emergency_exit_threshold = float(os.getenv('SL_EMERGENCY_THRESHOLD'))
        if os.getenv('SL_RETRY_ATTEMPTS'):
            self.sl_config.retry_attempts = int(os.getenv('SL_RETRY_ATTEMPTS'))
        if os.getenv('SL_PARALLEL_EXECUTION'):
            self.sl_config.parallel_execution = os.getenv('SL_PARALLEL_EXECUTION').lower() == 'true'
        
        # Order execution overrides
        if os.getenv('ORDER_MAX_CONCURRENT'):
            self.order_config.max_concurrent_orders = int(os.getenv('ORDER_MAX_CONCURRENT'))
        if os.getenv('ORDER_TIMEOUT'):
            self.order_config.order_timeout_seconds = int(os.getenv('ORDER_TIMEOUT'))
        
        # Monitoring overrides
        if os.getenv('MONITORING_REAL_TIME_ALERTS'):
            self.monitoring_config.enable_real_time_alerts = os.getenv('MONITORING_REAL_TIME_ALERTS').lower() == 'true'
        if os.getenv('MONITORING_LOG_TIMING'):
            self.monitoring_config.log_execution_timing = os.getenv('MONITORING_LOG_TIMING').lower() == 'true'
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            import json
            config_data = {
                'stop_loss': self._dataclass_to_dict(self.sl_config),
                'order_execution': self._dataclass_to_dict(self.order_config),
                'monitoring': self._dataclass_to_dict(self.monitoring_config),
                'alerts': self._dataclass_to_dict(self.alert_config)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving config file {self.config_file}: {e}")
    
    def _dataclass_to_dict(self, obj) -> Dict:
        """Convert dataclass to dictionary"""
        import dataclasses
        return dataclasses.asdict(obj)
    
    def get_production_config(self) -> 'EnhancedSLConfigManager':
        """Get production-optimized configuration"""
        # More conservative settings for production
        self.sl_config.max_slippage_percent = 0.3
        self.sl_config.emergency_exit_threshold = 1.5
        self.sl_config.retry_attempts = 5
        self.sl_config.parallel_execution = True
        self.sl_config.pre_auth_enabled = True
        
        self.order_config.max_concurrent_orders = 3
        self.order_config.order_timeout_seconds = 15
        self.order_config.retry_attempts = 5
        
        self.monitoring_config.enable_real_time_alerts = True
        self.monitoring_config.log_execution_timing = True
        self.monitoring_config.log_performance_metrics = True
        
        return self
    
    def get_development_config(self) -> 'EnhancedSLConfigManager':
        """Get development-optimized configuration"""
        # More verbose settings for development
        self.sl_config.max_slippage_percent = 1.0
        self.sl_config.emergency_exit_threshold = 3.0
        self.sl_config.retry_attempts = 2
        
        self.monitoring_config.log_all_price_updates = True
        self.monitoring_config.log_execution_timing = True
        self.monitoring_config.log_risk_assessments = True
        
        return self
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return any issues"""
        issues = []
        
        # Validate stop-loss config
        if self.sl_config.max_slippage_percent < 0 or self.sl_config.max_slippage_percent > 5:
            issues.append("max_slippage_percent should be between 0 and 5")
        
        if self.sl_config.emergency_exit_threshold < 0.5 or self.sl_config.emergency_exit_threshold > 10:
            issues.append("emergency_exit_threshold should be between 0.5 and 10")
        
        if self.sl_config.retry_attempts < 1 or self.sl_config.retry_attempts > 10:
            issues.append("retry_attempts should be between 1 and 10")
        
        # Validate order config
        if self.order_config.max_concurrent_orders < 1 or self.order_config.max_concurrent_orders > 20:
            issues.append("max_concurrent_orders should be between 1 and 20")
        
        if self.order_config.order_timeout_seconds < 5 or self.order_config.order_timeout_seconds > 60:
            issues.append("order_timeout_seconds should be between 5 and 60")
        
        return issues
    
    def print_config_summary(self):
        """Print a summary of current configuration"""
        print("=" * 60)
        print("ENHANCED STOP-LOSS CONFIGURATION SUMMARY")
        print("=" * 60)
        print(f"Max Slippage: {self.sl_config.max_slippage_percent}%")
        print(f"Emergency Threshold: {self.sl_config.emergency_exit_threshold}%")
        print(f"Retry Attempts: {self.sl_config.retry_attempts}")
        print(f"Parallel Execution: {self.sl_config.parallel_execution}")
        print(f"Pre-auth Enabled: {self.sl_config.pre_auth_enabled}")
        print(f"Max Concurrent Orders: {self.order_config.max_concurrent_orders}")
        print(f"Order Timeout: {self.order_config.order_timeout_seconds}s")
        print(f"Real-time Alerts: {self.monitoring_config.enable_real_time_alerts}")
        print(f"Execution Timing Logs: {self.monitoring_config.log_execution_timing}")
        print("=" * 60)


# Global configuration instance
config_manager = EnhancedSLConfigManager()

# Convenience functions
def get_sl_config() -> StopLossConfig:
    """Get stop-loss configuration"""
    return config_manager.sl_config

def get_order_config() -> OrderExecutionConfig:
    """Get order execution configuration"""
    return config_manager.order_config

def get_monitoring_config() -> MonitoringConfig:
    """Get monitoring configuration"""
    return config_manager.monitoring_config

def get_alert_config() -> AlertConfig:
    """Get alert configuration"""
    return config_manager.alert_config
