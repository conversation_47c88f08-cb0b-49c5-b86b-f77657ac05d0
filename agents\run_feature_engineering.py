#!/usr/bin/env python3
"""
Production script to run optimized feature engineering on all historical data files
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the parent directory to the path to import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from scripts.optimized_feature_engineering import OptimizedFeatureEngineering

async def main():
    """Main function to run feature engineering on all files"""
    
    print("[INIT] Starting Production Feature Engineering...")
    print("=" * 60)
    
    # Configuration - Updated for Options project
    input_dir = "data/historical"
    output_dir = "data/features"
    chunk_size = 50000  # Smaller chunk size to avoid window expression issues
    
    # Check if input directory exists
    if not Path(input_dir).exists():
        print(f"[ERROR] Input directory not found: {input_dir}")
        print("Please ensure the historical data files are in the correct location.")
        return
    
    # Check if there are parquet files
    input_files = list(Path(input_dir).glob("*.parquet"))
    if not input_files:
        print(f"[ERROR] No parquet files found in {input_dir}")
        print("Please ensure the historical data files are in parquet format.")
        return
    
    print(f"[FOLDER] Found {len(input_files)} files to process:")
    for f in input_files:
        print(f"   • {f.name}")
    
    print(f"\n⚙️  Configuration:")
    print(f"   • Input directory: {input_dir}")
    print(f"   • Output directory: {output_dir}")
    print(f"   • Chunk size: {chunk_size:,} rows")
    print(f"   • Compression: zstd level 3")
    
    # Create processor
    processor = OptimizedFeatureEngineering(input_dir, output_dir, chunk_size)
    
    # Process all files
    try:
        print(f"\n[WORKFLOW] Starting feature engineering...")
        await processor.process_all_files()
        
        print(f"\n🎉 Feature engineering completed successfully!")
        print(f"[STATUS] Check the output files in: {output_dir}")
        
        # List output files
        output_files = list(Path(output_dir).glob("features_*.parquet"))
        if output_files:
            print(f"\n[LIST] Generated files:")
            for f in output_files:
                print(f"   • {f.name}")
        
    except Exception as e:
        print(f"\n[ERROR] Error during feature engineering: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
