#!/usr/bin/env python3
"""
Enhanced Backtesting Agent Runner

This script demonstrates how to use the Enhanced Backtesting Agent with all its features:
- Multi-strategy and multi-timeframe backtesting
- Smart backtesting modes
- Comprehensive performance metrics
- Capital and risk modeling
- Result logging and visualization
"""

import os
import sys
import asyncio
import argparse
import logging
import time
from pathlib import Path

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.enhanced_backtesting_agent import EnhancedBacktestingAgent, BacktestMode

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/enhanced_backtesting_runner.log')
        ]
    )

def print_banner():
    """Print application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    [INIT] ENHANCED BACKTESTING AGENT [INIT]                         ║
║                                                                              ║
║  Comprehensive Multi-Strategy, Multi-Timeframe Backtesting System           ║
║                                                                              ║
║  Features:                                                                   ║
║  🔁 Multi-Strategy & Multi-Timeframe Backtesting                            ║
║  🧠 Smart Backtesting Modes (Deterministic, Probabilistic, Adaptive AI)     ║
║  🧪 Detailed Performance Metrics Calculation                                ║
║  🧰 Capital & Risk Modeling                                                 ║
║  🗂️ Scenario-Based & Regime-Based Testing                                   ║
║  🧬 Parameter Sweep & Optimization                                          ║
║  🧾 Result Logging & Versioning                                             ║
║  [STATUS] Backtest Visualization & Debugging                                      ║
║  [DEBUG] Signal Debugging & Replay                                               ║
║  [AGENT] LLM-Explainable Results Summary                                         ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_system_info():
    """Print system information"""
    import psutil
    import platform
    
    print("\n[STATUS] System Information:")
    print(f"   • Platform: {platform.system()} {platform.release()}")
    print(f"   • CPU Cores: {psutil.cpu_count()}")
    print(f"   • Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    
    # Check GPU availability
    try:
        import cupy as cp
        print(f"   • GPU: Available (CuPy)")
        print(f"   • GPU Memory: {cp.cuda.Device().mem_info[1] / (1024**3):.1f} GB")
    except ImportError:
        print(f"   • GPU: Not available")
    
    # Check optional dependencies
    optional_deps = {
        'optuna': 'Parameter Optimization',
        'plotly': 'Visualization',
        'bokeh': 'Interactive Charts',
    }
    
    print("\n📦 Optional Dependencies:")
    for dep, description in optional_deps.items():
        try:
            __import__(dep)
            print(f"   • {description}: [SUCCESS] Available")
        except ImportError:
            print(f"   • {description}: [ERROR] Not available")

async def run_demo_backtesting():
    """Run a demonstration of the enhanced backtesting system"""
    print("\n[TARGET] Running Demo Backtesting...")
    
    try:
        # Create agent with default configuration
        agent = EnhancedBacktestingAgent()
        
        # Initialize agent
        print("[CONFIG] Initializing Enhanced Backtesting Agent...")
        success = await agent.initialize()
        
        if not success:
            print("[ERROR] Failed to initialize agent")
            return False
        
        print("[SUCCESS] Agent initialized successfully")
        
        # Print configuration summary
        status = agent.get_status()
        print(f"\n[LIST] Configuration:")
        print(f"   • Agent: {status['config']['agent_name']}")
        print(f"   • Version: {status['config']['version']}")
        print(f"   • Mode: {status['config']['backtesting_mode']}")
        print(f"   • Strategies: {agent.total_strategies}")
        print(f"   • Symbols: {len(agent.symbols)}")
        print(f"   • GPU Available: {'[SUCCESS]' if status['gpu_available'] else '[ERROR]'}")
        
        # Start backtesting
        print("\n[INIT] Starting Enhanced Backtesting Process...")
        start_time = time.time()
        
        success = await agent.start_backtesting()
        
        execution_time = time.time() - start_time
        
        if success:
            print(f"[SUCCESS] Backtesting completed successfully in {execution_time:.2f} seconds")
            
            # Print final status
            final_status = agent.get_status()
            print(f"\n[METRICS] Results Summary:")
            print(f"   • Processed Strategies: {final_status['processed_strategies']}")
            print(f"   • Progress: {final_status['progress_pct']:.1f}%")
            print(f"   • Execution Time: {execution_time:.2f} seconds")
            
        else:
            print("[ERROR] Backtesting failed")
            return False
        
        # Stop agent
        await agent.stop()
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Demo backtesting failed: {e}")
        return False

async def run_custom_backtesting(config_path: str, mode: str, max_strategies: int = None, 
                                max_symbols: int = None, enable_gpu: bool = True):
    """Run custom backtesting with specified parameters"""
    print(f"\n[TARGET] Running Custom Backtesting...")
    print(f"   • Config: {config_path}")
    print(f"   • Mode: {mode}")
    print(f"   • Max Strategies: {max_strategies or 'All'}")
    print(f"   • Max Symbols: {max_symbols or 'All'}")
    print(f"   • GPU Enabled: {enable_gpu}")
    
    try:
        # Create agent with custom configuration
        agent = EnhancedBacktestingAgent(config_path)
        
        # Override configuration if needed
        if not await agent.initialize():
            print("[ERROR] Failed to initialize agent")
            return False
        
        # Apply command line overrides
        if max_strategies:
            agent.config.max_strategies = max_strategies
        if max_symbols:
            agent.config.max_symbols = max_symbols
        if not enable_gpu:
            agent.config.enable_gpu_acceleration = False
        
        # Set backtesting mode
        if mode.lower() == 'deterministic':
            agent.config.backtesting_mode = BacktestMode.DETERMINISTIC
        elif mode.lower() == 'probabilistic':
            agent.config.backtesting_mode = BacktestMode.PROBABILISTIC
        elif mode.lower() == 'adaptive_ai':
            agent.config.backtesting_mode = BacktestMode.ADAPTIVE_AI
        
        print("[SUCCESS] Agent configured successfully")
        
        # Start backtesting
        start_time = time.time()
        success = await agent.start_backtesting()
        execution_time = time.time() - start_time
        
        if success:
            print(f"[SUCCESS] Custom backtesting completed in {execution_time:.2f} seconds")
        else:
            print("[ERROR] Custom backtesting failed")
        
        await agent.stop()
        return success
        
    except Exception as e:
        print(f"[ERROR] Custom backtesting failed: {e}")
        return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Enhanced Backtesting Agent Runner',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run demo backtesting
  python run_enhanced_backtesting_agent.py --demo
  
  # Run with custom configuration
  python run_enhanced_backtesting_agent.py --config config/enhanced_backtesting_config.yaml
  
  # Run with specific mode and limits
  python run_enhanced_backtesting_agent.py --mode probabilistic --max-strategies 10 --max-symbols 5
  
  # Run without GPU acceleration
  python run_enhanced_backtesting_agent.py --no-gpu
        """
    )
    
    parser.add_argument('--demo', action='store_true',
                       help='Run demonstration backtesting')
    
    parser.add_argument('--config', type=str,
                       default='config/enhanced_backtesting_config.yaml',
                       help='Path to configuration file')
    
    parser.add_argument('--mode', type=str, choices=['deterministic', 'probabilistic', 'adaptive_ai'],
                       default='deterministic',
                       help='Backtesting mode')
    
    parser.add_argument('--max-strategies', type=int,
                       help='Maximum number of strategies to test')
    
    parser.add_argument('--max-symbols', type=int,
                       help='Maximum number of symbols to test')
    
    parser.add_argument('--no-gpu', action='store_true',
                       help='Disable GPU acceleration')
    
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='Logging level')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Print banner and system info
    print_banner()
    print_system_info()
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    try:
        if args.demo:
            # Run demo
            success = asyncio.run(run_demo_backtesting())
        else:
            # Run custom backtesting
            success = asyncio.run(run_custom_backtesting(
                config_path=args.config,
                mode=args.mode,
                max_strategies=args.max_strategies,
                max_symbols=args.max_symbols,
                enable_gpu=not args.no_gpu
            ))
        
        if success:
            print("\n🎉 Enhanced Backtesting completed successfully!")
            print("\n[FOLDER] Check the following directories for results:")
            print("   • data/backtest/ - Main results")
            print("   • data/backtest/versions/ - Detailed results")
            print("   • data/backtest/summaries/ - LLM summaries")
            print("   • data/backtest/charts/ - Visualizations")
            print("   • logs/ - Log files")
        else:
            print("\n[ERROR] Enhanced Backtesting failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n[WARN] Backtesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
