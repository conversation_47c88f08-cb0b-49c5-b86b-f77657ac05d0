# Python Trading System - py_eq Production .gitignore

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so

# Virtual environments
.venv/
venv/
env/
ENV/
.env
.env.*

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Trading System Logs (Production logs should be managed separately)
logs/*.log
logs/*.csv
logs/2025-*/
logs/test_*/
logs/strategy_accounts/
logs/performance_*.csv
logs/signals_*.csv
logs/trades_*.csv
*.log

# Test files and directories
test_*.py
*_test.py
tests/
testing/
test_logs/
simple_virtual_test.py
example_*.py

# Virtual Account Data (Production should use real accounts)
virtual_accounts/
*_virtual_*.json
*_virtual_*.csv
test_accounts/

# API Keys and Sensitive Configuration
config/api_keys.json
config/secrets.json
config/production_config.json
*.key
*.pem
secrets/
.secrets

# Database files (if using local DB)
*.db
*.sqlite
*.sqlite3

# Backup and temporary files
*.bak
*.backup
*.tmp
*_backup.*
*_old.*

# Data files (keep essential structure files)
data/*.csv
!data/stocks_to_monitor.csv
!data/watchlist.csv
data/historical_*/
data/cache/
data/temp/

# Symbol mapping and instrument files
smartapi_instruments.json
symbol_mappings_*.json
instruments_*.json

# Performance and analysis files
performance_*.csv
analysis_*.csv
reports/
reports_*/

# Development documentation (keep main README)
*README*.md
!README.md
ENHANCEMENT_*.md
FIXES_*.md
GUIDE_*.md
*_SUMMARY.md

# Alternative main files (keep only main.py for production)
main_*.py
!main.py

# Trading engine alternatives (keep only strategy files)
*_trading_engine.py
*_engine.py

# Fix and utility scripts (development only)
fix_*.py
analyze_*.py
debug_*.py

# Package files
*.whl
dist/
build/
*.egg-info/

# Coverage and testing
.coverage
.pytest_cache/
htmlcov/
.tox/

# Jupyter notebooks (if any)
.ipynb_checkpoints
*.ipynb

# Symbol mapping logs
symbol_mapping_download_*.log

# Go subdirectories (if any remain)
go_*/

# Node.js (if any JS components)
node_modules/
npm-debug.log*
yarn-debug.log*
