"""
Gap and Go Strategy for Live Trading

This strategy identifies significant gaps (>1%) from previous day's close and trades
breakouts from the first 30-minute range with volume confirmation.

Features:
- Gap detection (>1% from previous close)
- First 30-minute range identification
- Volume confirmation for entries
- Risk management with ₹800 per trade
- Stop loss and target management
- Real-time signal detection
"""

import logging
from datetime import datetime, time, timedelta, date
from typing import Dict, List, Optional, Tuple
import numpy as np
from dataclasses import dataclass

@dataclass
class GapAndGoSignal:
    """Gap and Go trading signal"""
    symbol: str
    signal_type: str  # 'LONG' or 'SHORT'
    entry_price: float
    stop_loss: float
    target: float
    risk_amount: float
    timestamp: datetime
    gap_percentage: float
    first_30min_high: float
    first_30min_low: float
    volume_confirmation: bool
    confidence: float

class GapAndGoStrategy:
    """Gap and Go Strategy"""

    def __init__(self, logger=None, market_data_service=None, order_service=None, config=None, risk_per_trade: float = 800.0):
        self.risk_per_trade = risk_per_trade
        self.logger = logger or logging.getLogger(f"{__class__.__name__}")
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config
        
        # Strategy parameters
        self.min_gap_percentage = 1.0  # Minimum 1% gap
        self.first_30min_period = 30  # First 30 minutes
        self.volume_multiplier = 1.1  # Volume should be 1.1x average (reduced from 1.5x for more signals)
        self.risk_reward_ratio = 3.0  # 1:3 risk reward
        # Use intraday stop loss percentage from config if available
        if hasattr(config, 'intraday_stop_loss_percent'):
            self.buffer_percentage = config.intraday_stop_loss_percent * 100  # Convert to percentage
        else:
            self.buffer_percentage = 0.8  # 0.8% buffer for intraday stop loss
        
        # Market hours
        self.market_start = time(9, 15)
        self.market_end = time(15, 30)
        self.first_30min_end = time(9, 45)  # 30 minutes after market open
        
        # Daily tracking
        self.daily_data: Dict[str, Dict] = {}  # symbol -> daily data
        self.signals_generated: Dict[str, bool] = {}  # symbol -> signal_generated_today
        self.previous_day_close: Dict[str, float] = {}  # symbol -> previous close
        
    def reset_daily_data(self):
        """Reset daily tracking data"""
        # Store previous day's close before clearing
        for symbol, data in self.daily_data.items():
            if 'current_close' in data:
                self.previous_day_close[symbol] = data['current_close']
                
        self.daily_data.clear()
        self.signals_generated.clear()
        self.logger.info("Reset daily Gap and Go data")
        
    def is_market_hours(self, timestamp: datetime) -> bool:
        """Check if timestamp is within market hours"""
        current_time = timestamp.time()
        return self.market_start <= current_time <= self.market_end
        
    def is_first_30min_period(self, timestamp: datetime) -> bool:
        """Check if timestamp is within first 30 minutes"""
        current_time = timestamp.time()
        return self.market_start <= current_time <= self.first_30min_end
        
    def update_daily_data(self, symbol: str, candle: Dict, timestamp: datetime):
        """Update daily data for gap detection and range tracking"""
        if symbol not in self.daily_data:
            self.daily_data[symbol] = {
                'gap_detected': False,
                'gap_percentage': 0.0,
                'gap_direction': None,  # 'UP' or 'DOWN'
                'first_30min_high': candle['high'],
                'first_30min_low': candle['low'],
                'first_30min_volume': candle['volume'],
                'candle_count': 1,
                'market_open_price': candle['open'],
                'current_close': candle['close']
            }
            
            # Check for gap on first candle of the day
            if symbol in self.previous_day_close:
                prev_close = self.previous_day_close[symbol]
                current_open = candle['open']
                gap_percentage = (current_open - prev_close) / prev_close * 100
                
                if abs(gap_percentage) >= self.min_gap_percentage:
                    self.daily_data[symbol]['gap_detected'] = True
                    self.daily_data[symbol]['gap_percentage'] = gap_percentage
                    self.daily_data[symbol]['gap_direction'] = 'UP' if gap_percentage > 0 else 'DOWN'
                    
                    self.logger.info(f"Gap detected for {symbol}: {gap_percentage:.2f}% "
                                   f"({self.daily_data[symbol]['gap_direction']})")
        else:
            # Update first 30-minute range
            data = self.daily_data[symbol]
            if self.is_first_30min_period(timestamp):
                data['first_30min_high'] = max(data['first_30min_high'], candle['high'])
                data['first_30min_low'] = min(data['first_30min_low'], candle['low'])
                data['first_30min_volume'] += candle['volume']
                data['candle_count'] += 1
                
            data['current_close'] = candle['close']
            
    def get_first_30min_data(self, symbol: str) -> Optional[Tuple[float, float, float]]:
        """Get first 30-minute high, low, and average volume"""
        if symbol not in self.daily_data:
            return None
            
        data = self.daily_data[symbol]
        avg_volume = data['first_30min_volume'] / data['candle_count'] if data['candle_count'] > 0 else 0
        
        return data['first_30min_high'], data['first_30min_low'], avg_volume
        
    def check_volume_confirmation(self, current_volume: float, avg_volume: float) -> bool:
        """Check if current volume confirms the breakout"""
        if avg_volume <= 0:
            return False
        return current_volume >= (avg_volume * self.volume_multiplier)
        
    def calculate_position_size(self, entry_price: float, stop_loss: float) -> int:
        """Calculate position size based on risk per trade"""
        if entry_price <= 0 or stop_loss <= 0:
            return 0
            
        risk_per_share = abs(entry_price - stop_loss)
        if risk_per_share <= 0:
            return 0
            
        position_size = int(self.risk_per_trade / risk_per_share)
        return max(1, position_size)  # Minimum 1 share

    def analyze_signal(self, signal) -> Optional[Dict]:
        """
        Analyze signal for Gap and Go strategy entry conditions

        Args:
            signal: Trading signal object

        Returns:
            Dict with entry conditions if signal is valid, None otherwise
        """
        try:
            if not self.market_data_service:
                self.logger.warning("No market data service available for Gap and Go analysis")
                return None

            # Get recent candles for analysis
            candles = self.market_data_service.get_historical_data(
                symbol=signal.symbol,
                timeframe='5min',
                days=2
            )

            if not candles or len(candles) < 20:
                return None

            # Convert to dict format for existing analysis method
            candle_dicts = []
            for candle in candles:
                candle_dict = {
                    'timestamp': candle.timestamp,
                    'open': candle.open,
                    'high': candle.high,
                    'low': candle.low,
                    'close': candle.close,
                    'volume': candle.volume
                }
                candle_dicts.append(candle_dict)

            # Use existing analyze_candle method
            gap_signal = self.analyze_candle(signal.symbol, candle_dicts[-1], candle_dicts[:-1])

            if gap_signal and gap_signal.signal_type == 'BUY':
                # Calculate entry conditions
                entry_price = gap_signal.entry_price
                stop_loss = gap_signal.stop_loss
                target = gap_signal.target
                quantity = self.calculate_position_size(entry_price, stop_loss)

                return {
                    'action': 'BUY',
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'target': target,
                    'quantity': quantity,
                    'confidence': gap_signal.confidence,
                    'strategy': 'GAP_AND_GO',
                    'timeframe': '5min'
                }

            return None

        except Exception as e:
            self.logger.error(f"Error analyzing Gap and Go signal: {e}")
            return None

    def process_signal(self, signal) -> Optional[Dict]:
        """
        Process signal method for compatibility with ProductionStrategyManager
        This is an alias for analyze_signal to maintain consistency across strategies
        """
        return self.analyze_signal(signal)

    def analyze_candle(self, symbol: str, candle: Dict, historical_candles: List[Dict]) -> Optional[GapAndGoSignal]:
        """
        Analyze current candle for Gap and Go signals with enhanced gap quality assessment
        
        Args:
            symbol: Stock symbol
            candle: Current candle data
            historical_candles: Historical candles for context
            
        Returns:
            GapAndGoSignal if signal detected, None otherwise
        """
        try:
            timestamp = candle.get('timestamp')
            if isinstance(timestamp, (int, float)):
                timestamp = datetime.fromtimestamp(timestamp)
            elif not isinstance(timestamp, datetime):
                return None
                
            # Skip if not market hours
            if not self.is_market_hours(timestamp):
                return None
                
            # Skip if signal already generated for this symbol today
            if self.signals_generated.get(symbol, False):
                return None
                
            # Update daily data
            self.update_daily_data(symbol, candle, timestamp)
            
            # Skip if no gap detected
            if symbol not in self.daily_data or not self.daily_data[symbol]['gap_detected']:
                return None
                
            # Skip if still in first 30 minutes (need to establish range first)
            if self.is_first_30min_period(timestamp):
                return None
                
            # Get first 30-minute data
            range_data = self.get_first_30min_data(symbol)
            if not range_data:
                return None
                
            first_30min_high, first_30min_low, avg_volume = range_data
            
            # Check for valid range
            if first_30min_high <= first_30min_low:
                return None
                
            daily_data = self.daily_data[symbol]
            gap_percentage = daily_data['gap_percentage']
            gap_direction = daily_data['gap_direction']
            
            current_price = candle['close']
            current_volume = candle.get('volume', 0)
            
            # Check volume confirmation
            volume_confirmation = self.check_volume_confirmation(current_volume, avg_volume)
            
            # Enhanced gap quality assessment
            gap_quality = self._assess_gap_quality(symbol, gap_percentage, historical_candles)
            
            # Skip low-quality gaps
            if gap_quality < 0.5:  # Threshold for gap quality
                self.logger.info(f"Low quality gap for {symbol}: {gap_quality:.2f}, skipping")
                return None
                
            # Check for breakout signals
            signal = None
            
            # Gap Up + Breakout: Price breaks above first 30-min high
            if (gap_direction == 'UP' and current_price > first_30min_high and volume_confirmation):
                entry_price = first_30min_high
                stop_loss = first_30min_low - (first_30min_low * self.buffer_percentage / 100)
                risk = entry_price - stop_loss
                target = entry_price + (risk * self.risk_reward_ratio)
                
                # Calculate confidence based on gap size, breakout strength, volume, and gap quality
                breakout_strength = (current_price - first_30min_high) / first_30min_high
                volume_strength = current_volume / avg_volume if avg_volume > 0 else 1
                gap_strength = min(abs(gap_percentage) / 5.0, 1.0)  # Normalize gap to 0-1
                confidence = min(0.95, 0.3 + (gap_strength * 0.2) + (breakout_strength * 10) + 
                                (volume_strength * 0.1) + (gap_quality * 0.3))
                
                signal = GapAndGoSignal(
                    symbol=symbol,
                    signal_type='LONG',
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    target=target,
                    risk_amount=risk,
                    timestamp=timestamp,
                    gap_percentage=gap_percentage,
                    first_30min_high=first_30min_high,
                    first_30min_low=first_30min_low,
                    volume_confirmation=volume_confirmation,
                    confidence=confidence
                )
                
            # Gap Down + Breakdown: Price breaks below first 30-min low
            elif (gap_direction == 'DOWN' and current_price < first_30min_low and volume_confirmation):
                entry_price = first_30min_low
                stop_loss = first_30min_high + (first_30min_high * self.buffer_percentage / 100)
                risk = stop_loss - entry_price
                target = entry_price - (risk * self.risk_reward_ratio)
                
                # Calculate confidence based on gap size, breakdown strength, volume, and gap quality
                breakdown_strength = (first_30min_low - current_price) / first_30min_low
                volume_strength = current_volume / avg_volume if avg_volume > 0 else 1
                gap_strength = min(abs(gap_percentage) / 5.0, 1.0)  # Normalize gap to 0-1
                confidence = min(0.95, 0.3 + (gap_strength * 0.2) + (breakdown_strength * 10) + 
                                (volume_strength * 0.1) + (gap_quality * 0.3))
                
                signal = GapAndGoSignal(
                    symbol=symbol,
                    signal_type='SHORT',
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    target=target,
                    risk_amount=risk,
                    timestamp=timestamp,
                    gap_percentage=gap_percentage,
                    first_30min_high=first_30min_high,
                    first_30min_low=first_30min_low,
                    volume_confirmation=volume_confirmation,
                    confidence=confidence
                )
                
            if signal:
                # Mark signal as generated for this symbol
                self.signals_generated[symbol] = True
                
                self.logger.info(f"Gap and Go {signal.signal_type} signal for {symbol}: "
                               f"Gap: {signal.gap_percentage:.2f}%, Gap Quality: {gap_quality:.2f}, "
                               f"Entry: ₹{signal.entry_price:.2f}, SL: ₹{signal.stop_loss:.2f}, "
                               f"Target: ₹{signal.target:.2f}, Risk: ₹{signal.risk_amount:.2f}")
                
            return signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing Gap and Go signal for {symbol}: {e}")
            return None
            
    def _assess_gap_quality(self, symbol: str, gap_percentage: float, historical_candles: List[Dict]) -> float:
        """
        Assess the quality of a gap based on various factors
        
        Args:
            symbol: Stock symbol
            gap_percentage: Gap percentage
            historical_candles: Historical candles for context
            
        Returns:
            Gap quality score (0.0 to 1.0)
        """
        try:
            # Base quality on gap size
            base_quality = min(abs(gap_percentage) / 10.0, 0.5)  # Max 0.5 from gap size alone
            
            # Check for abnormal volume on gap day
            if len(historical_candles) >= 3:
                # Get average volume of previous 3 days
                prev_volumes = [candle.get('volume', 0) for candle in historical_candles[-3:]]
                avg_prev_volume = sum(prev_volumes) / len(prev_volumes) if prev_volumes else 0
                
                # Get opening volume
                opening_volume = self.daily_data[symbol].get('first_30min_volume', 0)
                
                # Volume factor: higher volume = higher quality gap
                volume_factor = min(opening_volume / (avg_prev_volume + 1), 3.0) / 6.0  # Max 0.5 from volume
                
                # Combine factors
                quality = base_quality + volume_factor
                
                # Check for price consistency (no immediate reversal)
                if len(historical_candles) >= 1:
                    last_candle = historical_candles[-1]
                    gap_direction = self.daily_data[symbol].get('gap_direction')
                    
                    # If gap up and first candle is bullish, or gap down and first candle is bearish
                    if (gap_direction == 'UP' and last_candle.get('close', 0) > last_candle.get('open', 0)) or \
                       (gap_direction == 'DOWN' and last_candle.get('close', 0) < last_candle.get('open', 0)):
                        quality += 0.1  # Bonus for consistent direction
                
                return min(quality, 1.0)  # Cap at 1.0
            
            return base_quality  # Default to base quality if not enough history
            
        except Exception as e:
            self.logger.warning(f"Error assessing gap quality for {symbol}: {e}")
            return 0.5  # Default to neutral quality on error
            
    def get_strategy_info(self) -> Dict:
        """Get strategy information"""
        gaps_detected = len([d for d in self.daily_data.values() if d.get('gap_detected', False)])
        
        return {
            'name': 'Gap and Go',
            'description': 'Trades breakouts from first 30-min range after significant gaps',
            'risk_per_trade': self.risk_per_trade,
            'risk_reward_ratio': self.risk_reward_ratio,
            'min_gap_percentage': self.min_gap_percentage,
            'first_30min_period': self.first_30min_period,
            'volume_multiplier': self.volume_multiplier,
            'gaps_detected_today': gaps_detected,
            'signals_generated_today': len([s for s in self.signals_generated.values() if s])
        }
