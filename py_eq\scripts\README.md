# Scripts Directory

This directory contains utility scripts organized by purpose.

## Structure

### `/setup/`
Contains setup and configuration scripts:
- `setup.py` - Main setup script for the trading system
- `setup_telegram_bot.py` - Telegram bot configuration setup
- `quick_telegram_setup.py` - Quick Telegram bot setup utility

### `/debug/`
Contains debugging and diagnostic scripts:
- `debug_auth.py` - SmartAPI authentication debugging
- `debug_user_id.py` - User ID debugging utilities
- `get_user_id.py` - Retrieve user ID from SmartAPI

### Root Scripts
- `telegram_bot_launcher.py` - Telegram bot launcher script

## Usage

Run scripts from the py_eq root directory:

```bash
# Setup scripts
python scripts/setup/setup.py
python scripts/setup/setup_telegram_bot.py

# Debug scripts
python scripts/debug/debug_auth.py
python scripts/debug/get_user_id.py

# Launcher
python scripts/telegram_bot_launcher.py
```
