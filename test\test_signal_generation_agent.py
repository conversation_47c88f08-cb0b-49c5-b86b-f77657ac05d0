#!/usr/bin/env python3
"""
Test Suite for Signal Generation Agent

Tests:
- Signal generation logic
- Strategy evaluation
- Position sizing calculations
- Risk management validation
- Signal validation mechanisms
- Integration with other agents
- Performance and reliability
"""

import os
import sys
import pytest
import asyncio
import tempfile
import yaml
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from typing import List, Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.signal_generation_agent import (
    SignalGenerationAgent,
    TradingSignal,
    SignalInput,
    MarketIndicators,
    OHLCV,
    MarketRegime,
    PositionSizingResult,
    SignalValidationResult,
    generate_signal_id,
    is_market_hours,
    calculate_kelly_fraction
)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TEST FIXTURES
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.fixture
def sample_ohlcv_data():
    """Create sample OHLCV data for testing"""
    base_time = datetime.now().replace(hour=10, minute=0, second=0, microsecond=0)
    data = []
    
    for i in range(100):
        timestamp = base_time + timedelta(minutes=i)
        price = 100 + (i * 0.1)  # Trending upward
        
        ohlcv = OHLCV(
            symbol="RELIANCE",
            timestamp=timestamp,
            timeframe="1min",
            open=price,
            high=price + 0.5,
            low=price - 0.3,
            close=price + 0.2,
            volume=10000 + (i * 100)
        )
        data.append(ohlcv)
    
    return data

@pytest.fixture
def sample_indicators():
    """Create sample market indicators for testing"""
    return MarketIndicators(
        symbol="RELIANCE",
        timestamp=datetime.now(),
        ema_5=102.5,
        ema_10=101.8,
        ema_13=101.5,
        ema_20=101.0,
        ema_21=100.9,
        ema_30=100.5,
        ema_50=100.0,
        ema_100=99.5,
        sma_20=101.2,
        rsi_5=65.0,
        rsi_14=58.5,
        macd=0.5,
        macd_signal=0.3,
        macd_histogram=0.2,
        stoch_k=70.0,
        stoch_d=68.0,
        cci=50.0,
        adx=25.0,
        mfi=55.0,
        bb_upper=103.0,
        bb_lower=99.0,
        bb_middle=101.0,
        atr=1.5,
        vwap=101.5,
        supertrend=100.0,
        supertrend_direction=1,
        donchian_high=103.5,
        donchian_low=98.5
    )

@pytest.fixture
def sample_market_regime():
    """Create sample market regime for testing"""
    return MarketRegime(
        regime='bull',
        confidence=0.8,
        volatility_level='medium',
        trend_strength=0.7,
        market_breadth=65.0,
        correlation_level=0.6,
        timestamp=datetime.now()
    )

@pytest.fixture
def test_config():
    """Create test configuration"""
    return {
        'input_sources': {
            'market_data': {
                'primary_source': 'test',
                'data_refresh_interval_seconds': 1
            }
        },
        'strategy_evaluation': {
            'strategy_config_path': 'config/strategies.yaml'
        },
        'risk_management': {
            'default_risk_reward_ratio': 2.0,
            'atr_stop_loss_multiplier': 2.0,
            'max_daily_risk_percent': 5.0
        },
        'position_sizing': {
            'default_method': 'fixed_fraction',
            'capital_allocation': {
                'total_capital': 100000,
                'max_position_size_percent': 2.0,
                'max_risk_per_trade_percent': 1.0,
                'intraday_margin_multiplier': 3.5
            }
        },
        'signal_validation': {
            'min_confidence': 0.6,
            'cooldown': {
                'minutes_between_signals': 5
            },
            'time_filters': {
                'market_hours_only': True
            }
        },
        'output_config': {
            'storage': {
                'signals_path': 'test_data/signals',
                'performance_path': 'test_data/performance'
            },
            'logging': {
                'enable_file_logging': False,
                'level': 'INFO'
            }
        },
        'notifications': {
            'telegram': {
                'enable': False
            }
        },
        'integrations': {
            'market_monitoring': {
                'enable': False
            },
            'ai_training': {
                'enable': False
            }
        },
        'performance': {
            'processing': {
                'enable_multiprocessing': False
            }
        }
    }

@pytest.fixture
async def signal_agent(test_config):
    """Create Signal Generation Agent for testing"""
    # Create temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(test_config, f)
        config_path = f.name
    
    try:
        agent = SignalGenerationAgent(config_path)
        yield agent
    finally:
        # Cleanup
        os.unlink(config_path)
        if hasattr(agent, 'is_running') and agent.is_running:
            await agent.stop()

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 UTILITY FUNCTION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

def test_generate_signal_id():
    """Test signal ID generation"""
    symbol = "RELIANCE"
    strategy = "test_strategy"
    timestamp = datetime(2024, 1, 15, 10, 30, 45)
    
    signal_id = generate_signal_id(symbol, strategy, timestamp)
    
    assert signal_id == "RELIANCE_test_strategy_20240115_103045"
    assert isinstance(signal_id, str)
    assert len(signal_id) > 0

def test_is_market_hours():
    """Test market hours validation"""
    # Test market hours (10:30 AM on a weekday)
    market_time = datetime(2024, 1, 15, 10, 30, 0)  # Monday
    assert is_market_hours(market_time) == True
    
    # Test before market hours
    before_market = datetime(2024, 1, 15, 9, 0, 0)
    assert is_market_hours(before_market) == False
    
    # Test after market hours
    after_market = datetime(2024, 1, 15, 16, 0, 0)
    assert is_market_hours(after_market) == False
    
    # Test weekend
    weekend = datetime(2024, 1, 13, 10, 30, 0)  # Saturday
    assert is_market_hours(weekend) == False

def test_calculate_kelly_fraction():
    """Test Kelly Criterion calculation"""
    # Test valid inputs
    win_rate = 0.6
    avg_win = 2.0
    avg_loss = 1.0
    
    kelly = calculate_kelly_fraction(win_rate, avg_win, avg_loss)
    
    assert 0 <= kelly <= 0.25  # Should be capped at 25%
    assert isinstance(kelly, float)
    
    # Test edge cases
    assert calculate_kelly_fraction(0.0, 2.0, 1.0) == 0.0  # No wins
    assert calculate_kelly_fraction(1.0, 2.0, 1.0) == 0.0  # 100% wins (invalid)
    assert calculate_kelly_fraction(0.6, 2.0, 0.0) == 0.0  # No losses (invalid)

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 SIGNAL GENERATION AGENT TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_agent_initialization(signal_agent):
    """Test agent initialization"""
    agent = signal_agent
    
    assert agent is not None
    assert hasattr(agent, 'config')
    assert hasattr(agent, 'strategies')
    assert hasattr(agent, 'active_signals')
    assert hasattr(agent, 'performance_metrics')
    assert agent.is_running == False

@pytest.mark.asyncio
async def test_agent_setup(signal_agent):
    """Test agent setup process"""
    agent = signal_agent
    
    # Mock strategy loading
    with patch.object(agent, '_load_strategies') as mock_load:
        mock_load.return_value = None
        
        await agent.setup()
        
        mock_load.assert_called_once()

@pytest.mark.asyncio
async def test_position_sizing_calculation(signal_agent, sample_indicators):
    """Test position sizing calculations"""
    agent = signal_agent
    
    symbol = "RELIANCE"
    strategy_name = "test_strategy"
    current_price = 100.0
    capital_info = {
        'total_capital': 100000,
        'max_position_size_percent': 2.0,
        'max_risk_per_trade_percent': 1.0,
        'intraday_margin_multiplier': 3.5
    }
    
    result = await agent._calculate_position_sizing(symbol, strategy_name, current_price, capital_info)
    
    assert isinstance(result, PositionSizingResult)
    assert result.quantity >= 1
    assert result.capital_allocated > 0
    assert result.risk_amount > 0
    assert result.method_used in ['kelly', 'fixed_fraction', 'volatility_scaled', 'fixed_fraction_fallback', 'fallback']

@pytest.mark.asyncio
async def test_stop_loss_take_profit_calculation(signal_agent, sample_indicators, sample_market_regime):
    """Test stop loss and take profit calculations"""
    agent = signal_agent
    
    current_price = 100.0
    signal_type = 1  # Long position
    
    sl_tp = agent._calculate_stop_loss_take_profit(current_price, signal_type, sample_indicators, sample_market_regime)
    
    assert 'stop_loss' in sl_tp
    assert 'take_profit' in sl_tp
    assert 'risk_reward_ratio' in sl_tp
    
    # For long position, stop loss should be below current price
    assert sl_tp['stop_loss'] < current_price
    assert sl_tp['take_profit'] > current_price
    assert sl_tp['risk_reward_ratio'] > 0

@pytest.mark.asyncio
async def test_signal_validation(signal_agent, sample_ohlcv_data, sample_indicators, sample_market_regime):
    """Test signal validation logic"""
    agent = signal_agent
    
    # Create a test signal
    signal = TradingSignal(
        signal_id="TEST_SIGNAL_001",
        symbol="RELIANCE",
        strategy_name="test_strategy",
        signal_type=1,
        action="BUY",
        entry_price=100.0,
        stop_loss=98.0,
        take_profit=104.0,
        quantity=20,
        risk_reward_ratio=2.0,
        confidence=0.75,
        market_regime="bull",
        timestamp=datetime.now().replace(hour=10, minute=30),  # Market hours
        capital_allocated=2000.0,
        risk_amount=400.0,
        position_size_method="fixed_fraction",
        liquidity_check=False,
        time_filter_check=False,
        risk_check=False,
        cooldown_check=True,
        context={},
        indicators_snapshot={}
    )
    
    validation_result = await agent._validate_signal(signal)
    
    assert isinstance(validation_result, SignalValidationResult)
    assert isinstance(validation_result.is_valid, bool)
    assert validation_result.validation_score >= 0.0

@pytest.mark.asyncio
async def test_market_data_processing(signal_agent, sample_ohlcv_data, sample_indicators, sample_market_regime):
    """Test market data processing and signal generation"""
    agent = signal_agent
    
    # Mock strategy loading
    agent.strategies = {
        'test_strategy': {
            'name': 'test_strategy',
            'long': 'close > ema_20 & rsi_14 > 50',
            'short': 'close < ema_20 & rsi_14 < 50',
            'capital': 100000
        }
    }
    agent.compiled_strategies = {
        'test_strategy': {
            'name': 'test_strategy',
            'long_expr': 'close > ema_20 & rsi_14 > 50',
            'short_expr': 'close < ema_20 & rsi_14 < 50',
            'capital': 100000
        }
    }
    
    symbol = "RELIANCE"
    
    signals = await agent.process_market_data(symbol, sample_ohlcv_data, sample_indicators, sample_market_regime)
    
    assert isinstance(signals, list)
    # Signals may or may not be generated depending on conditions

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 INTEGRATION TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_agent_start_stop(signal_agent):
    """Test agent start and stop functionality"""
    agent = signal_agent
    
    # Mock setup
    with patch.object(agent, 'setup') as mock_setup:
        mock_setup.return_value = None
        
        await agent.start()
        assert agent.is_running == True
        
        await agent.stop()
        assert agent.is_running == False

@pytest.mark.asyncio
async def test_performance_metrics_tracking(signal_agent):
    """Test performance metrics tracking"""
    agent = signal_agent
    
    # Check initial metrics
    metrics = agent.get_performance_metrics()
    
    assert 'signals_generated' in metrics
    assert 'signals_validated' in metrics
    assert 'signals_rejected' in metrics
    assert 'execution_time_ms' in metrics
    
    assert metrics['signals_generated'] == 0
    assert metrics['signals_validated'] == 0
    assert metrics['signals_rejected'] == 0

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 ERROR HANDLING TESTS
# ═══════════════════════════════════════════════════════════════════════════════

@pytest.mark.asyncio
async def test_invalid_strategy_handling(signal_agent):
    """Test handling of invalid strategy expressions"""
    agent = signal_agent
    
    # Test with invalid expression
    invalid_expr = "invalid_column > 100"
    result = agent._evaluate_strategy_condition(None, invalid_expr)
    
    assert result == False  # Should handle gracefully

@pytest.mark.asyncio
async def test_missing_data_handling(signal_agent):
    """Test handling of missing market data"""
    agent = signal_agent
    
    # Test with empty OHLCV data
    empty_data = []
    indicators = MarketIndicators(symbol="TEST", timestamp=datetime.now())
    
    signals = await agent.process_market_data("TEST", empty_data, indicators, None)
    
    assert isinstance(signals, list)
    assert len(signals) == 0  # Should handle gracefully

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
