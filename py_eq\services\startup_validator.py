"""
Startup Validator - Comprehensive system validation before trading starts
Ensures system integrity and prevents trading violations
"""

import logging
from datetime import datetime, date
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

from services.daily_trade_tracker import DailyTradeTracker, TradeStatus
from services.order_state_manager import OrderStateManager, PositionStatus


@dataclass
class ValidationResult:
    """Validation result container"""
    is_valid: bool
    severity: str  # INFO, WARNING, ERROR, CRITICAL
    message: str
    action_required: Optional[str] = None


class StartupValidator:
    """
    Comprehensive startup validation service
    
    Validates:
    - Daily trade limits
    - Existing positions
    - Order states
    - System configuration
    - Market conditions
    """

    def __init__(self, logger: logging.Logger, trade_tracker: DailyTradeTracker, 
                 order_manager: OrderStateManager, order_service, max_trades_per_day: int = 3):
        self.logger = logger
        self.trade_tracker = trade_tracker
        self.order_manager = order_manager
        self.order_service = order_service
        self.max_trades_per_day = max_trades_per_day
        
        self.validation_results: List[ValidationResult] = []

    def run_full_validation(self) -> Tuple[bool, List[ValidationResult]]:
        """
        Run complete startup validation
        
        Returns:
            Tuple[bool, List[ValidationResult]]: (system_ready, validation_results)
        """
        self.logger.info("🔍 Starting comprehensive system validation...")
        self.validation_results = []
        
        # Run all validation checks
        self._validate_daily_trade_limits()
        self._validate_existing_positions()
        self._validate_order_states()
        self._validate_broker_sync()
        self._validate_system_configuration()
        self._validate_market_conditions()
        
        # Determine overall system readiness
        critical_errors = [r for r in self.validation_results if r.severity == "CRITICAL"]
        errors = [r for r in self.validation_results if r.severity == "ERROR"]
        warnings = [r for r in self.validation_results if r.severity == "WARNING"]
        
        system_ready = len(critical_errors) == 0 and len(errors) == 0
        
        # Log summary
        self.logger.info("=" * 60)
        self.logger.info("🔍 STARTUP VALIDATION SUMMARY")
        self.logger.info("=" * 60)
        
        if system_ready:
            self.logger.info("✅ System validation PASSED - Ready for trading")
        else:
            self.logger.error("❌ System validation FAILED - Trading blocked")
        
        self.logger.info(f"📊 Validation Results: {len(critical_errors)} Critical, {len(errors)} Errors, {len(warnings)} Warnings")
        
        # Log all results
        for result in self.validation_results:
            if result.severity == "CRITICAL":
                self.logger.error(f"🚨 CRITICAL: {result.message}")
                if result.action_required:
                    self.logger.error(f"   ACTION: {result.action_required}")
            elif result.severity == "ERROR":
                self.logger.error(f"❌ ERROR: {result.message}")
                if result.action_required:
                    self.logger.error(f"   ACTION: {result.action_required}")
            elif result.severity == "WARNING":
                self.logger.warning(f"⚠️ WARNING: {result.message}")
                if result.action_required:
                    self.logger.warning(f"   ACTION: {result.action_required}")
            else:
                self.logger.info(f"ℹ️ INFO: {result.message}")
        
        self.logger.info("=" * 60)
        
        return system_ready, self.validation_results

    def _validate_daily_trade_limits(self):
        """Validate daily trade limits and counts"""
        try:
            summary = self.trade_tracker.get_daily_summary()
            
            executed_trades = summary['executed_trades']
            max_trades = summary['max_trades_per_day']
            remaining_trades = summary['remaining_trades']
            
            # Check if daily limit is exceeded
            if executed_trades > max_trades:
                self.validation_results.append(ValidationResult(
                    is_valid=False,
                    severity="CRITICAL",
                    message=f"Daily trade limit EXCEEDED: {executed_trades}/{max_trades} trades",
                    action_required="Manual intervention required - no new trades allowed"
                ))
            elif executed_trades == max_trades:
                self.validation_results.append(ValidationResult(
                    is_valid=False,
                    severity="ERROR",
                    message=f"Daily trade limit REACHED: {executed_trades}/{max_trades} trades",
                    action_required="No new trades allowed today"
                ))
            elif executed_trades >= max_trades - 1:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="WARNING",
                    message=f"Near daily trade limit: {executed_trades}/{max_trades} trades (only {remaining_trades} remaining)",
                    action_required="Use remaining trades carefully"
                ))
            else:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="INFO",
                    message=f"Daily trade limit OK: {executed_trades}/{max_trades} trades used, {remaining_trades} remaining"
                ))
            
            # Check for pending trades
            pending_trades = self.trade_tracker.get_trades_by_status(TradeStatus.PENDING)
            if pending_trades:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="WARNING",
                    message=f"Found {len(pending_trades)} pending trades that need status updates",
                    action_required="Check and update pending trade statuses"
                ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=False,
                severity="ERROR",
                message=f"Error validating daily trade limits: {e}",
                action_required="Check trade tracker configuration"
            ))

    def _validate_existing_positions(self):
        """Validate existing positions"""
        try:
            # Get positions from order manager
            open_positions = self.order_manager.get_open_positions()
            
            if not open_positions:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="INFO",
                    message="No open positions found"
                ))
                return
            
            # Check position count against limits - but allow monitoring existing positions
            if len(open_positions) > self.max_trades_per_day:
                self.validation_results.append(ValidationResult(
                    is_valid=True,  # Changed to True - allow monitoring but block new trades
                    severity="WARNING",  # Changed from CRITICAL to WARNING
                    message=f"Excess open positions detected: {len(open_positions)} > {self.max_trades_per_day} (will monitor existing, block new trades)",
                    action_required="System will monitor existing positions but block new trades until positions are reduced"
                ))
            
            # Validate each position
            for position in open_positions:
                # Check for missing critical data
                if position.entry_price <= 0:
                    self.validation_results.append(ValidationResult(
                        is_valid=False,
                        severity="ERROR",
                        message=f"Position {position.symbol} has invalid entry price: {position.entry_price}",
                        action_required="Update entry price or close position manually"
                    ))
                
                if position.stop_loss <= 0:
                    self.validation_results.append(ValidationResult(
                        is_valid=True,
                        severity="WARNING",
                        message=f"Position {position.symbol} has no stop loss set",
                        action_required="Set stop loss for risk management"
                    ))
                
                # Check position status
                if position.status == PositionStatus.MONITORING:
                    self.validation_results.append(ValidationResult(
                        is_valid=True,
                        severity="WARNING",
                        message=f"Position {position.symbol} is in monitoring status",
                        action_required="Verify position status with broker"
                    ))
            
            self.validation_results.append(ValidationResult(
                is_valid=True,
                severity="INFO",
                message=f"Found {len(open_positions)} open positions: {[p.symbol for p in open_positions]}"
            ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=False,
                severity="ERROR",
                message=f"Error validating existing positions: {e}",
                action_required="Check order state manager configuration"
            ))

    def _validate_order_states(self):
        """Validate order states and consistency"""
        try:
            # Check for consistency between trade tracker and order manager
            traded_symbols = self.trade_tracker.traded_symbols
            position_symbols = set(self.order_manager.open_positions.keys())
            
            # Symbols in trade tracker but not in positions
            missing_positions = traded_symbols - position_symbols
            if missing_positions:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="WARNING",
                    message=f"Symbols traded but no positions found: {missing_positions}",
                    action_required="Verify if positions were closed or check data consistency"
                ))
            
            # Positions without corresponding trades
            untracked_positions = position_symbols - traded_symbols
            if untracked_positions:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="WARNING",
                    message=f"Positions found but not in trade tracker: {untracked_positions}",
                    action_required="Update trade tracker or verify position source"
                ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=False,
                severity="ERROR",
                message=f"Error validating order states: {e}",
                action_required="Check data consistency between services"
            ))

    def _validate_broker_sync(self):
        """Validate synchronization with broker"""
        try:
            # Get real positions from broker
            broker_positions = self.order_service.get_open_positions()
            tracked_positions = self.order_manager.get_open_positions()
            
            broker_symbols = set(pos.symbol for pos in broker_positions)
            tracked_symbols = set(pos.symbol for pos in tracked_positions)
            
            # Check for discrepancies
            broker_only = broker_symbols - tracked_symbols
            tracked_only = tracked_symbols - broker_symbols
            
            if broker_only:
                self.validation_results.append(ValidationResult(
                    is_valid=False,
                    severity="ERROR",
                    message=f"Positions in broker but not tracked: {broker_only}",
                    action_required="Add missing positions to tracking system"
                ))
            
            if tracked_only:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="WARNING",
                    message=f"Tracked positions not found in broker: {tracked_only}",
                    action_required="Verify if positions were closed externally"
                ))
            
            if not broker_only and not tracked_only:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="INFO",
                    message=f"Broker sync OK: {len(broker_positions)} positions match tracking"
                ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=False,
                severity="ERROR",
                message=f"Error validating broker sync: {e}",
                action_required="Check broker connection and authentication"
            ))

    def _validate_system_configuration(self):
        """Validate system configuration"""
        try:
            # Check if max trades per day is consistent
            tracker_max = self.trade_tracker.max_trades_per_day
            validator_max = self.max_trades_per_day
            
            if tracker_max != validator_max:
                self.validation_results.append(ValidationResult(
                    is_valid=False,
                    severity="ERROR",
                    message=f"Inconsistent max trades config: tracker={tracker_max}, validator={validator_max}",
                    action_required="Fix configuration inconsistency"
                ))
            else:
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="INFO",
                    message=f"Configuration OK: max trades per day = {tracker_max}"
                ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=False,
                severity="ERROR",
                message=f"Error validating system configuration: {e}",
                action_required="Check system configuration"
            ))

    def _validate_market_conditions(self):
        """Validate market conditions"""
        try:
            from utils.helpers import is_market_open
            import os
            
            if is_market_open() or os.getenv("OVERRIDE_MARKET_OPEN") == "true":
                self.validation_results.append(ValidationResult(
                    is_valid=True,
                    severity="INFO",
                    message="Market is open - trading allowed"
                ))
            else:
                self.validation_results.append(ValidationResult(
                    is_valid=False,
                    severity="WARNING",
                    message="Market is closed - trading not allowed",
                    action_required="Wait for market open or set OVERRIDE_MARKET_OPEN=true for testing"
                ))
            
        except Exception as e:
            self.validation_results.append(ValidationResult(
                is_valid=True,
                severity="WARNING",
                message=f"Could not validate market conditions: {e}",
                action_required="Check market hours validation"
            ))

    def get_trading_permission(self) -> Tuple[bool, str]:
        """
        Get final trading permission based on validation results

        Returns:
            Tuple[bool, str]: (can_trade, reason)
        """
        critical_errors = [r for r in self.validation_results if r.severity == "CRITICAL"]
        errors = [r for r in self.validation_results if r.severity == "ERROR"]

        if critical_errors:
            return False, f"Critical errors found: {len(critical_errors)} issues"

        if errors:
            return False, f"Errors found: {len(errors)} issues"

        # Check specific conditions
        summary = self.trade_tracker.get_daily_summary()
        if summary['daily_trade_count'] >= summary['max_trades_per_day']:
            return False, "Daily trade limit reached"

        # Check if we have excess positions (should block new trades but allow monitoring)
        open_positions = self.order_manager.get_open_positions()
        if len(open_positions) > self.max_trades_per_day:
            return False, f"Excess positions detected: {len(open_positions)} > {self.max_trades_per_day} (monitoring only)"

        return True, "All validations passed - trading allowed"
