"""
Telegram Bot Launcher

This script launches the Telegram bot for trading reports.
Can be run standalone or integrated with the main trading system.
"""

import os
import sys
import logging
import threading
import time
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.telegram_bot_service import TradingTelegramBot
from utils.safe_logging import get_safe_logger
from config.telegram_config import telegram_config


def setup_environment():
    """Setup environment variables for the bot"""
    # Try to load from .env.telegram file first
    env_file_path = os.path.join(os.path.dirname(__file__), '.env.telegram')
    if os.path.exists(env_file_path):
        try:
            with open(env_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Loaded configuration from .env.telegram")
        except Exception as e:
            print(f"⚠️ Error reading .env.telegram: {e}")

    # Check if environment variables are set
    required_vars = ['TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables or create .env.telegram file.")
        print("\nExample .env.telegram file:")
        print("TELEGRAM_BOT_TOKEN=your_bot_token_here")
        print("TELEGRAM_CHAT_ID=your_chat_id_here")
        print("TELEGRAM_AUTHORIZED_USERS=user1,user2,user3")
        print("TELEGRAM_ADMIN_USERS=admin1,admin2")
        return False

    return True


def create_sample_env_file():
    """Create a sample .env file for configuration"""
    env_content = """# Telegram Bot Configuration
# Get your bot token from @BotFather on Telegram
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Your Telegram chat ID (you can get this from @userinfobot)
TELEGRAM_CHAT_ID=your_chat_id_here

# Comma-separated list of authorized user IDs
TELEGRAM_AUTHORIZED_USERS=user1,user2,user3

# Comma-separated list of admin user IDs
TELEGRAM_ADMIN_USERS=admin1,admin2

# Optional: Custom settings
TELEGRAM_DAILY_REPORTS=true
TELEGRAM_WEEKLY_REPORTS=true
TELEGRAM_MONTHLY_REPORTS=true
TELEGRAM_TRADE_ALERTS=true
"""
    
    env_file_path = os.path.join(os.path.dirname(__file__), '.env.telegram.sample')
    with open(env_file_path, 'w') as f:
        f.write(env_content)
    
    print(f"📄 Sample environment file created: {env_file_path}")
    print("Please copy this to .env and fill in your actual values.")


def main():
    """Main function to launch the Telegram bot"""
    print("🤖 Starting Telegram Trading Bot...")
    print("=" * 50)
    
    # Setup logging
    logger = get_safe_logger("telegram_bot")
    
    # Check environment setup
    if not setup_environment():
        create_sample_env_file()
        return 1

    try:
        # Reload configuration from environment variables
        telegram_config.reload_from_env()

        # Validate configuration
        telegram_config.validate()
        print("✅ Configuration validated successfully")
        
        # Create and start the bot
        bot = TradingTelegramBot(logger=logger)
        
        print(f"🚀 Bot starting with token: {telegram_config.bot_token[:10]}...")
        print(f"📱 Authorized users: {len(telegram_config.authorized_users + telegram_config.admin_users)}")
        print(f"⚙️ Daily reports: {'✅' if telegram_config.send_daily_reports else '❌'}")
        print(f"⚙️ Weekly reports: {'✅' if telegram_config.send_weekly_reports else '❌'}")
        print(f"⚙️ Monthly reports: {'✅' if telegram_config.send_monthly_reports else '❌'}")
        print("=" * 50)
        
        # Start the bot (this will block)
        bot.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        return 0
    except Exception as e:
        logger.error(f"Failed to start Telegram bot: {e}")
        print(f"❌ Error: {e}")
        return 1


def run_bot_in_background():
    """Run the bot in a background thread"""
    def bot_thread():
        try:
            logger = get_safe_logger("telegram_bot_bg")
            bot = TradingTelegramBot(logger=logger)
            bot.start()
        except Exception as e:
            print(f"❌ Background bot error: {e}")
    
    # Check if environment is properly configured
    if not setup_environment():
        print("⚠️ Telegram bot not started due to missing configuration")
        return None
    
    try:
        # Reload configuration from environment variables
        telegram_config.reload_from_env()
        telegram_config.validate()
        thread = threading.Thread(target=bot_thread, daemon=True)
        thread.start()
        print("🤖 Telegram bot started in background")
        return thread
    except Exception as e:
        print(f"⚠️ Failed to start Telegram bot in background: {e}")
        return None


def test_bot_connection():
    """Test bot connection and configuration"""
    print("🧪 Testing Telegram bot connection...")
    
    if not setup_environment():
        return False
    
    try:
        # Reload configuration from environment variables
        telegram_config.reload_from_env()
        telegram_config.validate()

        # Try to create bot instance
        logger = get_safe_logger("telegram_test")
        bot = TradingTelegramBot(logger=logger)
        
        # Test sending a message
        test_message = f"""
🧪 *Bot Connection Test*

✅ Bot is properly configured and connected!
🕐 Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This is a test message to verify the bot is working correctly.
"""
        
        # Send test message to all authorized users
        all_users = set(telegram_config.authorized_users + telegram_config.admin_users)
        for user_id in all_users:
            try:
                bot.bot.send_message(user_id, test_message, parse_mode='Markdown')
                print(f"✅ Test message sent to user {user_id}")
            except Exception as e:
                print(f"❌ Failed to send test message to {user_id}: {e}")
                return False
        
        print("✅ Bot connection test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Bot connection test failed: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Telegram Trading Bot Launcher")
    parser.add_argument("--test", action="store_true", help="Test bot connection")
    parser.add_argument("--background", action="store_true", help="Run bot in background")
    parser.add_argument("--create-env", action="store_true", help="Create sample environment file")
    
    args = parser.parse_args()
    
    if args.create_env:
        create_sample_env_file()
    elif args.test:
        test_bot_connection()
    elif args.background:
        thread = run_bot_in_background()
        if thread:
            try:
                # Keep the main thread alive
                while thread.is_alive():
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Background bot stopped")
    else:
        sys.exit(main())
