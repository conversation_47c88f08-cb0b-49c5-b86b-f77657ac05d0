"""
Enhanced Stop-Loss Integration Example
Demonstrates how to integrate and use the enhanced SL mechanism
"""
import logging
import time
from datetime import datetime
from typing import Optional

# Enhanced SL imports
from services.position_monitor import EnhancedPositionMonitor, StopLossConfig
from services.enhanced_order_service import EnhancedSmartAPIOrderService, OrderExecutionConfig
from config.enhanced_sl_config import get_sl_config, get_order_config, config_manager
from monitoring.sl_performance_monitor import SLPerformanceMonitor

# Standard imports
from services.market_data_service import MarketDataService
from services.websocket_service import LivePriceWebSocket
from services.trading_logger import TradingLogger
from models.order import Order, TransactionType, OrderType, ProductType


class EnhancedTradingSystem:
    """Enhanced trading system with optimized stop-loss mechanism"""
    
    def __init__(self, api_key: str, client_id: str, client_pin: str, totp_key: str,
                 environment: str = "production"):
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Load configuration based on environment
        if environment == "production":
            config_manager.get_production_config()
        else:
            config_manager.get_development_config()
        
        # Validate configuration
        config_issues = config_manager.validate_config()
        if config_issues:
            self.logger.warning(f"Configuration issues found: {config_issues}")
        
        # Print configuration summary
        config_manager.print_config_summary()
        
        # Initialize enhanced services
        self.order_service = EnhancedSmartAPIOrderService(
            api_key=api_key,
            client_id=client_id,
            client_pin=client_pin,
            totp_key=totp_key,
            logger=self.logger,
            config=get_order_config()
        )
        
        self.market_data_service = MarketDataService(logger=self.logger)
        self.websocket_service = LivePriceWebSocket(logger=self.logger)
        self.trading_logger = TradingLogger(logger=self.logger)
        
        # Initialize performance monitor
        self.performance_monitor = SLPerformanceMonitor(logger=self.logger)
        
        # Initialize enhanced position monitor
        self.position_monitor = EnhancedPositionMonitor(
            order_service=self.order_service,
            market_data_service=self.market_data_service,
            logger=self.logger,
            websocket_service=self.websocket_service,
            trading_logger=self.trading_logger,
            sl_config=get_sl_config()
        )
        
        # System state
        self.system_active = False
        self.emergency_mode = False
        
        self.logger.info("🚀 Enhanced Trading System initialized successfully")

    def _setup_logging(self) -> logging.Logger:
        """Setup enhanced logging"""
        logger = logging.getLogger("EnhancedTradingSystem")
        logger.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler for enhanced SL logs
        file_handler = logging.FileHandler('enhanced_sl_system.log')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger

    def start_system(self):
        """Start the enhanced trading system"""
        try:
            self.logger.info("🔄 Starting Enhanced Trading System...")
            
            # Authenticate with broker
            if not self.order_service.authenticate():
                raise Exception("Failed to authenticate with broker")
            
            # Start WebSocket connection
            self.websocket_service.start()
            
            # Start performance monitoring
            self.performance_monitor.start_monitoring()
            
            # Start position monitoring
            self.position_monitor.start_monitoring()
            
            self.system_active = True
            self.logger.info("✅ Enhanced Trading System started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start system: {e}")
            raise

    def stop_system(self):
        """Stop the enhanced trading system"""
        try:
            self.logger.info("🔄 Stopping Enhanced Trading System...")
            
            # Stop position monitoring
            self.position_monitor.stop_monitoring()
            
            # Stop performance monitoring
            self.performance_monitor.stop_monitoring()
            
            # Stop WebSocket
            self.websocket_service.stop()
            
            self.system_active = False
            self.logger.info("✅ Enhanced Trading System stopped successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping system: {e}")

    def place_order_with_enhanced_sl(self, symbol: str, quantity: int, entry_price: float,
                                   stop_loss: float, target: float, 
                                   transaction_type: TransactionType) -> Optional[Order]:
        """Place order with enhanced stop-loss monitoring"""
        try:
            if not self.system_active:
                self.logger.error("System not active, cannot place order")
                return None
            
            # Get symbol token (you'll need to implement this based on your token service)
            token = self._get_symbol_token(symbol)
            if not token:
                self.logger.error(f"Could not get token for symbol {symbol}")
                return None
            
            # Place the order
            order = self.order_service.place_order(
                symbol=symbol,
                token=token,
                exchange="NSE",
                transaction_type=transaction_type,
                entry_price=entry_price,
                stop_loss=stop_loss,
                target=target,
                quantity=quantity
            )
            
            if order:
                self.logger.info(f"✅ Order placed with enhanced SL: {symbol} {quantity} @ ₹{entry_price}")
                self.logger.info(f"📊 SL: ₹{stop_loss}, Target: ₹{target}")
                
                # The enhanced position monitor will automatically track this position
                # and execute immediate exits when SL/target is hit
                
            return order
            
        except Exception as e:
            self.logger.error(f"Error placing order with enhanced SL: {e}")
            return None

    def _get_symbol_token(self, symbol: str) -> Optional[str]:
        """Get symbol token - implement based on your token service"""
        # This is a placeholder - implement based on your token mapping service
        token_map = {
            "RELIANCE": "738249",
            "TCS": "11536",
            "INFY": "1594",
            # Add more symbols as needed
        }
        return token_map.get(symbol)

    def enable_emergency_mode(self):
        """Enable emergency mode for immediate exits"""
        self.emergency_mode = True
        self.position_monitor.enable_emergency_mode()
        self.logger.error("🚨 EMERGENCY MODE ENABLED")

    def disable_emergency_mode(self):
        """Disable emergency mode"""
        self.emergency_mode = False
        self.position_monitor.disable_emergency_mode()
        self.logger.info("✅ Emergency mode disabled")

    def emergency_square_off_all(self) -> bool:
        """Emergency square-off all positions"""
        try:
            self.logger.error("🚨 EMERGENCY SQUARE-OFF ALL POSITIONS")
            return self.order_service.emergency_square_off_all()
        except Exception as e:
            self.logger.error(f"Error in emergency square-off: {e}")
            return False

    def get_system_status(self) -> dict:
        """Get comprehensive system status"""
        try:
            # Get performance metrics
            performance = self.performance_monitor.get_performance_summary()
            
            # Get position monitor status
            monitor_status = self.position_monitor.get_monitoring_status()
            
            # Get order service performance
            order_performance = self.order_service.get_execution_performance()
            
            # Get enhanced position monitor performance
            enhanced_performance = self.position_monitor.get_performance_metrics()
            
            return {
                'system_active': self.system_active,
                'emergency_mode': self.emergency_mode,
                'performance_monitor': performance,
                'position_monitor': monitor_status,
                'order_service_performance': order_performance,
                'enhanced_sl_performance': enhanced_performance,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}

    def export_performance_report(self, filepath: str = None):
        """Export comprehensive performance report"""
        if not filepath:
            filepath = f"enhanced_sl_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            self.performance_monitor.export_performance_data(filepath)
            self.logger.info(f"📁 Performance report exported to {filepath}")
        except Exception as e:
            self.logger.error(f"Error exporting performance report: {e}")

    def run_system_health_check(self) -> bool:
        """Run comprehensive system health check"""
        try:
            self.logger.info("🔍 Running system health check...")
            
            # Check API authentication
            if not self.order_service._authenticated:
                self.logger.error("❌ API not authenticated")
                return False
            
            # Check WebSocket connection
            if not self.websocket_service.is_connected():
                self.logger.warning("⚠️ WebSocket not connected")
            
            # Check performance metrics
            performance = self.performance_monitor.get_performance_summary()
            if 'health_status' in performance:
                health = performance['health_status']
                if health == 'CRITICAL':
                    self.logger.error(f"❌ Performance health is CRITICAL")
                    return False
                elif health == 'WARNING':
                    self.logger.warning(f"⚠️ Performance health is WARNING")
            
            # Check configuration
            config_issues = config_manager.validate_config()
            if config_issues:
                self.logger.warning(f"⚠️ Configuration issues: {config_issues}")
            
            self.logger.info("✅ System health check completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error in health check: {e}")
            return False


def main():
    """Example usage of the enhanced SL system"""
    # Initialize system (replace with your actual credentials)
    system = EnhancedTradingSystem(
        api_key="your_api_key",
        client_id="your_client_id",
        client_pin="your_pin",
        totp_key="your_totp_key",
        environment="development"  # or "production"
    )
    
    try:
        # Start the system
        system.start_system()
        
        # Run health check
        if not system.run_system_health_check():
            print("❌ System health check failed")
            return
        
        # Example: Place an order with enhanced SL
        order = system.place_order_with_enhanced_sl(
            symbol="RELIANCE",
            quantity=10,
            entry_price=2500.0,
            stop_loss=2450.0,
            target=2600.0,
            transaction_type=TransactionType.BUY
        )
        
        if order:
            print(f"✅ Order placed: {order.order_id}")
            
            # Monitor for a while
            print("🔍 Monitoring positions for 60 seconds...")
            time.sleep(60)
            
            # Get system status
            status = system.get_system_status()
            print(f"📊 System Status: {status}")
            
            # Export performance report
            system.export_performance_report()
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Stop the system
        system.stop_system()


if __name__ == "__main__":
    main()
