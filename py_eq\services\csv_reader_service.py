"""
CSV Reader Service for Stock Monitoring

This service reads stock symbols and their assigned strategies from CSV files,
providing strategy-specific filtering and symbol management.

Features:
- CSV file parsing
- Strategy-specific symbol filtering
- Enabled/disabled stock management
- Timeframe mapping per strategy
- Real-time CSV reloading
"""

import logging
import csv
import os
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from pathlib import Path


@dataclass
class StockInfo:
    """Stock information from CSV"""
    symbol: str
    strategy: str
    timeframe: str
    enabled: bool
    notes: str = ""


class CSVReaderService:
    """Service for reading and managing stock data from CSV files"""
    
    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path
        self.logger = logging.getLogger(__name__)
        
        # Stock data storage
        self.stocks: List[StockInfo] = []
        self.stocks_by_strategy: Dict[str, List[StockInfo]] = {}
        self.enabled_stocks: List[StockInfo] = []
        
        # Statistics
        self.stats = {
            'total_stocks': 0,
            'enabled_stocks': 0,
            'strategy_distribution': {},
            'last_loaded': None
        }
        
        # Strategy timeframe mapping
        self.strategy_timeframes = {
            'MA_CROSSOVER': '15min',
            'SUPPORT_RESISTANCE': '15min',
            'GAPANDGO': '5min',
            'ORB': '5min'
        }
    
    def load_stocks(self) -> bool:
        """Load stocks from CSV file"""
        try:
            if not os.path.exists(self.csv_file_path):
                self.logger.error(f"CSV file not found: {self.csv_file_path}")
                return False
            
            self.stocks.clear()
            self.stocks_by_strategy.clear()
            self.enabled_stocks.clear()
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                for row in reader:
                    try:
                        # Parse row data
                        symbol = row.get('symbol', '').strip().upper()
                        strategy = row.get('strategy', '').strip().upper()
                        timeframe = row.get('timeframe', '').strip()
                        enabled = str(row.get('enabled', 'true')).strip().lower() == 'true'
                        notes = row.get('notes', '').strip()
                        
                        if not symbol or not strategy:
                            continue
                        
                        # Use default timeframe if not specified
                        if not timeframe:
                            timeframe = self.strategy_timeframes.get(strategy, '15min')
                        
                        stock_info = StockInfo(
                            symbol=symbol,
                            strategy=strategy,
                            timeframe=timeframe,
                            enabled=enabled,
                            notes=notes
                        )
                        
                        self.stocks.append(stock_info)
                        
                        # Group by strategy
                        if strategy not in self.stocks_by_strategy:
                            self.stocks_by_strategy[strategy] = []
                        self.stocks_by_strategy[strategy].append(stock_info)
                        
                        # Add to enabled stocks if enabled
                        if enabled:
                            self.enabled_stocks.append(stock_info)
                            
                    except Exception as e:
                        self.logger.warning(f"Error parsing CSV row: {row}, error: {e}")
                        continue
            
            # Update statistics
            self._update_statistics()
            
            self.logger.info(f"✅ Loaded {len(self.stocks)} stocks from CSV")
            self.logger.info(f"📊 Strategy distribution: {self.stats['strategy_distribution']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading stocks from CSV: {e}")
            return False
    
    def _update_statistics(self):
        """Update internal statistics"""
        from datetime import datetime
        
        self.stats['total_stocks'] = len(self.stocks)
        self.stats['enabled_stocks'] = len(self.enabled_stocks)
        self.stats['last_loaded'] = datetime.now()
        
        # Calculate strategy distribution
        strategy_counts = {}
        for stock in self.enabled_stocks:
            strategy = stock.strategy
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        self.stats['strategy_distribution'] = strategy_counts
    
    def get_all_stocks(self) -> List[StockInfo]:
        """Get all stocks"""
        return self.stocks.copy()
    
    def get_enabled_stocks(self) -> List[StockInfo]:
        """Get only enabled stocks"""
        return self.enabled_stocks.copy()
    
    def get_stocks_by_strategy(self, strategy: str) -> List[StockInfo]:
        """Get stocks for a specific strategy"""
        strategy = strategy.upper()
        return self.stocks_by_strategy.get(strategy, []).copy()
    
    def get_enabled_stocks_by_strategy(self, strategy: str) -> List[StockInfo]:
        """Get enabled stocks for a specific strategy"""
        strategy = strategy.upper()
        stocks = self.stocks_by_strategy.get(strategy, [])
        return [stock for stock in stocks if stock.enabled]
    
    def get_symbols_by_strategy(self, strategy: str, enabled_only: bool = True) -> List[str]:
        """Get symbol names for a specific strategy"""
        if enabled_only:
            stocks = self.get_enabled_stocks_by_strategy(strategy)
        else:
            stocks = self.get_stocks_by_strategy(strategy)
        
        return [stock.symbol for stock in stocks]
    
    def get_all_symbols(self, enabled_only: bool = True) -> List[str]:
        """Get all symbol names"""
        if enabled_only:
            return [stock.symbol for stock in self.enabled_stocks]
        else:
            return [stock.symbol for stock in self.stocks]
    
    def get_strategy_for_symbol(self, symbol: str) -> Optional[str]:
        """Get strategy assigned to a symbol"""
        symbol = symbol.upper()
        for stock in self.stocks:
            if stock.symbol == symbol:
                return stock.strategy
        return None
    
    def get_timeframe_for_symbol(self, symbol: str) -> Optional[str]:
        """Get timeframe for a symbol"""
        symbol = symbol.upper()
        for stock in self.stocks:
            if stock.symbol == symbol:
                return stock.timeframe
        return None
    
    def get_timeframe_for_strategy(self, strategy: str) -> str:
        """Get default timeframe for a strategy"""
        return self.strategy_timeframes.get(strategy.upper(), '15min')
    
    def is_symbol_enabled(self, symbol: str) -> bool:
        """Check if a symbol is enabled"""
        symbol = symbol.upper()
        for stock in self.stocks:
            if stock.symbol == symbol:
                return stock.enabled
        return False
    
    def get_strategy_statistics(self) -> Dict:
        """Get detailed statistics about strategies"""
        stats = {
            'total_strategies': len(self.stocks_by_strategy),
            'strategies': {}
        }
        
        for strategy, stocks in self.stocks_by_strategy.items():
            enabled_count = sum(1 for stock in stocks if stock.enabled)
            timeframes = set(stock.timeframe for stock in stocks)
            
            stats['strategies'][strategy] = {
                'total_stocks': len(stocks),
                'enabled_stocks': enabled_count,
                'disabled_stocks': len(stocks) - enabled_count,
                'timeframes': list(timeframes),
                'default_timeframe': self.strategy_timeframes.get(strategy, '15min')
            }
        
        return stats
    
    def get_symbols_for_timeframe(self, timeframe: str, enabled_only: bool = True) -> List[str]:
        """Get symbols that use a specific timeframe"""
        symbols = []
        stocks = self.enabled_stocks if enabled_only else self.stocks
        
        for stock in stocks:
            if stock.timeframe == timeframe:
                symbols.append(stock.symbol)
        
        return symbols
    
    def reload_csv(self) -> bool:
        """Reload CSV file"""
        self.logger.info("Reloading CSV file...")
        return self.load_stocks()
    
    def validate_csv_structure(self) -> Dict:
        """Validate CSV file structure and content"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'summary': {}
        }
        
        try:
            if not os.path.exists(self.csv_file_path):
                validation_result['valid'] = False
                validation_result['errors'].append(f"CSV file not found: {self.csv_file_path}")
                return validation_result
            
            # Check required columns
            required_columns = ['symbol', 'strategy', 'timeframe', 'enabled']
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                
                # Check headers
                headers = reader.fieldnames or []
                missing_columns = [col for col in required_columns if col not in headers]
                
                if missing_columns:
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Missing required columns: {missing_columns}")
                
                # Check data
                row_count = 0
                empty_symbols = 0
                invalid_strategies = 0
                invalid_timeframes = 0
                
                valid_strategies = set(self.strategy_timeframes.keys())
                valid_timeframes = {'5min', '15min', '1min', '30min', '1h', '1d'}
                
                for row_num, row in enumerate(reader, start=2):  # Start from 2 (header is row 1)
                    row_count += 1
                    
                    symbol = row.get('symbol', '').strip()
                    strategy = row.get('strategy', '').strip().upper()
                    timeframe = row.get('timeframe', '').strip()
                    
                    if not symbol:
                        empty_symbols += 1
                        validation_result['warnings'].append(f"Row {row_num}: Empty symbol")
                    
                    if strategy and strategy not in valid_strategies:
                        invalid_strategies += 1
                        validation_result['warnings'].append(f"Row {row_num}: Unknown strategy '{strategy}'")
                    
                    if timeframe and timeframe not in valid_timeframes:
                        invalid_timeframes += 1
                        validation_result['warnings'].append(f"Row {row_num}: Invalid timeframe '{timeframe}'")
                
                validation_result['summary'] = {
                    'total_rows': row_count,
                    'empty_symbols': empty_symbols,
                    'invalid_strategies': invalid_strategies,
                    'invalid_timeframes': invalid_timeframes,
                    'valid_strategies': list(valid_strategies),
                    'valid_timeframes': list(valid_timeframes)
                }
                
        except Exception as e:
            validation_result['valid'] = False
            validation_result['errors'].append(f"Error validating CSV: {e}")
        
        return validation_result
    
    def get_statistics(self) -> Dict:
        """Get comprehensive service statistics"""
        return {
            **self.stats,
            'csv_file_path': self.csv_file_path,
            'file_exists': os.path.exists(self.csv_file_path),
            'strategy_timeframes': self.strategy_timeframes
        }
