#!/usr/bin/env python3
"""
Download and populate symbol mappings in MongoDB
This script downloads equity mappings for stocks in stocks_to_monitor.csv
"""

import os
import sys
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.symbol_mapping_service import SymbolMappingService
from services.stock_monitor import StockMonitor

def setup_logging():
    """Setup logging configuration"""
    logger = logging.getLogger('SymbolMappingDownloader')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(console_handler)
    
    return logger

def download_symbol_mappings():
    """Download and populate symbol mappings in MongoDB"""
    logger = setup_logging()
    
    logger.info("=" * 50)
    logger.info("DOWNLOADING SYMBOL MAPPINGS")
    logger.info("=" * 50)
    
    # Initialize services
    mapping_service = SymbolMappingService(logger=logger)
    stock_monitor = StockMonitor(data_directory="data", logger=logger)
    
    # Get monitored stocks
    monitored_stocks = stock_monitor.get_monitored_stocks()
    monitored_symbols = [stock.symbol for stock in monitored_stocks]
    
    logger.info(f"Found {len(monitored_symbols)} symbols in stocks_to_monitor.csv")
    
    # Download SmartAPI master data
    logger.info("Downloading SmartAPI master data...")
    master_df = mapping_service.download_smartapi_master_data()
    
    if master_df is None:
        logger.error("Failed to download SmartAPI master data")
        return False
    
    # Process equity mappings for monitored symbols
    logger.info(f"Processing equity mappings for {len(monitored_symbols)} symbols...")
    equity_count = mapping_service.process_equity_mappings(master_df, monitored_symbols)
    
    # Process index mappings
    logger.info("Processing index mappings...")
    index_count = mapping_service.process_index_mappings(master_df)
    
    # Get mapping statistics
    stats = mapping_service.get_mapping_stats()
    
    logger.info("\nMAPPING STATISTICS:")
    logger.info(f"  Equity mappings: {stats.get('equity_count', 0)}")
    logger.info(f"  Option mappings: {stats.get('option_count', 0)}")
    logger.info(f"  Index mappings: {stats.get('index_count', 0)}")
    
    # Check if all monitored symbols have mappings
    logger.info("\nCHECKING MONITORED SYMBOLS:")
    missing_symbols = []
    
    for symbol in monitored_symbols:
        mapping = mapping_service.get_equity_mapping(symbol)
        if mapping:
            logger.info(f"  ✅ {symbol}: Token={mapping.get('smartapi_token', 'N/A')}")
        else:
            logger.warning(f"  ❌ {symbol}: No mapping found")
            missing_symbols.append(symbol)
    
    if missing_symbols:
        logger.warning(f"\nMISSING MAPPINGS FOR {len(missing_symbols)} SYMBOLS:")
        for symbol in missing_symbols:
            logger.warning(f"  - {symbol}")
    else:
        logger.info("\nAll monitored symbols have mappings!")
    
    logger.info("=" * 50)
    logger.info("DOWNLOAD COMPLETED")
    logger.info("=" * 50)
    
    mapping_service.close()
    return True

if __name__ == "__main__":
    download_symbol_mappings()