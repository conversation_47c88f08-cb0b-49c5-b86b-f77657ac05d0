#!/usr/bin/env python3
"""
LLM Interface Agent - Natural Language Interface for Trading System

Features:
🗣️ 1. Natural Language Querying
- Plain English queries for system state and performance
- Auto query generation for backtest database
- Drill-down analysis and regime explanations

🧑‍[SYSTEM] 2. Code Suggestion and Auto-Patching
- Fix strategy expressions and YAML configurations
- Modify pipeline code and generate new strategies
- Auto-prompt optimization with Optuna

📤 3. Agent-Oriented Command Execution
- Route commands to appropriate agents
- Interactive debugging and explanations
- Auto-documentation and knowledge graph

[CONNECT] 4. Multi-Model Integration
- Ollama model selection per task type
- LangChain and LangGraph for advanced workflows
- Angel One API integration for real-time data

Author: AI Assistant
Date: 2025-01-16
"""

import asyncio
import logging
import yaml
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from pathlib import Path
import importlib
from collections import defaultdict, deque

# LangChain and LangGraph imports
try:
    from langchain_ollama import ChatOllama
    from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
    from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
    from langchain_core.output_parsers import StrOutputParser
    from langgraph.graph import StateGraph, END
    from langgraph.graph.message import add_messages
    from typing_extensions import TypedDict
except ImportError as e:
    logging.warning(f"LangChain/LangGraph not available: {e}")
    ChatOllama = None

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class QueryRequest:
    """User query request"""
    query: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class QueryResponse:
    """LLM response"""
    response: str
    agent_used: Optional[str] = None
    model_used: Optional[str] = None
    processing_time_ms: float = 0
    confidence: float = 0.0
    suggestions: List[str] = None
    code_generated: Optional[str] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []

@dataclass
class ModelCapability:
    """Model capability definition"""
    name: str
    description: str
    use_cases: List[str]
    temperature: float
    max_tokens: int
    strengths: List[str] = None
    weaknesses: List[str] = None
    
    def __post_init__(self):
        if self.strengths is None:
            self.strengths = []
        if self.weaknesses is None:
            self.weaknesses = []

# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 LLM INTERFACE AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class LLMInterfaceAgent:
    """
    LLM Interface Agent for natural language interaction with trading system
    
    Provides intelligent routing to appropriate models and agents based on query type,
    with support for code generation, debugging, and real-time trading operations.
    """
    
    def __init__(self, config_path: str = "config/llm_interface_config.yaml"):
        """Initialize LLM Interface Agent"""
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Setup logging
        self._setup_logging()
        
        # Initialize components
        self.models = {}
        self.agents = {}
        self.query_history = deque(maxlen=1000)
        self.session_contexts = defaultdict(dict)
        
        # Performance tracking
        self.performance_metrics = {
            'queries_processed': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'avg_processing_time': 0.0,
            'model_usage': defaultdict(int),
            'agent_usage': defaultdict(int)
        }
        
        # State management
        self.is_running = False
        self.startup_time = None
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("🧠 LLM Interface Agent initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config.get('llm_interface', {})
        except Exception as e:
            logging.error(f"[ERROR] Error loading config: {e}")
            return {}
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('system', {}).get('logging', {})
        
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_config.get('file', 'logs/llm_interface.log')),
                logging.StreamHandler()
            ]
        )
    
    async def initialize(self) -> bool:
        """Initialize the LLM Interface Agent"""
        try:
            self.logger.info("[INIT] Initializing LLM Interface Agent...")
            
            # Initialize Ollama models
            await self._initialize_models()
            
            # Initialize agent connections
            await self._initialize_agents()
            
            # Setup LangGraph workflow
            await self._setup_langgraph()
            
            # Validate system health
            if not await self._health_check():
                return False
            
            self.is_running = True
            self.startup_time = datetime.now()
            self.logger.info("[SUCCESS] LLM Interface Agent initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error initializing LLM Interface Agent: {e}")
            return False
    
    async def _initialize_models(self):
        """Initialize Ollama models"""
        try:
            if ChatOllama is None:
                self.logger.warning("[WARN] LangChain not available, using fallback mode")
                return
            
            models_config = self.config.get('models', {})
            ollama_config = self.config.get('system', {}).get('ollama', {})
            
            for model_type, model_config in models_config.items():
                model_name = model_config['model']
                
                # Create ChatOllama instance
                self.models[model_type] = ChatOllama(
                    model=model_name,
                    base_url=ollama_config.get('base_url', 'http://localhost:11434'),
                    temperature=model_config.get('temperature', 0.7),
                    num_predict=model_config.get('max_tokens', 2048),
                    timeout=ollama_config.get('timeout', 30)
                )
                
                self.logger.info(f"[SUCCESS] Initialized model: {model_name} for {model_type}")
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error initializing models: {e}")
    
    async def _initialize_agents(self):
        """Initialize connections to other agents"""
        try:
            agents_config = self.config.get('agents', {})
            
            for agent_name, agent_config in agents_config.items():
                try:
                    # Dynamic import of agent module
                    module_name = agent_config['module']
                    class_name = agent_config['class']
                    config_path = agent_config['config_path']
                    
                    module = importlib.import_module(module_name)
                    agent_class = getattr(module, class_name)
                    
                    # Initialize agent
                    agent_instance = agent_class(config_path)
                    self.agents[agent_name] = {
                        'instance': agent_instance,
                        'methods': agent_config.get('methods', {}),
                        'initialized': False
                    }
                    
                    self.logger.info(f"[SUCCESS] Connected to agent: {agent_name}")
                    
                except Exception as e:
                    self.logger.warning(f"[WARN] Could not connect to {agent_name}: {e}")
                    
        except Exception as e:
            self.logger.error(f"[ERROR] Error initializing agents: {e}")
    
    async def _setup_langgraph(self):
        """Setup LangGraph workflow for complex query processing"""
        try:
            if ChatOllama is None:
                return
            
            # Define state for LangGraph
            class State(TypedDict):
                messages: List[Union[HumanMessage, AIMessage, SystemMessage]]
                query_type: str
                selected_model: str
                selected_agent: str
                context: Dict[str, Any]
                result: str
            
            # Create workflow graph
            workflow = StateGraph(State)
            
            # Add nodes
            workflow.add_node("classify_query", self._classify_query_node)
            workflow.add_node("select_model", self._select_model_node)
            workflow.add_node("route_to_agent", self._route_to_agent_node)
            workflow.add_node("generate_response", self._generate_response_node)
            
            # Add edges
            workflow.set_entry_point("classify_query")
            workflow.add_edge("classify_query", "select_model")
            workflow.add_edge("select_model", "route_to_agent")
            workflow.add_edge("route_to_agent", "generate_response")
            workflow.add_edge("generate_response", END)
            
            self.workflow = workflow.compile()
            self.logger.info("[SUCCESS] LangGraph workflow setup complete")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Error setting up LangGraph: {e}")
    
    async def _health_check(self) -> bool:
        """Perform system health check"""
        try:
            # Check Ollama connection
            if self.models:
                test_model = list(self.models.values())[0]
                test_response = await test_model.ainvoke([HumanMessage(content="Hello")])
                if not test_response:
                    self.logger.error("[ERROR] Ollama health check failed")
                    return False

            self.logger.info("[SUCCESS] System health check passed")
            return True

        except Exception as e:
            self.logger.error(f"[ERROR] Health check failed: {e}")
            return False

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔀 QUERY PROCESSING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def process_query(self, query_request: QueryRequest) -> QueryResponse:
        """Main query processing method"""
        start_time = datetime.now()

        try:
            self.logger.info(f"[DEBUG] Processing query: {query_request.query[:100]}...")

            # Update metrics
            self.performance_metrics['queries_processed'] += 1

            # Add to history
            self.query_history.append({
                'timestamp': query_request.timestamp,
                'query': query_request.query,
                'user_id': query_request.user_id,
                'session_id': query_request.session_id
            })

            # Process using LangGraph if available, otherwise fallback
            if hasattr(self, 'workflow') and self.workflow:
                response = await self._process_with_langgraph(query_request)
            else:
                response = await self._process_with_fallback(query_request)

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            response.processing_time_ms = processing_time

            # Update metrics
            if response.error:
                self.performance_metrics['failed_queries'] += 1
            else:
                self.performance_metrics['successful_queries'] += 1

            # Update average processing time
            total_queries = self.performance_metrics['queries_processed']
            current_avg = self.performance_metrics['avg_processing_time']
            self.performance_metrics['avg_processing_time'] = (
                (current_avg * (total_queries - 1) + processing_time) / total_queries
            )

            # Update usage metrics
            if response.model_used:
                self.performance_metrics['model_usage'][response.model_used] += 1
            if response.agent_used:
                self.performance_metrics['agent_usage'][response.agent_used] += 1

            self.logger.info(f"[SUCCESS] Query processed in {processing_time:.2f}ms")
            return response

        except Exception as e:
            self.logger.error(f"[ERROR] Error processing query: {e}")
            self.performance_metrics['failed_queries'] += 1

            return QueryResponse(
                response=f"[ERROR] Error processing your request: {str(e)}",
                error=str(e),
                processing_time_ms=(datetime.now() - start_time).total_seconds() * 1000
            )

    async def _process_with_langgraph(self, query_request: QueryRequest) -> QueryResponse:
        """Process query using LangGraph workflow"""
        try:
            # Prepare initial state
            initial_state = {
                "messages": [HumanMessage(content=query_request.query)],
                "query_type": "",
                "selected_model": "",
                "selected_agent": "",
                "context": query_request.context or {},
                "result": ""
            }

            # Run workflow
            result = await self.workflow.ainvoke(initial_state)

            return QueryResponse(
                response=result["result"],
                agent_used=result["selected_agent"],
                model_used=result["selected_model"],
                confidence=0.8  # Default confidence for LangGraph processing
            )

        except Exception as e:
            self.logger.error(f"[ERROR] LangGraph processing failed: {e}")
            return await self._process_with_fallback(query_request)

    async def _process_with_fallback(self, query_request: QueryRequest) -> QueryResponse:
        """Fallback query processing without LangGraph"""
        try:
            # Classify query type
            query_type = self._classify_query(query_request.query)

            # Select appropriate model
            model_type = self._select_model_for_query(query_type)

            # Route to appropriate agent or handle directly
            if query_type in ['backtest_queries', 'trading_queries', 'risk_queries', 'market_queries']:
                return await self._route_to_agent(query_request, query_type)
            else:
                return await self._generate_direct_response(query_request, model_type)

        except Exception as e:
            self.logger.error(f"[ERROR] Fallback processing failed: {e}")
            return QueryResponse(
                response="[ERROR] Unable to process your request. Please try again.",
                error=str(e)
            )

    def _classify_query(self, query: str) -> str:
        """Classify query type based on keywords"""
        query_lower = query.lower()
        routing_config = self.config.get('routing', {}).get('patterns', {})

        # Score each query type
        scores = {}
        for query_type, config in routing_config.items():
            keywords = config.get('keywords', [])
            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                scores[query_type] = score

        # Return highest scoring type or default
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        else:
            return 'quick_queries'  # Default fallback

    def _select_model_for_query(self, query_type: str) -> str:
        """Select appropriate model based on query type"""
        routing_config = self.config.get('routing', {}).get('patterns', {})

        if query_type in routing_config:
            return routing_config[query_type].get('model', 'general_reasoning')
        else:
            return 'general_reasoning'  # Default model

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔀 LANGGRAPH NODE METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _classify_query_node(self, state):
        """LangGraph node for query classification"""
        try:
            query = state["messages"][-1].content
            query_type = self._classify_query(query)
            state["query_type"] = query_type

            self.logger.info(f"[DEBUG] Classified query as: {query_type}")
            return state

        except Exception as e:
            self.logger.error(f"[ERROR] Error in classify_query_node: {e}")
            state["query_type"] = "quick_queries"
            return state

    async def _select_model_node(self, state):
        """LangGraph node for model selection"""
        try:
            query_type = state["query_type"]
            model_type = self._select_model_for_query(query_type)
            state["selected_model"] = model_type

            self.logger.info(f"🧠 Selected model: {model_type}")
            return state

        except Exception as e:
            self.logger.error(f"[ERROR] Error in select_model_node: {e}")
            state["selected_model"] = "general_reasoning"
            return state

    async def _route_to_agent_node(self, state):
        """LangGraph node for agent routing"""
        try:
            query_type = state["query_type"]
            routing_config = self.config.get('routing', {}).get('patterns', {})

            if query_type in routing_config:
                agent_name = routing_config[query_type].get('agent', 'direct_response')
                state["selected_agent"] = agent_name
            else:
                state["selected_agent"] = "direct_response"

            self.logger.info(f"[TARGET] Routed to agent: {state['selected_agent']}")
            return state

        except Exception as e:
            self.logger.error(f"[ERROR] Error in route_to_agent_node: {e}")
            state["selected_agent"] = "direct_response"
            return state

    async def _generate_response_node(self, state):
        """LangGraph node for response generation"""
        try:
            query = state["messages"][-1].content
            model_type = state["selected_model"]
            agent_name = state["selected_agent"]

            # Generate response based on routing
            if agent_name == "direct_response":
                response = await self._generate_model_response(query, model_type)
            else:
                response = await self._call_agent_method(query, agent_name)

            state["result"] = response
            return state

        except Exception as e:
            self.logger.error(f"[ERROR] Error in generate_response_node: {e}")
            state["result"] = f"[ERROR] Error generating response: {str(e)}"
            return state

    # ═══════════════════════════════════════════════════════════════════════════════
    # [AGENT] AGENT COMMUNICATION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _route_to_agent(self, query_request: QueryRequest, query_type: str) -> QueryResponse:
        """Route query to appropriate agent"""
        try:
            routing_config = self.config.get('routing', {}).get('patterns', {})
            agent_name = routing_config.get(query_type, {}).get('agent', 'direct_response')

            if agent_name == 'direct_response':
                model_type = self._select_model_for_query(query_type)
                response_text = await self._generate_model_response(query_request.query, model_type)
                return QueryResponse(
                    response=response_text,
                    model_used=model_type,
                    confidence=0.7
                )

            # Call specific agent
            response_text = await self._call_agent_method(query_request.query, agent_name)

            return QueryResponse(
                response=response_text,
                agent_used=agent_name,
                model_used=self._select_model_for_query(query_type),
                confidence=0.8
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error routing to agent: {e}")
            return QueryResponse(
                response=f"[ERROR] Error routing to agent: {str(e)}",
                error=str(e)
            )

    async def _call_agent_method(self, query: str, agent_name: str) -> str:
        """Call method on specific agent"""
        try:
            if agent_name not in self.agents:
                return f"[ERROR] Agent '{agent_name}' not available"

            agent_info = self.agents[agent_name]
            agent_instance = agent_info['instance']

            # Handle different agent types
            if agent_name == 'performance_analysis_agent':
                return await self._query_performance_agent(query, agent_instance)
            elif agent_name == 'market_monitoring_agent':
                return await self._query_market_agent(query, agent_instance)
            elif agent_name == 'signal_generation_agent':
                return await self._query_signal_agent(query, agent_instance)
            elif agent_name == 'risk_agent':
                return await self._query_risk_agent(query, agent_instance)
            elif agent_name == 'execution_agent':
                return await self._query_execution_agent(query, agent_instance)
            else:
                return f"[ERROR] Unknown agent type: {agent_name}"

        except Exception as e:
            self.logger.error(f"[ERROR] Error calling agent method: {e}")
            return f"[ERROR] Error communicating with {agent_name}: {str(e)}"

    async def _generate_model_response(self, query: str, model_type: str) -> str:
        """Generate response using specified model"""
        try:
            if model_type not in self.models:
                return f"[ERROR] Model '{model_type}' not available"

            model = self.models[model_type]

            # Create appropriate prompt based on model type
            if model_type == 'code_generation':
                prompt = self._create_code_prompt(query)
            elif model_type == 'code_explanation':
                prompt = self._create_explanation_prompt(query)
            else:
                prompt = self._create_general_prompt(query)

            # Generate response
            response = await model.ainvoke([HumanMessage(content=prompt)])
            return response.content

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating model response: {e}")
            return f"[ERROR] Error generating response: {str(e)}"

    async def _generate_direct_response(self, query_request: QueryRequest, model_type: str) -> QueryResponse:
        """Generate direct response without agent routing"""
        try:
            response_text = await self._generate_model_response(query_request.query, model_type)

            return QueryResponse(
                response=response_text,
                model_used=model_type,
                confidence=0.7
            )

        except Exception as e:
            self.logger.error(f"[ERROR] Error generating direct response: {e}")
            return QueryResponse(
                response=f"[ERROR] Error generating response: {str(e)}",
                error=str(e)
            )

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TARGET] AGENT-SPECIFIC QUERY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _query_performance_agent(self, query: str, agent_instance) -> str:
        """Query performance analysis agent"""
        try:
            # Extract performance-related keywords
            if any(keyword in query.lower() for keyword in ['roi', 'return', 'profit', 'performance']):
                if hasattr(agent_instance, 'get_strategy_performance'):
                    metrics = await agent_instance.get_strategy_performance()
                    return self._format_performance_response(metrics, query)

            elif any(keyword in query.lower() for keyword in ['sharpe', 'ratio', 'risk']):
                if hasattr(agent_instance, 'get_performance_metrics'):
                    metrics = await agent_instance.get_performance_metrics()
                    return self._format_metrics_response(metrics, query)

            elif any(keyword in query.lower() for keyword in ['report', 'summary']):
                if hasattr(agent_instance, 'generate_performance_report'):
                    report = await agent_instance.generate_performance_report()
                    return f"[STATUS] **Performance Report**\n\n{report}"

            return "[STATUS] Performance data retrieved. Please specify what metrics you'd like to see (ROI, Sharpe ratio, drawdown, etc.)"

        except Exception as e:
            return f"[ERROR] Error querying performance agent: {str(e)}"

    async def _query_market_agent(self, query: str, agent_instance) -> str:
        """Query market monitoring agent"""
        try:
            if any(keyword in query.lower() for keyword in ['regime', 'trend', 'market']):
                if hasattr(agent_instance, 'get_market_regime'):
                    regime = agent_instance.get_market_regime()
                    return f"[METRICS] **Current Market Regime**: {regime.regime if regime else 'Unknown'}\n" \
                           f"**Confidence**: {regime.confidence if regime else 'N/A'}\n" \
                           f"**Trend**: {regime.trend if regime else 'N/A'}"

            elif any(keyword in query.lower() for keyword in ['signal', 'active', 'trading']):
                if hasattr(agent_instance, 'get_active_signals'):
                    signals = agent_instance.get_active_signals()
                    return f"[TARGET] **Active Signals**: {len(signals)} signals found\n" + \
                           "\n".join([f"• {signal.symbol}: {signal.strategy} ({signal.direction})"
                                    for signal in signals[:5]])

            elif any(keyword in query.lower() for keyword in ['indicator', 'technical']):
                return "[STATUS] Technical indicators are being monitored. Specify a symbol for detailed indicators."

            return "[METRICS] Market monitoring active. Ask about regime, signals, or indicators."

        except Exception as e:
            return f"[ERROR] Error querying market agent: {str(e)}"

    async def _query_signal_agent(self, query: str, agent_instance) -> str:
        """Query signal generation agent"""
        try:
            if any(keyword in query.lower() for keyword in ['generate', 'signal', 'strategy']):
                if hasattr(agent_instance, 'get_active_strategies'):
                    strategies = agent_instance.get_active_strategies()
                    return f"[TARGET] **Active Strategies**: {len(strategies)} strategies running\n" + \
                           "\n".join([f"• {name}: {info.get('description', 'No description')}"
                                    for name, info in strategies.items()][:5])

            elif any(keyword in query.lower() for keyword in ['performance', 'success']):
                return "[STATUS] Signal performance tracking available. Specify time period or strategy name."

            return "[TARGET] Signal generation active. Ask about strategies, performance, or generate new signals."

        except Exception as e:
            return f"[ERROR] Error querying signal agent: {str(e)}"

    async def _query_risk_agent(self, query: str, agent_instance) -> str:
        """Query risk management agent"""
        try:
            if any(keyword in query.lower() for keyword in ['portfolio', 'allocation', 'capital']):
                if hasattr(agent_instance, 'get_portfolio_metrics'):
                    metrics = await agent_instance.get_portfolio_metrics()
                    return f"[MONEY] **Portfolio Metrics**\n" \
                           f"• Total Capital: Rs.{metrics.get('total_capital', 'N/A'):,}\n" \
                           f"• Available: Rs.{metrics.get('available_capital', 'N/A'):,}\n" \
                           f"• At Risk: Rs.{metrics.get('capital_at_risk', 'N/A'):,}\n" \
                           f"• Active Positions: {metrics.get('active_positions', 'N/A')}"

            elif any(keyword in query.lower() for keyword in ['risk', 'limit', 'drawdown']):
                if hasattr(agent_instance, 'check_risk_limits'):
                    limits = await agent_instance.check_risk_limits()
                    return f"[WARN] **Risk Limits Status**\n" + \
                           "\n".join([f"• {limit}: {'[SUCCESS] OK' if status else '[ERROR] EXCEEDED'}"
                                    for limit, status in limits.items()])

            return "[WARN] Risk management active. Ask about portfolio, limits, or capital allocation."

        except Exception as e:
            return f"[ERROR] Error querying risk agent: {str(e)}"

    async def _query_execution_agent(self, query: str, agent_instance) -> str:
        """Query execution agent"""
        try:
            if any(keyword in query.lower() for keyword in ['trades', 'orders', 'execution']):
                if hasattr(agent_instance, 'get_execution_summary'):
                    summary = await agent_instance.get_execution_summary()
                    return f"⚙️ **Execution Summary**\n" \
                           f"• Total Orders: {summary.get('total_orders', 'N/A')}\n" \
                           f"• Successful: {summary.get('successful_orders', 'N/A')}\n" \
                           f"• Failed: {summary.get('failed_orders', 'N/A')}\n" \
                           f"• Avg Fill Time: {summary.get('avg_fill_time', 'N/A')}ms"

            elif any(keyword in query.lower() for keyword in ['status', 'active', 'pending']):
                return "⚙️ Execution agent status: Active and monitoring for signals."

            return "⚙️ Execution agent ready. Ask about trades, orders, or execution status."

        except Exception as e:
            return f"[ERROR] Error querying execution agent: {str(e)}"

    # ═══════════════════════════════════════════════════════════════════════════════
    # 📝 PROMPT CREATION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _create_code_prompt(self, query: str) -> str:
        """Create prompt for code generation tasks"""
        return f"""You are an expert Python developer specializing in trading systems and financial analysis.

User Query: {query}

Please provide:
1. Clean, well-commented Python code
2. Brief explanation of the approach
3. Any important considerations or warnings
4. Suggest testing approaches if applicable

Focus on:
- Using polars, pyarrow, and cuDF for data processing
- Following existing code patterns in the trading system
- Implementing proper error handling
- Optimizing for performance

Code:"""

    def _create_explanation_prompt(self, query: str) -> str:
        """Create prompt for code explanation tasks"""
        return f"""You are an expert at explaining complex trading system code and strategies.

User Query: {query}

Please provide:
1. Clear, step-by-step explanation
2. Why this approach was chosen
3. Potential issues or improvements
4. How it fits into the larger trading system

Focus on:
- Making complex concepts accessible
- Highlighting key decision points
- Explaining performance implications
- Suggesting optimizations

Explanation:"""

    def _create_general_prompt(self, query: str) -> str:
        """Create prompt for general queries"""
        return f"""You are an AI assistant for a comprehensive intraday trading system.

User Query: {query}

Please provide:
1. Direct answer to the question
2. Relevant context from the trading system
3. Actionable suggestions if applicable
4. Next steps or follow-up questions

Focus on:
- Being helpful and accurate
- Providing trading-specific insights
- Suggesting relevant system features
- Maintaining professional tone

Response:"""

    # ═══════════════════════════════════════════════════════════════════════════════
    # [STATUS] RESPONSE FORMATTING METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def _format_performance_response(self, metrics: Dict[str, Any], query: str) -> str:
        """Format performance metrics response"""
        try:
            if not metrics:
                return "[STATUS] No performance data available."

            response = "[STATUS] **Performance Analysis**\n\n"

            # Extract relevant metrics based on query
            if 'roi' in query.lower() or 'return' in query.lower():
                roi_data = metrics.get('roi_metrics', {})
                response += f"[MONEY] **ROI Metrics**\n"
                response += f"• Total ROI: {roi_data.get('total_roi', 'N/A'):.2f}%\n"
                response += f"• Average ROI: {roi_data.get('avg_roi', 'N/A'):.2f}%\n"
                response += f"• Best Strategy: {roi_data.get('best_strategy', 'N/A')}\n\n"

            if 'accuracy' in query.lower():
                acc_data = metrics.get('accuracy_metrics', {})
                response += f"[TARGET] **Accuracy Metrics**\n"
                response += f"• Overall Accuracy: {acc_data.get('overall_accuracy', 'N/A'):.1f}%\n"
                response += f"• Win Rate: {acc_data.get('win_rate', 'N/A'):.1f}%\n\n"

            if 'drawdown' in query.lower():
                dd_data = metrics.get('drawdown_metrics', {})
                response += f"📉 **Drawdown Analysis**\n"
                response += f"• Max Drawdown: {dd_data.get('max_drawdown', 'N/A'):.2f}%\n"
                response += f"• Current Drawdown: {dd_data.get('current_drawdown', 'N/A'):.2f}%\n\n"

            return response.strip()

        except Exception as e:
            return f"[ERROR] Error formatting performance response: {str(e)}"

    def _format_metrics_response(self, metrics: Dict[str, Any], query: str) -> str:
        """Format general metrics response"""
        try:
            if not metrics:
                return "[STATUS] No metrics data available."

            response = "[STATUS] **System Metrics**\n\n"

            # Format based on available metrics
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    response += f"• {key.replace('_', ' ').title()}: {value:.2f}\n"
                else:
                    response += f"• {key.replace('_', ' ').title()}: {value}\n"

            return response

        except Exception as e:
            return f"[ERROR] Error formatting metrics response: {str(e)}"

    # ═══════════════════════════════════════════════════════════════════════════════
    # [CONNECT] ANGEL ONE API INTEGRATION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def query_angel_one_api(self, query: str) -> str:
        """Query Angel One API for real-time trading data"""
        try:
            angel_config = self.config.get('angel_one', {})
            if not angel_config.get('enabled', False):
                return "[ERROR] Angel One API integration not enabled."

            # Classify Angel One query type
            query_lower = query.lower()

            if any(keyword in query_lower for keyword in ['portfolio', 'holdings', 'positions']):
                return await self._get_portfolio_status()
            elif any(keyword in query_lower for keyword in ['margin', 'funds', 'balance']):
                return await self._get_margin_status()
            elif any(keyword in query_lower for keyword in ['trades', 'orders', 'executed']):
                return await self._get_trade_status()
            elif any(keyword in query_lower for keyword in ['price', 'ltp', 'quote']):
                return await self._get_market_data(query)
            else:
                return "[CONNECT] Angel One API available. Ask about portfolio, margin, trades, or market data."

        except Exception as e:
            self.logger.error(f"[ERROR] Error querying Angel One API: {e}")
            return f"[ERROR] Error accessing Angel One API: {str(e)}"

    async def _get_portfolio_status(self) -> str:
        """Get portfolio status from Angel One"""
        try:
            # This would integrate with actual Angel One API
            # For now, return placeholder response
            return """💼 **Portfolio Status** (Demo Data)

• Total Holdings Value: Rs.2,45,000
• Day's P&L: +Rs.3,250 (+1.32%)
• Unrealized P&L: +Rs.12,500 (+5.38%)
• Active Positions: 3
• Available Margin: Rs.1,85,000

**Top Holdings:**
• RELIANCE: +2.5% (Rs.45,000)
• TCS: +1.8% (Rs.32,000)
• HDFC: -0.5% (Rs.28,000)"""

        except Exception as e:
            return f"[ERROR] Error getting portfolio status: {str(e)}"

    async def _get_margin_status(self) -> str:
        """Get margin status from Angel One"""
        try:
            return """[MONEY] **Margin Status** (Demo Data)

• Available Cash: Rs.1,85,000
• Used Margin: Rs.65,000
• Total Margin: Rs.2,50,000
• Margin Utilization: 26%
• Intraday Leverage: 3.5x

**Margin Breakdown:**
• Equity: Rs.1,20,000
• F&O: Rs.45,000
• Currency: Rs.0
• Commodity: Rs.0"""

        except Exception as e:
            return f"[ERROR] Error getting margin status: {str(e)}"

    async def _get_trade_status(self) -> str:
        """Get trade status from Angel One"""
        try:
            return """⚙️ **Trade Status** (Demo Data)

**Today's Trades:**
• Total Orders: 8
• Executed: 6
• Pending: 1
• Cancelled: 1

**Recent Executions:**
• ADANIPORTS BUY 100 @ Rs.1,245 (11:30 AM)
• TATASTEEL SELL 50 @ Rs.890 (10:45 AM)
• RELIANCE BUY 25 @ Rs.2,650 (09:30 AM)

**P&L Summary:**
• Realized P&L: +Rs.2,150
• Unrealized P&L: +Rs.1,100"""

        except Exception as e:
            return f"[ERROR] Error getting trade status: {str(e)}"

    async def _get_market_data(self, query: str) -> str:
        """Get market data from Angel One"""
        try:
            # Extract symbol from query if possible
            symbols = ['RELIANCE', 'TCS', 'HDFC', 'INFY', 'ADANIPORTS']
            found_symbol = None

            for symbol in symbols:
                if symbol.lower() in query.lower():
                    found_symbol = symbol
                    break

            if found_symbol:
                return f"""[STATUS] **Market Data for {found_symbol}** (Demo Data)

• LTP: Rs.2,650.50
• Change: +Rs.25.75 (+0.98%)
• Volume: 1,25,000
• High: Rs.2,665.00
• Low: Rs.2,635.25
• Open: Rs.2,640.00

**Technical Indicators:**
• RSI: 65.2
• MACD: Bullish
• EMA 20: Rs.2,635"""
            else:
                return "[STATUS] Market data available. Please specify a symbol (e.g., RELIANCE, TCS, HDFC)."

        except Exception as e:
            return f"[ERROR] Error getting market data: {str(e)}"

    # ═══════════════════════════════════════════════════════════════════════════════
    # [TOOLS] UTILITY METHODS
    # ═══════════════════════════════════════════════════════════════════════════════

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics"""
        return {
            **self.performance_metrics,
            'uptime_seconds': (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0,
            'models_available': list(self.models.keys()),
            'agents_connected': list(self.agents.keys()),
            'query_history_size': len(self.query_history),
            'is_running': self.is_running
        }

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        return {
            'agent_status': 'running' if self.is_running else 'stopped',
            'startup_time': self.startup_time.isoformat() if self.startup_time else None,
            'models': {
                name: {
                    'available': name in self.models,
                    'usage_count': self.performance_metrics['model_usage'].get(name, 0)
                }
                for name in self.config.get('models', {}).keys()
            },
            'agents': {
                name: {
                    'connected': name in self.agents,
                    'usage_count': self.performance_metrics['agent_usage'].get(name, 0)
                }
                for name in self.config.get('agents', {}).keys()
            },
            'performance': self.get_performance_metrics()
        }

    async def shutdown(self):
        """Shutdown the LLM Interface Agent"""
        try:
            self.logger.info("[STOP] Shutting down LLM Interface Agent...")

            self.is_running = False

            # Close model connections
            for model_name, model in self.models.items():
                try:
                    # Models don't typically need explicit cleanup
                    self.logger.info(f"[SUCCESS] Closed model: {model_name}")
                except Exception as e:
                    self.logger.warning(f"[WARN] Error closing model {model_name}: {e}")

            # Close agent connections
            for agent_name, agent_info in self.agents.items():
                try:
                    agent_instance = agent_info['instance']
                    if hasattr(agent_instance, 'shutdown'):
                        await agent_instance.shutdown()
                    self.logger.info(f"[SUCCESS] Closed agent: {agent_name}")
                except Exception as e:
                    self.logger.warning(f"[WARN] Error closing agent {agent_name}: {e}")

            self.logger.info("[SUCCESS] LLM Interface Agent shutdown complete")

        except Exception as e:
            self.logger.error(f"[ERROR] Error during shutdown: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION AND DEMO FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function for testing"""
    agent = LLMInterfaceAgent()

    try:
        # Initialize agent
        if not await agent.initialize():
            print("[ERROR] Failed to initialize LLM Interface Agent")
            return

        print("[SUCCESS] LLM Interface Agent initialized successfully!")
        print("🗣️ You can now ask questions in natural language.")
        print("Examples:")
        print("  • 'What's the ROI of Donchian strategy last week?'")
        print("  • 'Fix error in strategy YAML - missing MACD_signal'")
        print("  • 'Show me my portfolio status'")
        print("  • 'Generate a new RSI scalping strategy'")
        print("  • Type 'quit' to exit")
        print()

        # Interactive loop
        while True:
            try:
                user_input = input("[AGENT] Ask me anything: ").strip()

                if user_input.lower() in ['quit', 'exit', 'bye']:
                    break

                if not user_input:
                    continue

                # Process query
                query_request = QueryRequest(query=user_input)
                response = await agent.process_query(query_request)

                # Display response
                print(f"\n📝 **Response** ({response.processing_time_ms:.1f}ms):")
                print(response.response)

                if response.model_used:
                    print(f"🧠 Model: {response.model_used}")
                if response.agent_used:
                    print(f"[AGENT] Agent: {response.agent_used}")
                if response.confidence > 0:
                    print(f"[TARGET] Confidence: {response.confidence:.1%}")

                print("-" * 60)

            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"[ERROR] Error: {e}")

    finally:
        await agent.shutdown()
        print("[EXIT] Goodbye!")

if __name__ == "__main__":
    asyncio.run(main())
