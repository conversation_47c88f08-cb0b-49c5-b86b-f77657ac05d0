#!/usr/bin/env python3
"""
Clear Pending Trades Script

This script clears all pending trades from the daily trade tracker.
Use this when the system has accumulated stale pending trades that are preventing new trades.

Usage:
    python scripts/clear_pending_trades.py [--force]
    
Options:
    --force    Force clear without confirmation prompt
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from py_eq
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.daily_trade_tracker import DailyTradeTracker


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(description='Clear pending trades from daily trade tracker')
    parser.add_argument('--force', action='store_true', 
                       help='Force clear without confirmation prompt')
    parser.add_argument('--max-age', type=int, default=0,
                       help='Only clear trades older than this many minutes (0 = all)')
    
    args = parser.parse_args()
    
    logger = setup_logging()
    
    # Initialize trade tracker
    logger.info("🔧 Initializing daily trade tracker...")
    trade_tracker = DailyTradeTracker(
        logger=logger,
        data_dir="data",
        max_trades_per_day=3
    )
    
    # Get current status
    summary = trade_tracker.get_daily_summary()
    pending_trades = trade_tracker.get_pending_trades()
    
    logger.info("=" * 60)
    logger.info("📊 CURRENT TRADE STATUS")
    logger.info("=" * 60)
    logger.info(f"Total trades today: {summary['total_trades']}")
    logger.info(f"Executed trades: {summary['executed_trades']}")
    logger.info(f"Pending trades: {summary['pending_trades']}")
    logger.info(f"Rejected trades: {summary['rejected_trades']}")
    
    if summary['pending_trades'] == 0:
        logger.info("✅ No pending trades found. Nothing to clear.")
        return
    
    logger.info("\n📋 PENDING TRADES:")
    for trade in pending_trades:
        logger.info(f"  - {trade.symbol} ({trade.trade_id}) - {trade.timestamp}")
    
    # Confirmation prompt unless --force is used
    if not args.force:
        print("\n" + "=" * 60)
        if args.max_age > 0:
            response = input(f"Clear pending trades older than {args.max_age} minutes? (y/N): ")
        else:
            response = input(f"Clear ALL {summary['pending_trades']} pending trades? (y/N): ")
        
        if response.lower() not in ['y', 'yes']:
            logger.info("❌ Operation cancelled by user")
            return
    
    # Clear trades
    logger.info("\n🧹 Clearing pending trades...")
    
    if args.max_age > 0:
        cleared_count = trade_tracker.cleanup_stale_pending_trades(max_age_minutes=args.max_age)
        logger.info(f"✅ Cleared {cleared_count} stale pending trades (older than {args.max_age} minutes)")
    else:
        cleared_count = trade_tracker.force_clear_all_pending_trades(reason="Manual script execution")
        logger.info(f"✅ Cleared {cleared_count} pending trades")
    
    # Show final status
    final_summary = trade_tracker.get_daily_summary()
    logger.info("\n📊 FINAL STATUS:")
    logger.info(f"Pending trades remaining: {final_summary['pending_trades']}")
    logger.info(f"Can trade more: {final_summary['can_trade_more']}")
    
    if final_summary['pending_trades'] == 0:
        logger.info("🎉 All pending trades cleared successfully!")
        logger.info("🚀 The trading system should now be able to place new trades.")
    else:
        logger.warning(f"⚠️ {final_summary['pending_trades']} pending trades still remain")


if __name__ == "__main__":
    main()
