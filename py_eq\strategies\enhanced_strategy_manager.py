"""
Real Trading Strategy Manager with AngelOne Balance Integration

This manager uses real account balance from AngelOne broker for all trading decisions.
No virtual accounts or paper trading components.

Features:
- Real balance fetching from AngelOne API
- Dynamic risk management based on actual available funds
- Global trade limit: 3 trades per day across all strategies
- Real order execution through SmartAPI
- 1% daily risk allocation from available balance
"""
import asyncio
import logging
from datetime import time
from enum import Enum
from typing import Dict, List, Optional, Set
from threading import Lock

from models.signal import Signal
from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from services.market_data_service import MarketDataServiceInterface
from services.real_balance_service import RealBalanceService
from config.config import TradingConfig
from strategies.ma_crossover_strategy import MACrossoverStrategy
from strategies.support_resistance_strategy import SupportResistanceStrategy
from strategies.gap_and_go_strategy import GapAndGoStrategy
from strategies.orb_strategy import ORBStrategy


class StrategyType(Enum):
    """Strategy type enumeration"""
    MA_CROSSOVER = "MA_CROSSOVER"
    SUPPORT_RESISTANCE = "SUPPORT_RESISTANCE"
    GAP_AND_GO = "GAP_AND_GO"
    ORB = "ORB"


class RealTradingStrategyManager:
    """
    Real Trading Strategy Manager with AngelOne Balance Integration

    Features:
    - Real balance fetching from AngelOne API
    - Global trade limit: 3 trades per day across all strategies
    - Dynamic risk management: 1% of available balance per trade
    - Real order execution through SmartAPI
    - No virtual accounts or paper trading
    """

    def __init__(
        self,
        logger: logging.Logger,
        market_data_service: MarketDataServiceInterface,
        order_service,
        config: TradingConfig,
        trading_logger=None
    ):
        self.logger = logger
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config
        self.trading_logger = trading_logger

        # Initialize Real Balance Service
        self.balance_service = RealBalanceService(order_service, logger)

        # Parse square_off_time from config
        try:
            self.square_off_time = time.fromisoformat(config.square_off_time)
        except (ValueError, AttributeError):
            self.square_off_time = time(15, 12)  # Default to 15:12 (before Angel One's 15:15 auto square-off)
            self.logger.warning(f"Invalid square_off_time in config: {getattr(config, 'square_off_time', 'None')}. Using default 15:12")

        # Initialize strategies with real order service
        self.ma_crossover_strategy = MACrossoverStrategy(
            logger, market_data_service, order_service, config
        )

        self.support_resistance_strategy = SupportResistanceStrategy(
            logger, market_data_service, order_service, config
        )

        self.gap_and_go_strategy = GapAndGoStrategy(
            logger, market_data_service, order_service, config
        )

        self.orb_strategy = ORBStrategy(
            logger, market_data_service, order_service, config
        )

        # Track which stocks have been traded today to avoid conflicts
        self.traded_stocks_today: Set[str] = set()
        self.traded_stocks_lock = Lock()

        # Track orders placed today
        self.orders_placed_today: List[Order] = []
        self.orders_lock = Lock()

        self.logger.info(f"🏦 Real Trading Strategy Manager initialized")
        self.logger.info(f"💰 Using real AngelOne account balance for all trading decisions")
        self.logger.info(f"🎯 Global trade limit: 3 trades per day across all strategies")
        self.logger.info(f"📊 Risk management: 1% of available balance per trade")

    async def initialize_balance(self) -> bool:
        """
        Initialize real balance from AngelOne

        Returns:
            bool: True if balance initialized successfully
        """
        success = await self.balance_service.fetch_real_balance()
        if success:
            self.balance_service.log_balance_summary()
        return success

    def can_place_trade(self) -> bool:
        """
        Check if a new trade can be placed based on real balance and global limits

        Returns:
            bool: True if trade can be placed
        """
        return self.balance_service.can_place_trade()

    async def process_signal(self, signal: Signal, strategy_type: StrategyType) -> Optional[Order]:
        """
        Process a signal using the specified strategy with real balance validation
        """
        # Check if we can place a trade
        if not self.can_place_trade():
            self.logger.warning(f"❌ Cannot place trade for {signal.symbol} - daily limit reached or insufficient balance")
            return None

        # Check if this stock has already been traded today
        with self.traded_stocks_lock:
            if signal.symbol in self.traded_stocks_today:
                self.logger.info(f"Skipping {signal.symbol} - already traded today")
                return None

        # Process signal with the specified strategy
        order = None
        try:
            if strategy_type == StrategyType.MA_CROSSOVER:
                # Get entry conditions from MA Crossover strategy
                entry_data = self.ma_crossover_strategy.analyze_signal(signal)
                if entry_data:
                    order = await self._place_real_order(signal, entry_data, "MA_CROSSOVER")

            elif strategy_type == StrategyType.SUPPORT_RESISTANCE:
                # Get entry conditions from Support/Resistance strategy
                entry_data = self.support_resistance_strategy.analyze_signal(signal)
                if entry_data:
                    order = await self._place_real_order(signal, entry_data, "SUPPORT_RESISTANCE")

            elif strategy_type == StrategyType.GAP_AND_GO:
                # Get entry conditions from Gap and Go strategy
                entry_data = self.gap_and_go_strategy.analyze_signal(signal)
                if entry_data:
                    order = await self._place_real_order(signal, entry_data, "GAP_AND_GO")

            elif strategy_type == StrategyType.ORB:
                # Get entry conditions from ORB strategy
                entry_data = self.orb_strategy.analyze_signal(signal)
                if entry_data:
                    order = await self._place_real_order(signal, entry_data, "ORB")
            else:
                self.logger.error(f"Unknown strategy type: {strategy_type}")
                return None

        except Exception as e:
            self.logger.error(f"Error processing signal for {signal.symbol} with {strategy_type.value}: {e}")
            return None

        # If order was placed successfully, mark the stock as traded today
        if order:
            with self.traded_stocks_lock:
                self.traded_stocks_today.add(signal.symbol)

            with self.orders_lock:
                self.orders_placed_today.append(order)

            # Log to trading logger
            if self.trading_logger:
                self.trading_logger.log_order_placed(
                    order=order,
                    strategy=strategy_type.value,
                    notes=f"Real order placed via {strategy_type.value} strategy"
                )

            self.logger.info(f"✅ Real order placed for {signal.symbol} via {strategy_type.value} strategy")

        return order

    async def _place_real_order(self, signal: Signal, entry_data: Dict, strategy_name: str) -> Optional[Order]:
        """Place a real order using AngelOne API with balance validation"""
        try:
            # Extract entry data
            entry_price = entry_data.get('entry_price')
            stop_loss = entry_data.get('stop_loss')
            target = entry_data.get('target')
            direction = entry_data.get('direction', 'LONG')

            if not all([entry_price, stop_loss, target]):
                self.logger.error(f"Missing entry data for {signal.symbol}")
                return None

            # Calculate position size based on real balance and risk management
            quantity = self.balance_service.calculate_position_size(entry_price, stop_loss)
            if quantity <= 0:
                self.logger.warning(f"❌ Invalid quantity calculated for {signal.symbol}")
                return None

            # Calculate order value and validate
            order_value = entry_price * quantity
            estimated_costs = order_value * 0.002  # ~0.2% total transaction costs

            if not self.balance_service.validate_order_value(order_value, estimated_costs):
                self.logger.warning(f"❌ Order value validation failed for {signal.symbol}")
                return None

            # Place real order through SmartAPI
            order_response = await self._execute_real_order(
                symbol=signal.symbol,
                transaction_type=TransactionType.BUY if direction == "LONG" else TransactionType.SELL,
                quantity=quantity,
                price=entry_price,
                stop_loss=stop_loss,
                target=target
            )

            if order_response and order_response.get('status'):
                # Record the trade in balance service
                self.balance_service.record_trade_placed(order_value)

                # Create order object for tracking
                order = Order(
                    order_id=order_response.get('data', {}).get('orderid', ''),
                    symbol=signal.symbol,
                    symbol_token=signal.symbol_token if hasattr(signal, 'symbol_token') else "",
                    exchange="NSE",
                    transaction_type=TransactionType.BUY if direction == "LONG" else TransactionType.SELL,
                    order_type=OrderType.MARKET,
                    product_type=ProductType.INTRADAY,
                    quantity=quantity,
                    price=entry_price,
                    stop_loss=stop_loss,
                    target=target,
                    status=OrderStatus.COMPLETED,
                    entry_price=entry_price,
                    strategy=strategy_name
                )

                self.logger.info(f"🎯 Real order placed: {signal.symbol} {direction} @ ₹{entry_price:.2f} "
                               f"Qty: {quantity} SL: ₹{stop_loss:.2f} Target: ₹{target:.2f} "
                               f"Strategy: {strategy_name} OrderID: {order.order_id}")

                return order
            else:
                error_msg = order_response.get('message', 'Unknown error') if order_response else 'No response'
                self.logger.error(f"❌ Failed to place real order for {signal.symbol}: {error_msg}")

        except Exception as e:
            self.logger.error(f"Error placing real order for {signal.symbol}: {e}")

        return None

    async def _execute_real_order(self, symbol: str, transaction_type: TransactionType,
                                 quantity: int, price: float, stop_loss: float, target: float) -> Optional[Dict]:
        """Execute real order through SmartAPI"""
        try:
            # Prepare order parameters
            order_params = {
                'variety': 'NORMAL',
                'tradingsymbol': f"{symbol}-EQ",
                'symboltoken': '',  # Will be resolved by order service
                'transactiontype': 'BUY' if transaction_type == TransactionType.BUY else 'SELL',
                'exchange': 'NSE',
                'ordertype': 'MARKET',
                'producttype': 'INTRADAY',
                'duration': 'DAY',
                'price': '0',  # Market order
                'squareoff': '0',
                'stoploss': '0',
                'quantity': str(quantity)
            }

            # Place order through order service
            response = await asyncio.to_thread(self.order_service.place_order, order_params)
            return response

        except Exception as e:
            self.logger.error(f"Error executing real order for {symbol}: {e}")
            return None

    async def process_signals_batch(self, signals: List[Signal], strategy_type: Optional[StrategyType] = None) -> List[Order]:
        """Process a batch of signals with real balance validation"""
        orders = []

        for signal in signals:
            try:
                if strategy_type:
                    # Use specified strategy type
                    order = await self.process_signal(signal, strategy_type)
                else:
                    # Use signal's preferred strategy to determine which strategy to try
                    preferred_strategy = getattr(signal, 'preferred_strategy', 'ALL')

                    if preferred_strategy == 'MA_CROSSOVER':
                        order = await self.process_signal(signal, StrategyType.MA_CROSSOVER)
                    elif preferred_strategy == 'SUPPORT_RESISTANCE':
                        order = await self.process_signal(signal, StrategyType.SUPPORT_RESISTANCE)
                    elif preferred_strategy == 'GAPANDGO':
                        order = await self.process_signal(signal, StrategyType.GAP_AND_GO)
                    elif preferred_strategy == 'ORB':
                        order = await self.process_signal(signal, StrategyType.ORB)
                    else:
                        # For 'ALL' or unknown strategies, try MA_CROSSOVER first, then SUPPORT_RESISTANCE
                        order = await self.process_signal(signal, StrategyType.MA_CROSSOVER)
                        if not order:
                            order = await self.process_signal(signal, StrategyType.SUPPORT_RESISTANCE)

                if order:
                    orders.append(order)

                # Check if global trade limit is reached
                if not self.can_place_trade():
                    self.logger.info("Global daily trade limit reached (3 trades per day)")
                    break

            except Exception as e:
                self.logger.error(f"Error processing signal {signal.symbol}: {e}")
                continue

        self.logger.info(f"Batch processing completed. Placed {len(orders)} orders out of {len(signals)} signals.")
        return orders

    async def process_signals_batch_async(self, signals: List[Signal], balance_service: RealBalanceService = None) -> List[Order]:
        """
        Process a batch of signals asynchronously with real-time balance checking

        Args:
            signals: List of signals to process
            balance_service: Optional balance service to use for order validation

        Returns:
            List of successfully placed orders
        """
        if not signals:
            self.logger.info("No signals to process")
            return []

        # Use the provided balance service or fall back to our own
        current_balance_service = balance_service if balance_service else self.balance_service
        
        # CRITICAL SAFETY: Check if we've reached the daily trade limit
        if not current_balance_service.can_place_trade():
            self.logger.warning("🚫 Cannot process signals - daily trade limit reached or insufficient balance")
            return []

        new_orders = []
        trades_executed_this_batch = 0
        max_trades_per_batch = 1  # Only allow one trade per batch to prevent rapid-fire trading
        
        # Get strategy summary for monitoring
        strategy_summary = self.get_strategy_summary()
        self.logger.info(f"📊 Batch processing: {len(signals)} signals, {strategy_summary['total_trades_today']}/{strategy_summary['max_trades_per_day']} trades used")

        # Process each signal in the batch
        for signal in signals:
            # CRITICAL SAFETY: Double-check daily trade limit before each trade
            if not current_balance_service.can_place_trade():
                self.logger.warning("🛑 Stopping batch processing - daily trade limit reached")
                break

            # Skip if symbol already has an open position or has been traded today
            if signal.symbol in self.active_positions:
                self.logger.debug(f"⏭️ Skipping {signal.symbol} - already has active position")
                continue

            if signal.symbol in current_balance_service.traded_stocks_today:
                self.logger.debug(f"⏭️ Skipping {signal.symbol} - already traded today")
                continue

            # Determine which strategy to use based on signal metadata
            strategy_type = await self._determine_strategy(signal)
            if not strategy_type:
                continue

            # Process the signal with the determined strategy
            try:
                order = await self.process_signal(signal, strategy_type)
                
                # If order was placed successfully
                if order:
                    new_orders.append(order)
                    trades_executed_this_batch += 1
                    
                    # Update strategy summary after successful trade
                    strategy_summary = self.get_strategy_summary()
                    
                    # CRITICAL SAFETY: Break if we've reached the daily limit
                    if not current_balance_service.can_place_trade():
                        self.logger.warning("🛑 Stopping batch processing - daily trade limit reached after placing order")
                        break

                    # Respect maximum trades per batch
                    if trades_executed_this_batch >= max_trades_per_batch:
                        self.logger.info(f"⏱️ Maximum trades per batch ({max_trades_per_batch}) reached")
                        break

            except Exception as e:
                self.logger.error(f"Error processing signal for {signal.symbol}: {e}")
                continue

        return new_orders

    def get_trading_statistics(self) -> Dict[str, any]:
        """Get comprehensive trading statistics based on real balance"""
        balance_summary = self.balance_service.get_trading_summary()

        with self.orders_lock:
            total_orders = len(self.orders_placed_today)

        with self.traded_stocks_lock:
            total_stocks_traded = len(self.traded_stocks_today)

        stats = {
            'balance_info': balance_summary,
            'trading_stats': {
                'total_orders_today': total_orders,
                'total_stocks_traded': total_stocks_traded,
                'trades_remaining': balance_summary['trades_remaining'],
                'can_place_trade': balance_summary['can_place_trade']
            },
            'risk_management': {
                'daily_risk_amount': balance_summary['daily_risk_amount'],
                'max_order_value': balance_summary['max_order_value'],
                'risk_percentage': '1%'
            }
        }

        return stats

    def log_trading_summary(self):
        """Log summary of real trading status"""
        self.logger.info("🏦 REAL TRADING SUMMARY")
        self.logger.info("=" * 50)

        # Log balance information
        self.balance_service.log_balance_summary()

        # Log trading activity
        with self.orders_lock:
            total_orders = len(self.orders_placed_today)

        with self.traded_stocks_lock:
            total_stocks_traded = len(self.traded_stocks_today)

        self.logger.info("📊 TRADING ACTIVITY")
        self.logger.info("=" * 50)
        self.logger.info(f"📈 Orders Placed Today: {total_orders}")
        self.logger.info(f"📋 Stocks Traded Today: {total_stocks_traded}")
        self.logger.info(f"🎯 Trades Remaining: {self.balance_service.max_trades_per_day - self.balance_service.trades_placed_today}")
        self.logger.info(f"✅ Can Place Trade: {'Yes' if self.can_place_trade() else 'No'}")
        self.logger.info("=" * 50)

    async def refresh_balance(self) -> bool:
        """Refresh real balance from AngelOne"""
        return await self.balance_service.fetch_real_balance()

    def reset_daily_counters(self):
        """Reset daily counters for new trading day"""
        self.balance_service.reset_daily_counters()

        with self.traded_stocks_lock:
            self.traded_stocks_today.clear()

        with self.orders_lock:
            self.orders_placed_today.clear()

        self.logger.info("🔄 Daily counters reset for new trading day")
