"""
Async Strategy Manager for optimized signal processing
This module provides async versions of strategy processing for better performance
"""
import asyncio
import logging
from typing import List, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor
import time

from models.signal import Signal
from models.order import Order
from .enhanced_strategy_manager import EnhancedStrategyManager, StrategyType


class AsyncStrategyManager:
    """
    Async wrapper for EnhancedStrategyManager to provide better performance
    through concurrent processing of signals
    """
    
    def __init__(self, enhanced_manager: EnhancedStrategyManager, logger: logging.Logger):
        self.enhanced_manager = enhanced_manager
        self.logger = logger
        self.executor = ThreadPoolExecutor(max_workers=8)  # Limit concurrent operations
        
    async def process_signals_batch_async(self, signals: List[Signal], 
                                        strategy_type: Optional[StrategyType] = None) -> List[Order]:
        """
        Process signals asynchronously with concurrent execution
        This provides significant performance improvements for large signal batches
        """
        if not signals:
            return []
            
        start_time = time.time()
        orders = []
        
        # Split signals into smaller batches for better memory management
        batch_size = 10  # Process 10 signals concurrently
        signal_batches = [signals[i:i + batch_size] for i in range(0, len(signals), batch_size)]
        
        self.logger.info(f"🔄 Processing {len(signals)} signals in {len(signal_batches)} async batches")
        
        for batch_idx, signal_batch in enumerate(signal_batches):
            batch_start = time.time()
            
            # Create async tasks for each signal in the batch
            tasks = []
            for signal in signal_batch:
                task = self._process_single_signal_async(signal, strategy_type)
                tasks.append(task)
            
            # Execute all tasks in the batch concurrently
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect successful orders
            batch_orders = []
            for result in batch_results:
                if isinstance(result, Order):
                    batch_orders.append(result)
                elif isinstance(result, Exception):
                    self.logger.error(f"Error in async signal processing: {result}")
            
            orders.extend(batch_orders)
            
            batch_time = time.time() - batch_start
            self.logger.debug(f"⚡ Batch {batch_idx + 1}/{len(signal_batches)} completed in {batch_time:.2f}s "
                            f"({len(batch_orders)} orders)")
            
            # Check if all accounts are at their limits
            if self.enhanced_manager._all_accounts_at_limit():
                self.logger.info("All strategy accounts have reached their daily trade limits")
                break
        
        total_time = time.time() - start_time
        self.logger.info(f"⚡ Async batch processing completed: {len(orders)} orders from {len(signals)} signals "
                        f"in {total_time:.2f}s (avg: {total_time/len(signals)*1000:.1f}ms per signal)")
        
        return orders
    
    async def _process_single_signal_async(self, signal: Signal, 
                                         strategy_type: Optional[StrategyType] = None) -> Optional[Order]:
        """
        Process a single signal asynchronously
        """
        try:
            # Run the synchronous signal processing in the thread pool
            loop = asyncio.get_event_loop()
            
            if strategy_type:
                order = await loop.run_in_executor(
                    self.executor,
                    self.enhanced_manager.process_signal,
                    signal,
                    strategy_type
                )
            else:
                # Use signal's preferred strategy
                preferred_strategy = getattr(signal, 'preferred_strategy', 'ALL')
                
                if preferred_strategy in ['MA_CROSSOVER', 'GAPANDGO']:
                    # Try MA Crossover strategy first
                    order = await loop.run_in_executor(
                        self.executor,
                        self.enhanced_manager.process_signal,
                        signal,
                        StrategyType.MA_CROSSOVER
                    )
                    if not order:
                        # Fallback to Support/Resistance
                        order = await loop.run_in_executor(
                            self.executor,
                            self.enhanced_manager.process_signal,
                            signal,
                            StrategyType.SUPPORT_RESISTANCE
                        )
                elif preferred_strategy in ['SUPPORT_RESISTANCE', 'ORB']:
                    # Try Support/Resistance strategy first
                    order = await loop.run_in_executor(
                        self.executor,
                        self.enhanced_manager.process_signal,
                        signal,
                        StrategyType.SUPPORT_RESISTANCE
                    )
                    if not order:
                        # Fallback to MA Crossover
                        order = await loop.run_in_executor(
                            self.executor,
                            self.enhanced_manager.process_signal,
                            signal,
                            StrategyType.MA_CROSSOVER
                        )
                else:
                    # For 'ALL' or unknown strategies, try both
                    order = await loop.run_in_executor(
                        self.executor,
                        self.enhanced_manager.process_signal,
                        signal,
                        StrategyType.MA_CROSSOVER
                    )
                    if not order:
                        order = await loop.run_in_executor(
                            self.executor,
                            self.enhanced_manager.process_signal,
                            signal,
                            StrategyType.SUPPORT_RESISTANCE
                        )
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error processing signal {signal.symbol} async: {e}")
            return None
    
    async def get_performance_stats_async(self) -> Dict[str, Any]:
        """
        Get performance statistics asynchronously
        """
        loop = asyncio.get_event_loop()
        stats = await loop.run_in_executor(
            self.executor,
            self.enhanced_manager.get_strategy_statistics
        )
        return stats
    
    def cleanup(self):
        """
        Clean up resources
        """
        self.executor.shutdown(wait=True)
        self.logger.info("🧹 Async strategy manager cleaned up")


class AsyncMarketDataProcessor:
    """
    Async processor for market data operations to improve MongoDB query performance
    """
    
    def __init__(self, market_data_service, logger: logging.Logger):
        self.market_data_service = market_data_service
        self.logger = logger
        self.executor = ThreadPoolExecutor(max_workers=12)  # Higher concurrency for data operations
    
    async def get_historical_data_batch_async(self, symbols: List[str], 
                                            timeframe: str = "15min", 
                                            days: int = 3) -> Dict[str, List]:
        """
        Fetch historical data for multiple symbols concurrently
        """
        start_time = time.time()
        
        # Create async tasks for each symbol
        tasks = []
        for symbol in symbols:
            task = self._get_historical_data_single_async(symbol, timeframe, days)
            tasks.append((symbol, task))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # Build result dictionary
        data_dict = {}
        for (symbol, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                self.logger.error(f"Error fetching data for {symbol}: {result}")
                data_dict[symbol] = []
            else:
                data_dict[symbol] = result
        
        total_time = time.time() - start_time
        successful_fetches = sum(1 for data in data_dict.values() if data)
        
        self.logger.info(f"⚡ Async data fetch completed: {successful_fetches}/{len(symbols)} symbols "
                        f"in {total_time:.2f}s (avg: {total_time/len(symbols)*1000:.1f}ms per symbol)")
        
        return data_dict
    
    async def _get_historical_data_single_async(self, symbol: str, timeframe: str, days: int) -> List:
        """
        Fetch historical data for a single symbol asynchronously
        """
        loop = asyncio.get_event_loop()
        candles = await loop.run_in_executor(
            self.executor,
            self.market_data_service.get_historical_data,
            symbol,
            timeframe,
            days
        )
        return candles
    
    async def get_current_prices_batch_async(self, symbols: List[str]) -> Dict[str, float]:
        """
        Get current prices for multiple symbols concurrently
        """
        start_time = time.time()
        
        # Create async tasks for each symbol
        tasks = []
        for symbol in symbols:
            task = self._get_current_price_single_async(symbol)
            tasks.append((symbol, task))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # Build result dictionary
        price_dict = {}
        for (symbol, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                self.logger.error(f"Error fetching price for {symbol}: {result}")
                price_dict[symbol] = None
            else:
                price_dict[symbol] = result
        
        total_time = time.time() - start_time
        successful_fetches = sum(1 for price in price_dict.values() if price is not None)
        
        self.logger.debug(f"⚡ Async price fetch: {successful_fetches}/{len(symbols)} symbols "
                         f"in {total_time:.2f}s")
        
        return price_dict
    
    async def _get_current_price_single_async(self, symbol: str) -> Optional[float]:
        """
        Get current price for a single symbol asynchronously
        """
        loop = asyncio.get_event_loop()
        price = await loop.run_in_executor(
            self.executor,
            self.market_data_service.get_last_price,
            symbol
        )
        return price
    
    def cleanup(self):
        """
        Clean up resources
        """
        self.executor.shutdown(wait=True)
        self.logger.info("🧹 Async market data processor cleaned up")
