"""
Safe logging utilities for handling Unicode characters in Windows console
"""
import logging
import sys
from typing import Dict


class SafeLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter that safely handles Unicode characters for console output"""
    
    # Emoji to text mapping for console output
    EMOJI_MAP = {
        '🛑': '[STOP]',
        '⏹️': '[STOP]', 
        '🏁': '[FINISH]',
        '📊': '[CHART]',
        '🚀': '[START]',
        '🔄': '[UPDATE]',
        '🎯': '[TARGET]',
        '📈': '[UP]',
        '🕐': '[TIME]',
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '📡': '[SIGNAL]',
        '📋': '[LIST]',
        '💰': '[MONEY]',
        '🔍': '[SEARCH]',
        '⭐': '[STAR]',
        '🔥': '[HOT]',
        '💡': '[IDEA]',
        '🎉': '[CELEBRATE]',
        '🚨': '[ALERT]',
        '📞': '[CALL]',
        '📧': '[EMAIL]',
        '🌟': '[STAR]',
        '🎪': '[EVENT]',
        '🎭': '[MASK]',
        '🎨': '[ART]',
        '🎵': '[MUSIC]',
        '🎬': '[MOVIE]',
        '🎮': '[GAME]',
        '🎲': '[DICE]',
        '🎪': '[CIRCUS]'
    }
    
    def __init__(self, logger, extra=None):
        super().__init__(logger, extra or {})
    
    def process(self, msg, kwargs):
        """Process the message to handle Unicode safely"""
        # Convert message to string if it isn't already
        if not isinstance(msg, str):
            msg = str(msg)
        
        # For console output on Windows, replace emoji with text
        if sys.platform == 'win32':
            for emoji, text in self.EMOJI_MAP.items():
                msg = msg.replace(emoji, text)
        
        return msg, kwargs


def get_safe_logger(name: str) -> SafeLoggerAdapter:
    """Get a logger that safely handles Unicode characters"""
    base_logger = logging.getLogger(name)
    return SafeLoggerAdapter(base_logger)


def safe_log_message(message: str) -> str:
    """Convert a message with emoji to a safe version for console output"""
    if sys.platform == 'win32':
        emoji_map = SafeLoggerAdapter.EMOJI_MAP
        for emoji, text in emoji_map.items():
            message = message.replace(emoji, text)
    return message


# Convenience functions for common log levels
def safe_info(logger: logging.Logger, message: str, *args, **kwargs):
    """Log an info message with safe Unicode handling"""
    safe_message = safe_log_message(message)
    logger.info(safe_message, *args, **kwargs)


def safe_error(logger: logging.Logger, message: str, *args, **kwargs):
    """Log an error message with safe Unicode handling"""
    safe_message = safe_log_message(message)
    logger.error(safe_message, *args, **kwargs)


def safe_warning(logger: logging.Logger, message: str, *args, **kwargs):
    """Log a warning message with safe Unicode handling"""
    safe_message = safe_log_message(message)
    logger.warning(safe_message, *args, **kwargs)


def safe_debug(logger: logging.Logger, message: str, *args, **kwargs):
    """Log a debug message with safe Unicode handling"""
    safe_message = safe_log_message(message)
    logger.debug(safe_message, *args, **kwargs)
