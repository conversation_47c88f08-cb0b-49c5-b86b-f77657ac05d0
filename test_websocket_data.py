#!/usr/bin/env python3
"""
Quick test to check if WebSocket is receiving data
"""

import asyncio
import logging
from datetime import datetime
from agents.market_monitoring_agent import MarketMonitoringAgent

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_data():
    """Test if WebSocket is receiving data"""
    try:
        print("🔍 Testing WebSocket Data Reception...")
        print(f"⏰ Current time: {datetime.now()}")
        
        # Initialize market monitoring agent
        agent = MarketMonitoringAgent()
        await agent.start()
        await agent.start_background_tasks()
        
        # Test symbols
        test_symbols = ['INFY', 'RELIANCE', 'HDFCBANK']
        print(f"📊 Testing with symbols: {test_symbols}")
        
        # Subscribe to symbols
        success = await agent.subscribe_to_symbols(test_symbols)
        if not success:
            print("❌ Failed to subscribe to symbols")
            return
            
        print("✅ Subscribed to symbols, waiting for data...")
        
        # Wait for data for 30 seconds
        start_time = datetime.now()
        data_received = False
        
        while (datetime.now() - start_time).total_seconds() < 30:
            # Check if we have any market data
            for symbol in test_symbols:
                if symbol in agent.market_data:
                    for timeframe in ['1min', '5min']:
                        if timeframe in agent.market_data[symbol] and len(agent.market_data[symbol][timeframe]) > 0:
                            print(f"✅ Received data for {symbol} ({timeframe}): {len(agent.market_data[symbol][timeframe])} candles")
                            data_received = True
                            
                # Check if we have indicators
                if symbol in agent.indicators:
                    print(f"✅ Calculated indicators for {symbol}")
                    data_received = True
            
            if data_received:
                break
                
            await asyncio.sleep(1)
        
        if not data_received:
            print("❌ No data received in 30 seconds")
            print("🔍 Checking WebSocket status...")
            print(f"   Connected: {agent.is_connected}")
            print(f"   Subscribed symbols: {len(agent.subscribed_symbols)}")
            print(f"   Market data keys: {list(agent.market_data.keys())}")
        else:
            print("✅ WebSocket data reception test passed!")
            
        # Cleanup
        await agent.stop()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_websocket_data())
