"""
Global Telegram Notifier

This module provides a global Telegram bot instance that can be used
throughout the trading system to send notifications.
"""

import logging
import threading
from typing import Optional
from datetime import datetime

from .telegram_bot_service import TradingTelegramBot
from ..config.telegram_config import telegram_config
from ..utils.safe_logging import SafeLogger


class TelegramNotifier:
    """
    Global Telegram notifier singleton for sending trading notifications
    """
    
    _instance: Optional['TelegramNotifier'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.logger = SafeLogger.get_logger(__name__)
        self.bot: Optional[TradingTelegramBot] = None
        self.is_enabled = False
        
        # Try to initialize the bot
        self._initialize_bot()
    
    def _initialize_bot(self):
        """Initialize the Telegram bot if configuration is available"""
        try:
            # Check if configuration is valid
            if not telegram_config.bot_token or not telegram_config.chat_id:
                self.logger.info("⚠️ Telegram bot not configured - notifications disabled")
                return
            
            # Validate configuration
            telegram_config.validate()
            
            # Create bot instance
            self.bot = TradingTelegramBot(logger=self.logger)
            self.is_enabled = True
            
            self.logger.info("🤖 Telegram notifier initialized successfully")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to initialize Telegram notifier: {e}")
            self.logger.info("📈 Trading system will continue without Telegram notifications")
            self.is_enabled = False
    
    def is_available(self) -> bool:
        """Check if Telegram notifications are available"""
        return self.is_enabled and self.bot is not None
    
    def notify_order_placed(self, order, strategy: str, account_name: str = ""):
        """Send order placement notification"""
        if not self.is_available():
            return
        
        try:
            self.bot.send_order_placement_alert(order, strategy, account_name)
        except Exception as e:
            self.logger.error(f"Failed to send order placement notification: {e}")
    
    def notify_position_update(self, symbol: str, action: str, price: float, pnl: float, strategy: str, account_name: str = ""):
        """Send position update notification"""
        if not self.is_available():
            return
        
        try:
            self.bot.send_position_update_alert(symbol, action, price, pnl, strategy, account_name)
        except Exception as e:
            self.logger.error(f"Failed to send position update notification: {e}")
    
    def notify_daily_summary(self, total_trades: int, total_pnl: float, win_rate: float, open_positions: int):
        """Send daily summary notification"""
        if not self.is_available():
            return
        
        try:
            self.bot.send_daily_summary_alert(total_trades, total_pnl, win_rate, open_positions)
        except Exception as e:
            self.logger.error(f"Failed to send daily summary notification: {e}")
    
    def notify_balance_alert(self, current_balance: float, alert_type: str):
        """Send balance alert notification"""
        if not self.is_available():
            return
        
        try:
            self.bot.send_balance_alert(current_balance, alert_type)
        except Exception as e:
            self.logger.error(f"Failed to send balance alert notification: {e}")
    
    def notify_system_status(self, status: str, message: str):
        """Send system status notification"""
        if not self.is_available():
            return
        
        try:
            self.bot.send_system_status_alert(status, message)
        except Exception as e:
            self.logger.error(f"Failed to send system status notification: {e}")
    
    def notify_trade_signal(self, symbol: str, signal_type: str, strategy: str, price: float, confidence: float = 0.0):
        """Send trade signal notification"""
        if not self.is_available():
            return
        
        try:
            signal_emoji = "📈" if signal_type.upper() == "BUY" else "📉"
            confidence_text = f" ({confidence:.1f}% confidence)" if confidence > 0 else ""
            
            alert_text = f"""
{signal_emoji} *TRADE SIGNAL*

🏷️ *Symbol:* {symbol}
📊 *Strategy:* {strategy}
📈 *Signal:* {signal_type.upper()}
💰 *Price:* ₹{price:.2f}{confidence_text}

⏰ *Time:* {datetime.now().strftime('%H:%M:%S')}

This is a signal notification. Check your trading system for order placement.
"""
            
            # Send to all authorized users
            all_users = set(telegram_config.authorized_users + telegram_config.admin_users)
            for user_id in all_users:
                try:
                    self.bot.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                except Exception as e:
                    self.logger.error(f"Failed to send signal notification to {user_id}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to send trade signal notification: {e}")
    
    def notify_error(self, error_message: str, context: str = ""):
        """Send error notification"""
        if not self.is_available():
            return
        
        try:
            context_text = f"\n📍 *Context:* {context}" if context else ""
            
            alert_text = f"""
❌ *SYSTEM ERROR*

🚨 *Error:* {error_message}{context_text}

⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Please check the system logs for more details.
"""
            
            # Send to admin users only for errors
            for user_id in telegram_config.admin_users:
                try:
                    self.bot.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                except Exception as e:
                    self.logger.error(f"Failed to send error notification to {user_id}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to send error notification: {e}")
    
    def notify_custom(self, message: str, emoji: str = "📢", admin_only: bool = False):
        """Send custom notification"""
        if not self.is_available():
            return
        
        try:
            alert_text = f"""
{emoji} *NOTIFICATION*

{message}

⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # Choose recipients
            if admin_only:
                recipients = telegram_config.admin_users
            else:
                recipients = set(telegram_config.authorized_users + telegram_config.admin_users)
            
            for user_id in recipients:
                try:
                    self.bot.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                except Exception as e:
                    self.logger.error(f"Failed to send custom notification to {user_id}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to send custom notification: {e}")


# Global instance
telegram_notifier = TelegramNotifier()


# Convenience functions for easy access
def notify_order_placed(order, strategy: str, account_name: str = ""):
    """Global function to notify order placement"""
    telegram_notifier.notify_order_placed(order, strategy, account_name)


def notify_position_update(symbol: str, action: str, price: float, pnl: float, strategy: str, account_name: str = ""):
    """Global function to notify position updates"""
    telegram_notifier.notify_position_update(symbol, action, price, pnl, strategy, account_name)


def notify_daily_summary(total_trades: int, total_pnl: float, win_rate: float, open_positions: int):
    """Global function to notify daily summary"""
    telegram_notifier.notify_daily_summary(total_trades, total_pnl, win_rate, open_positions)


def notify_balance_alert(current_balance: float, alert_type: str):
    """Global function to notify balance alerts"""
    telegram_notifier.notify_balance_alert(current_balance, alert_type)


def notify_system_status(status: str, message: str):
    """Global function to notify system status"""
    telegram_notifier.notify_system_status(status, message)


def notify_trade_signal(symbol: str, signal_type: str, strategy: str, price: float, confidence: float = 0.0):
    """Global function to notify trade signals"""
    telegram_notifier.notify_trade_signal(symbol, signal_type, strategy, price, confidence)


def notify_error(error_message: str, context: str = ""):
    """Global function to notify errors"""
    telegram_notifier.notify_error(error_message, context)


def notify_custom(message: str, emoji: str = "📢", admin_only: bool = False):
    """Global function to send custom notifications"""
    telegram_notifier.notify_custom(message, emoji, admin_only)


def is_telegram_available() -> bool:
    """Check if Telegram notifications are available"""
    return telegram_notifier.is_available()
