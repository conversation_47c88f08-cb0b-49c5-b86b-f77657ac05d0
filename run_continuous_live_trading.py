#!/usr/bin/env python3
"""
🚀 Continuous Live Trading System
AI-Driven Continuous Market Monitoring and Trading

This script:
1. Monitors market continuously during trading hours
2. Uses AI training models for trading decisions
3. Coordinates Signal Generation, Risk Management, and Execution agents
4. Limits to maximum 5 trades per day
5. All decisions (trade, hold, exit) are made by AI agents
6. Runs continuously until market close or manual stop
"""

import os
import sys
import asyncio
import logging
import argparse
import signal
from pathlib import Path
from datetime import datetime, time as dt_time, timedelta
from typing import Dict, List, Any, Optional
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import agents and utilities
from agents.signal_generation_agent import SignalGenerationAgent
from agents.risk_agent import RiskManagementAgent
from agents.execution_agent import ExecutionAgent
from agents.market_monitoring_agent import MarketMonitoringAgent
from run_enhanced_paper_trading import EnhancedPaperTradingWorkflow
from utils.nse_500_universe import NSE500Universe
from utils.config_loader import load_config_for_agent, ConfigurationLoader

# Import data models
try:
    from utils.execution_models import SignalPayload
except ImportError:
    # Fallback if execution_models doesn't exist
    from dataclasses import dataclass

    @dataclass
    class SignalPayload:
        symbol: str
        exchange: str
        symbol_token: str
        action: str
        entry_price: float
        sl_price: float
        target_price: float
        quantity: int
        strategy_name: str
        signal_id: str

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/continuous_trading_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ContinuousLiveTradingSystem:
    """
    Continuous Live Trading System with AI Agent Coordination
    """
    
    def __init__(self, mode: str = "live", max_daily_trades: int = 5):
        """Initialize continuous trading system"""
        
        self.mode = mode
        self.max_daily_trades = max_daily_trades
        self.trades_today = 0
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        # Agent instances
        self.signal_agent = None
        self.risk_agent = None
        self.execution_agent = None
        self.market_agent = None
        
        # Trading state
        self.active_positions = {}
        self.selected_stocks = []
        self.last_signal_check = None
        self.trading_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Performance tracking
        self.session_stats = {
            "signals_generated": 0,
            "trades_executed": 0,
            "trades_rejected": 0,
            "total_pnl": 0.0,
            "start_time": datetime.now(),
            "last_activity": datetime.now()
        }
        
        logger.info(f"🚀 Continuous Live Trading System initialized in {mode} mode")
        logger.info(f"📊 Max daily trades: {max_daily_trades}")
        logger.info(f"🆔 Session ID: {self.trading_session_id}")
    
    async def initialize_agents(self) -> bool:
        """Initialize all trading agents"""
        try:
            print("\n🤖 Initializing AI Trading Agents...")
            
            # Initialize Market Monitoring Agent
            print("   📊 Initializing Market Monitoring Agent...")
            self.market_agent = MarketMonitoringAgent()
            await self.market_agent.setup()
            print("   ✅ Market Monitoring Agent ready")
            
            # Initialize Signal Generation Agent
            print("   🧠 Initializing Signal Generation Agent...")
            self.signal_agent = SignalGenerationAgent()
            await self.signal_agent.setup()
            print("   ✅ Signal Generation Agent ready")
            
            # Initialize Risk Management Agent
            print("   🛡️ Initializing Risk Management Agent...")
            self.risk_agent = RiskManagementAgent()
            await self.risk_agent.setup()
            print("   ✅ Risk Management Agent ready")
            
            # Initialize Execution Agent
            print("   ⚡ Initializing Execution Agent...")
            try:
                self.execution_agent = ExecutionAgent(trading_mode=self.mode)
                success = await self.execution_agent.initialize()

                if not success:
                    logger.error("❌ Failed to initialize Execution Agent")
                    return False
            except Exception as e:
                logger.error(f"❌ Exception during Execution Agent initialization: {e}")
                print(f"   ❌ Execution Agent error: {e}")
                return False
            
            if self.mode == "live":
                print("   ✅ Execution Agent ready for LIVE TRADING")
            else:
                print("   ✅ Execution Agent ready for paper trading")
            
            # Connect agents for coordination
            self.signal_agent.risk_management_agent = self.risk_agent
            
            print("   🔗 Agent coordination established")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize agents: {e}")
            return False
    
    async def select_trading_universe(self) -> bool:
        """Select stocks for continuous monitoring"""
        try:
            print("\n🎯 Selecting Trading Universe...")
            
            # Use enhanced stock selection with continuous trading optimizations
            enhanced_workflow = EnhancedPaperTradingWorkflow(mode=self.mode)
            await enhanced_workflow._initialize_universe()

            # Override some parameters for continuous trading
            print("   🎯 Optimizing for continuous trading (more stocks, relaxed filters)...")
            await enhanced_workflow._select_trading_stocks()
            
            self.selected_stocks = enhanced_workflow.selected_stocks

            # Ensure minimum stock count for continuous trading
            if len(self.selected_stocks) < 5:
                print(f"   ⚠️ Only {len(self.selected_stocks)} stocks selected, adding more for continuous trading...")

                # Use the same universe as the enhanced workflow
                universe = enhanced_workflow.universe
                large_cap_stocks = universe.get_stocks_by_market_cap("Large")
                nifty_50_stocks = [s for s in large_cap_stocks if s.nifty_50]

                # Add stocks not already selected
                existing_symbols = {s.symbol for s in self.selected_stocks}
                additional_stocks = [s for s in nifty_50_stocks if s.symbol not in existing_symbols]

                print(f"   📊 Found {len(additional_stocks)} additional Nifty 50 stocks to add")

                # Add up to 10 total stocks
                needed = min(10 - len(self.selected_stocks), len(additional_stocks))
                if needed > 0:
                    self.selected_stocks.extend(additional_stocks[:needed])
                    print(f"   ✅ Added {needed} stocks: {', '.join([s.symbol for s in additional_stocks[:needed]])}")

                print(f"   ✅ Enhanced to {len(self.selected_stocks)} stocks for better trading opportunities")

            if not self.selected_stocks:
                logger.error("❌ No stocks selected for trading")
                return False
            
            print(f"   ✅ Selected {len(self.selected_stocks)} stocks for monitoring")
            print(f"   📈 Top stocks: {', '.join([stock.symbol for stock in self.selected_stocks[:5]])}")

            # Start WebSocket connection first
            if self.market_agent and hasattr(self.market_agent, 'start_background_tasks'):
                print("   🔗 Starting WebSocket connection...")
                await self.market_agent.start_background_tasks()
                print("   ✅ WebSocket connection established")

                # Now setup WebSocket subscriptions
                if hasattr(self.market_agent, 'subscribe_to_symbols'):
                    symbols = [stock.symbol for stock in self.selected_stocks]
                    await self.market_agent.subscribe_to_symbols(symbols)
                    print("   📡 WebSocket subscriptions established")
                else:
                    print("   📡 WebSocket subscriptions not available (using existing data)")
            else:
                print("   📡 WebSocket connection not available (using existing data)")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to select trading universe: {e}")
            return False
    
    def is_market_hours(self) -> bool:
        """Check if current time is during market hours"""
        try:
            import pytz
            
            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()
            
            # Market hours: 9:15 AM to 3:30 PM
            market_open = dt_time(9, 15)
            market_close = dt_time(15, 30)
            
            # Check if it's a weekday
            is_weekday = now.weekday() < 5
            
            return is_weekday and market_open <= current_time <= market_close
            
        except Exception as e:
            logger.warning(f"Error checking market hours: {e}")
            return False
    
    def is_trading_window(self) -> bool:
        """Check if current time is within active trading window"""
        try:
            current_time = datetime.now().time()
            
            # Active trading window: 9:20 AM to 2:30 PM
            trading_start = dt_time(9, 20)
            trading_end = dt_time(14, 30)
            
            return trading_start <= current_time <= trading_end
            
        except Exception as e:
            logger.warning(f"Error checking trading window: {e}")
            return False
    
    async def wait_for_market_open(self):
        """Wait for market to open"""
        while not self.is_market_hours() and not self.shutdown_event.is_set():
            current_time = datetime.now().time()
            market_open = dt_time(9, 15)
            
            if current_time < market_open:
                wait_minutes = (datetime.combine(datetime.now().date(), market_open) - 
                              datetime.combine(datetime.now().date(), current_time)).total_seconds() / 60
                print(f"⏰ Market opens in {wait_minutes:.0f} minutes. Waiting...")
            else:
                print("📅 Market is closed (weekend/holiday). Waiting for next trading day...")
            
            await asyncio.sleep(60)  # Check every minute

    async def continuous_trading_loop(self):
        """Main continuous trading loop"""
        try:
            print("\n🔄 Starting Continuous Trading Loop...")
            print(f"📊 Mode: {self.mode.upper()}")
            print(f"🎯 Max daily trades: {self.max_daily_trades}")
            print(f"⏰ Trading window: 09:20 - 14:30")

            self.running = True
            signal_check_interval = 30  # Check for signals every 30 seconds
            position_check_interval = 10  # Check positions every 10 seconds

            last_signal_check = datetime.now()
            last_position_check = datetime.now()

            while self.running and not self.shutdown_event.is_set():
                try:
                    current_time = datetime.now()

                    # Check if market is still open
                    if not self.is_market_hours():
                        print("📅 Market closed. Ending trading session...")
                        break

                    # Update session stats
                    self.session_stats["last_activity"] = current_time

                    # Check for new trading signals
                    if (current_time - last_signal_check).total_seconds() >= signal_check_interval:
                        if self.is_trading_window() and self.trades_today < self.max_daily_trades:
                            await self._check_for_trading_signals()
                        last_signal_check = current_time

                    # Monitor existing positions
                    if (current_time - last_position_check).total_seconds() >= position_check_interval:
                        await self._monitor_positions()
                        last_position_check = current_time

                    # Auto square-off near market close
                    if current_time.time() >= dt_time(15, 20):
                        await self._auto_square_off_positions()
                        break

                    # Short sleep to prevent excessive CPU usage
                    await asyncio.sleep(5)

                except Exception as e:
                    logger.error(f"❌ Error in trading loop: {e}")
                    await asyncio.sleep(10)  # Wait before retrying

            print("✅ Continuous trading loop completed")

        except Exception as e:
            logger.error(f"❌ Critical error in trading loop: {e}")
        finally:
            self.running = False

    async def _check_for_trading_signals(self):
        """Check for new trading signals from AI agents"""
        try:
            if not self.signal_agent or not self.selected_stocks:
                return

            # Rotate through selected stocks for signal generation
            for stock in self.selected_stocks:
                try:
                    # Generate signal using AI agent
                    # Get recent market data for the symbol
                    if self.market_agent and stock.symbol in self.market_agent.market_data:
                        # Get OHLCV data from market agent's stored data
                        ohlcv_data = list(self.market_agent.market_data[stock.symbol]['5min'])[-100:]

                        # Get indicators for the symbol
                        indicators = self.market_agent.get_indicators(stock.symbol)

                        # Get current market regime
                        market_regime = self.market_agent.get_market_regime()

                        if ohlcv_data and indicators:
                            # Process market data to generate signals
                            signals = await self.signal_agent.process_market_data(
                                symbol=stock.symbol,
                                ohlcv_data=ohlcv_data,
                                indicators=indicators,
                                market_regime=market_regime
                            )

                            # Get the first valid signal
                            signal = signals[0] if signals else None
                        else:
                            signal = None
                    else:
                        signal = None

                    if signal and signal.action in ["BUY", "SELL"]:
                        self.session_stats["signals_generated"] += 1

                        print(f"🎯 Signal generated: {signal.action} {signal.symbol} @ {signal.entry_price:.2f}")
                        print(f"   📊 Confidence: {signal.confidence:.2f}")
                        print(f"   🛡️ Stop Loss: {signal.stop_loss:.2f}")
                        print(f"   🎯 Take Profit: {signal.take_profit:.2f}")

                        # Process signal through risk management and execution
                        success = await self._process_trading_signal(signal)

                        if success:
                            self.trades_today += 1
                            print(f"✅ Trade executed. Daily trades: {self.trades_today}/{self.max_daily_trades}")

                            # Stop if we've reached daily limit
                            if self.trades_today >= self.max_daily_trades:
                                print(f"🛑 Daily trade limit reached ({self.max_daily_trades})")
                                break
                        else:
                            self.session_stats["trades_rejected"] += 1

                    # Small delay between stocks to avoid overwhelming the system
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"❌ Error generating signal for {stock.symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"❌ Error checking for trading signals: {e}")

    async def _process_trading_signal(self, signal) -> bool:
        """Process trading signal through risk management and execution"""
        try:
            # Step 1: Risk Management Validation
            if not self.risk_agent:
                logger.error("❌ Risk agent not available")
                return False

            # Convert signal to trade request for risk validation
            try:
                from utils.risk_models import TradeRequest, TradeDirection, ProductType, OrderType
            except ImportError:
                # Fallback if risk_models doesn't exist - create basic classes
                from dataclasses import dataclass
                from enum import Enum

                class TradeDirection(Enum):
                    LONG = "LONG"
                    SHORT = "SHORT"

                class ProductType(Enum):
                    MIS = "MIS"
                    CNC = "CNC"

                class OrderType(Enum):
                    LIMIT = "LIMIT"
                    MARKET = "MARKET"

                @dataclass
                class TradeRequest:
                    signal_id: str
                    symbol: str
                    exchange: str
                    strategy_name: str
                    direction: TradeDirection
                    entry_price: float
                    stop_loss: float
                    take_profit: float
                    quantity: int
                    product_type: ProductType
                    order_type: OrderType
                    risk_amount: float
                    capital_allocated: float
                    risk_reward_ratio: float
                    market_regime: str
                    confidence: float
                    timestamp: any
                    context: dict

            trade_request = TradeRequest(
                signal_id=signal.signal_id,
                symbol=signal.symbol,
                exchange="NSE",
                strategy_name=signal.strategy_name,
                direction=TradeDirection.LONG if signal.signal_type == 1 else TradeDirection.SHORT,
                entry_price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                quantity=signal.quantity,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=signal.risk_amount,
                capital_allocated=signal.capital_allocated,
                risk_reward_ratio=signal.risk_reward_ratio,
                market_regime=signal.market_regime,
                confidence=signal.confidence,
                timestamp=signal.timestamp,
                context=signal.context
            )

            # Validate with risk agent
            validation_result = await self.risk_agent.validate_trade(trade_request)

            if not validation_result.is_valid:
                print(f"❌ Risk validation failed for {signal.symbol}: {validation_result.rejection_reason}")
                return False

            print(f"✅ Risk validation passed for {signal.symbol}")

            # Step 2: Execute trade through execution agent
            if not self.execution_agent:
                logger.error("❌ Execution agent not available")
                return False

            # Convert to execution signal format

            execution_signal = SignalPayload(
                symbol=f"{signal.symbol}-EQ",
                exchange="NSE",
                symbol_token="",  # Will be resolved by execution agent
                action=signal.action,
                entry_price=signal.entry_price,
                sl_price=signal.stop_loss,
                target_price=signal.take_profit,
                quantity=signal.quantity,
                strategy_name=signal.strategy_name,
                signal_id=signal.signal_id
            )

            # Execute the trade
            success, message, trade_execution = await self.execution_agent.process_signal(execution_signal)

            if success:
                print(f"✅ Trade executed successfully: {message}")

                # Track position
                self.active_positions[signal.symbol] = {
                    "signal": signal,
                    "trade_execution": trade_execution,
                    "entry_time": datetime.now(),
                    "status": "ACTIVE"
                }

                self.session_stats["trades_executed"] += 1
                return True
            else:
                print(f"❌ Trade execution failed: {message}")
                return False

        except Exception as e:
            logger.error(f"❌ Error processing trading signal: {e}")
            return False

    async def _monitor_positions(self):
        """Monitor existing positions for exit signals"""
        try:
            if not self.active_positions:
                return

            for symbol, position_data in list(self.active_positions.items()):
                try:
                    signal = position_data["signal"]

                    # Check for exit signal from AI agent
                    # Get current market data for exit analysis
                    if self.market_agent and symbol in self.market_agent.market_data:
                        # Get recent OHLCV data from market agent's stored data
                        ohlcv_data = list(self.market_agent.market_data[symbol]['5min'])[-50:]

                        # Get indicators for the symbol
                        indicators = self.market_agent.get_indicators(symbol)

                        # Get current market regime
                        market_regime = self.market_agent.get_market_regime()

                        if ohlcv_data and indicators:
                            # Process market data to check for exit signals
                            signals = await self.signal_agent.process_market_data(
                                symbol=symbol,
                                ohlcv_data=ohlcv_data,
                                indicators=indicators,
                                market_regime=market_regime
                            )

                            # Look for exit signals or opposite direction signals
                            exit_signal = None
                            for sig in signals:
                                if (sig.action == "EXIT" or
                                    (signal.action == "BUY" and sig.action == "SELL") or
                                    (signal.action == "SELL" and sig.action == "BUY")):
                                    exit_signal = sig
                                    break
                        else:
                            exit_signal = None
                    else:
                        exit_signal = None

                    if exit_signal and exit_signal.action == "EXIT":
                        print(f"🚪 Exit signal generated for {symbol}")

                        # Execute exit through execution agent
                        success = await self._execute_position_exit(symbol, exit_signal)

                        if success:
                            # Remove from active positions
                            del self.active_positions[symbol]
                            print(f"✅ Position closed for {symbol}")

                except Exception as e:
                    logger.error(f"❌ Error monitoring position {symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"❌ Error monitoring positions: {e}")

    async def _execute_position_exit(self, symbol: str, exit_signal) -> bool:
        """Execute position exit"""
        try:
            if not self.execution_agent:
                return False

            # Create exit signal

            execution_signal = SignalPayload(
                symbol=f"{symbol}-EQ",
                exchange="NSE",
                symbol_token="",
                action="SELL" if exit_signal.original_action == "BUY" else "BUY",  # Opposite action
                entry_price=exit_signal.exit_price,
                sl_price=0,  # No SL for exit
                target_price=0,  # No target for exit
                quantity=exit_signal.quantity,
                strategy_name=exit_signal.strategy_name,
                signal_id=f"EXIT_{exit_signal.signal_id}"
            )

            success, message, trade_execution = await self.execution_agent.process_signal(execution_signal)

            if success:
                print(f"✅ Position exit executed: {message}")
                return True
            else:
                print(f"❌ Position exit failed: {message}")
                return False

        except Exception as e:
            logger.error(f"❌ Error executing position exit: {e}")
            return False

    async def _auto_square_off_positions(self):
        """Auto square-off all positions near market close"""
        try:
            if not self.active_positions:
                return

            print("🔄 Auto square-off: Closing all positions before market close...")

            for symbol, position_data in list(self.active_positions.items()):
                try:
                    signal = position_data["signal"]

                    # Create square-off signal

                    execution_signal = SignalPayload(
                        symbol=f"{symbol}-EQ",
                        exchange="NSE",
                        symbol_token="",
                        action="SELL" if signal.action == "BUY" else "BUY",
                        entry_price=0,  # Market price
                        sl_price=0,
                        target_price=0,
                        quantity=signal.quantity,
                        strategy_name="AUTO_SQUARE_OFF",
                        signal_id=f"SQUARE_OFF_{signal.signal_id}"
                    )

                    success, message, _ = await self.execution_agent.process_signal(execution_signal)

                    if success:
                        print(f"✅ Auto square-off completed for {symbol}")
                        del self.active_positions[symbol]
                    else:
                        print(f"❌ Auto square-off failed for {symbol}: {message}")

                except Exception as e:
                    logger.error(f"❌ Error in auto square-off for {symbol}: {e}")
                    continue

        except Exception as e:
            logger.error(f"❌ Error in auto square-off: {e}")

    async def generate_session_report(self) -> Dict[str, Any]:
        """Generate comprehensive session report"""
        try:
            end_time = datetime.now()
            session_duration = end_time - self.session_stats["start_time"]

            report = {
                "session_id": self.trading_session_id,
                "mode": self.mode,
                "start_time": self.session_stats["start_time"].isoformat(),
                "end_time": end_time.isoformat(),
                "duration_minutes": session_duration.total_seconds() / 60,
                "trading_stats": {
                    "signals_generated": self.session_stats["signals_generated"],
                    "trades_executed": self.session_stats["trades_executed"],
                    "trades_rejected": self.session_stats["trades_rejected"],
                    "success_rate": (self.session_stats["trades_executed"] /
                                   max(self.session_stats["signals_generated"], 1)) * 100,
                    "daily_trade_limit": self.max_daily_trades,
                    "trades_remaining": max(0, self.max_daily_trades - self.trades_today)
                },
                "positions": {
                    "active_positions": len(self.active_positions),
                    "position_details": list(self.active_positions.keys())
                },
                "selected_stocks": [stock.symbol for stock in self.selected_stocks] if self.selected_stocks else [],
                "system_status": {
                    "signal_agent": "ACTIVE" if self.signal_agent else "INACTIVE",
                    "risk_agent": "ACTIVE" if self.risk_agent else "INACTIVE",
                    "execution_agent": "ACTIVE" if self.execution_agent else "INACTIVE",
                    "market_agent": "ACTIVE" if self.market_agent else "INACTIVE"
                }
            }

            return report

        except Exception as e:
            logger.error(f"❌ Error generating session report: {e}")
            return {"error": str(e)}

    async def cleanup(self):
        """Cleanup resources and save final data"""
        try:
            print("\n🧹 Cleaning up resources...")

            # Stop all agents
            if self.signal_agent and hasattr(self.signal_agent, 'cleanup'):
                await self.signal_agent.cleanup()
            elif self.signal_agent and hasattr(self.signal_agent, 'stop'):
                await self.signal_agent.stop()

            if self.risk_agent and hasattr(self.risk_agent, 'cleanup'):
                await self.risk_agent.cleanup()
            elif self.risk_agent and hasattr(self.risk_agent, 'shutdown'):
                await self.risk_agent.shutdown()

            if self.execution_agent and hasattr(self.execution_agent, 'cleanup'):
                await self.execution_agent.cleanup()

            if self.market_agent and hasattr(self.market_agent, 'cleanup'):
                await self.market_agent.cleanup()
            elif self.market_agent and hasattr(self.market_agent, 'stop'):
                await self.market_agent.stop()

            # Generate final report
            final_report = await self.generate_session_report()

            # Save report
            reports_dir = Path("reports/continuous_trading")
            reports_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"continuous_trading_{self.mode}_{timestamp}.json"

            with open(report_file, 'w') as f:
                json.dump(final_report, f, indent=2, default=str)

            print(f"💾 Final report saved to: {report_file}")
            print("✅ Cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print(f"\n🛑 Received signal {signum}. Initiating graceful shutdown...")
            self.shutdown_event.set()
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def run_continuous_trading(self) -> Dict[str, Any]:
        """Main method to run continuous trading"""
        try:
            print("\n" + "="*80)
            print("🚀 CONTINUOUS LIVE TRADING SYSTEM")
            print("="*80)
            print(f"📊 Mode: {self.mode.upper()}")
            print(f"🎯 Max daily trades: {self.max_daily_trades}")
            print(f"🆔 Session: {self.trading_session_id}")
            print("="*80)

            # Setup signal handlers
            self.setup_signal_handlers()

            # Step 1: Initialize all agents
            print("\n🤖 Step 1: Initializing AI Trading Agents...")
            if not await self.initialize_agents():
                return {"error": "Failed to initialize agents"}

            # Step 2: Select trading universe
            print("\n🎯 Step 2: Selecting Trading Universe...")
            if not await self.select_trading_universe():
                return {"error": "Failed to select trading universe"}

            # Step 3: Wait for market to open (if needed)
            if not self.is_market_hours():
                print("\n⏰ Step 3: Waiting for Market to Open...")
                await self.wait_for_market_open()

            if self.shutdown_event.is_set():
                print("🛑 Shutdown requested before trading started")
                return {"status": "SHUTDOWN_BEFORE_START"}

            # Step 4: Start continuous trading
            print("\n🔄 Step 4: Starting Continuous Trading...")

            if self.mode == "live":
                print("🚨 LIVE TRADING MODE - REAL MONEY WILL BE USED!")
            else:
                print("📝 Paper trading mode - No real money used")

            print(f"⏰ Current time: {datetime.now().strftime('%H:%M:%S')}")
            print(f"📊 Monitoring {len(self.selected_stocks)} stocks")
            print(f"🎯 Ready to execute up to {self.max_daily_trades} trades")

            # Run the continuous trading loop
            await self.continuous_trading_loop()

            # Step 5: Generate final report
            print("\n📊 Step 5: Generating Final Report...")
            final_report = await self.generate_session_report()

            print("\n" + "="*80)
            print("📊 TRADING SESSION SUMMARY")
            print("="*80)
            print(f"🕐 Duration: {final_report.get('duration_minutes', 0):.1f} minutes")
            print(f"📈 Signals Generated: {final_report['trading_stats']['signals_generated']}")
            print(f"✅ Trades Executed: {final_report['trading_stats']['trades_executed']}")
            print(f"❌ Trades Rejected: {final_report['trading_stats']['trades_rejected']}")
            print(f"📊 Success Rate: {final_report['trading_stats']['success_rate']:.1f}%")
            print(f"🎯 Trades Remaining: {final_report['trading_stats']['trades_remaining']}")
            print("="*80)

            return {
                "status": "SUCCESS",
                "session_report": final_report,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Continuous trading failed: {e}")
            return {"error": str(e)}
        finally:
            await self.cleanup()


async def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Continuous Live Trading System")
    parser.add_argument("--mode", choices=["live", "paper", "demo"],
                       default="paper", help="Trading mode")
    parser.add_argument("--max-trades", "--max-trade", type=int, default=5,
                       help="Maximum trades per day (default: 5)")
    parser.add_argument("--stocks", type=int, default=20,
                       help="Number of stocks to monitor (default: 20)")

    args = parser.parse_args()

    # Create logs directory
    Path("logs").mkdir(exist_ok=True)

    try:
        print("🚀 Starting Continuous Live Trading System...")

        # Safety confirmation for live trading
        if args.mode == "live":
            print("\n" + "="*60)
            print("🚨 CRITICAL WARNING: LIVE TRADING MODE")
            print("="*60)
            print("⚠️  REAL MONEY WILL BE USED!")
            print("⚠️  REAL ORDERS WILL BE PLACED ON NSE!")
            print("⚠️  YOU CAN LOSE MONEY!")
            print("⚠️  This system will run continuously during market hours!")
            print("⚠️  Maximum trades per day:", args.max_trades)
            print("="*60)

            print("\nThis system will:")
            print("✓ Monitor market continuously during trading hours")
            print("✓ Use AI agents for all trading decisions")
            print("✓ Place real BUY/SELL orders on NSE")
            print("✓ Apply stop-loss and take-profit automatically")
            print("✓ Auto square-off positions before market close")
            print(f"✓ Limit to maximum {args.max_trades} trades per day")

            # Multiple confirmations
            confirm1 = input("\nType 'I UNDERSTAND THE RISKS' to confirm: ")
            if confirm1 != "I UNDERSTAND THE RISKS":
                print("❌ Confirmation failed. Exiting...")
                return

            confirm2 = input("Type 'START LIVE TRADING' to begin: ")
            if confirm2 != "START LIVE TRADING":
                print("❌ Confirmation failed. Exiting...")
                return

            print("\n🚨 LIVE TRADING CONFIRMED - Starting continuous trading...")
            print("💰 Real money will be used for trading!")

        elif args.mode == "paper":
            print("📝 Paper trading mode - No real money will be used")
        else:
            print("🎮 Demo mode - Simulation only")

        # Initialize and run system
        system = ContinuousLiveTradingSystem(
            mode=args.mode,
            max_daily_trades=args.max_trades
        )

        results = await system.run_continuous_trading()

        if results.get("status") == "SUCCESS":
            print("\n🎉 Continuous trading session completed successfully!")
        else:
            print(f"\n❌ Trading session ended with error: {results.get('error', 'Unknown error')}")

        return results

    except KeyboardInterrupt:
        print("\n🛑 Trading interrupted by user")
    except Exception as e:
        logger.error(f"❌ Critical error in main: {e}")
        print(f"❌ Critical error: {e}")


if __name__ == "__main__":
    # Ensure proper event loop handling
    try:
        results = asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 System shutdown by user")
    except Exception as e:
        print(f"❌ System error: {e}")
        logger.error(f"System error: {e}")
