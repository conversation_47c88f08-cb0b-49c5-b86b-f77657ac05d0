#!/usr/bin/env python3
"""
Verify that the system is using stocks_to_monitor.csv and MongoDB correctly
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.stock_symbol_integration_service import StockSymbolIntegrationService
from services.efficient_market_data_service import EfficientMarketDataService

def setup_logging():
    """Setup logging configuration"""
    logger = logging.getLogger('VerifyIntegration')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(console_handler)
    
    return logger

def verify_integration():
    """Verify that the system is using stocks_to_monitor.csv and MongoDB correctly"""
    logger = setup_logging()
    
    logger.info("=" * 70)
    logger.info("VERIFYING INTEGRATION BETWEEN STOCKS_TO_MONITOR.CSV AND MONGODB")
    logger.info("=" * 70)
    
    # Step 1: Read stocks_to_monitor.csv directly
    csv_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'stocks_to_monitor.csv')
    if not os.path.exists(csv_path):
        logger.error(f"CSV file not found: {csv_path}")
        return False
    
    logger.info(f"Reading CSV file: {csv_path}")
    df = pd.read_csv(csv_path)
    csv_symbols = df['symbol'].tolist()
    logger.info(f"Found {len(csv_symbols)} symbols in CSV file")
    
    # Step 2: Initialize services
    logger.info("\nInitializing services...")
    symbol_service = StockSymbolIntegrationService(data_directory="../data", logger=logger)
    market_data_service = EfficientMarketDataService(logger=logger)
    
    # Connect services
    market_data_service.set_symbol_integration_service(symbol_service)
    
    # Step 3: Get symbols from integration service
    service_symbols = symbol_service.get_monitored_symbols()
    logger.info(f"Found {len(service_symbols)} symbols from integration service")
    
    # Step 4: Verify symbols match
    missing_in_service = set(csv_symbols) - set(service_symbols)
    if missing_in_service:
        logger.warning(f"Symbols in CSV but missing in service: {missing_in_service}")
    else:
        logger.info("✅ All CSV symbols found in integration service")
    
    # Step 5: Check MongoDB connection
    mongo_status = symbol_service.get_connection_status()
    logger.info("\nMongoDB Connection Status:")
    if mongo_status.get('mongodb_connected', False):
        logger.info("✅ MongoDB connected")
        if 'collections' in mongo_status:
            for collection, count in mongo_status['collections'].items():
                logger.info(f"  - {collection}: {count} documents")
    else:
        logger.warning("❌ MongoDB not connected")
    
    # Step 6: Test token lookup for each symbol
    logger.info("\nTesting token lookup for each symbol:")
    tokens_found = 0
    tokens_missing = 0
    
    for symbol in service_symbols[:10]:  # Test first 10 symbols
        token_info = market_data_service.get_symbol_token(symbol)
        if token_info:
            token, exchange = token_info
            logger.info(f"  ✅ {symbol}: Token={token}, Exchange={exchange}")
            tokens_found += 1
        else:
            logger.warning(f"  ❌ {symbol}: No token found")
            tokens_missing += 1
    
    logger.info(f"\nToken lookup results (sample of 10 symbols):")
    logger.info(f"  - Found: {tokens_found}")
    logger.info(f"  - Missing: {tokens_missing}")
    
    # Step 7: Summary
    logger.info("\nINTEGRATION VERIFICATION SUMMARY:")
    if len(csv_symbols) == len(service_symbols) and tokens_found > 0:
        logger.info("✅ Integration is working correctly!")
        logger.info(f"  - CSV symbols: {len(csv_symbols)}")
        logger.info(f"  - Service symbols: {len(service_symbols)}")
        logger.info(f"  - MongoDB connected: {mongo_status.get('mongodb_connected', False)}")
        logger.info(f"  - Token lookup success rate: {tokens_found/(tokens_found+tokens_missing)*100:.1f}%")
    else:
        logger.warning("⚠️ Integration has issues:")
        if len(csv_symbols) != len(service_symbols):
            logger.warning(f"  - Symbol count mismatch: CSV={len(csv_symbols)}, Service={len(service_symbols)}")
        if tokens_missing > 0:
            logger.warning(f"  - Token lookup failures: {tokens_missing}/{tokens_found+tokens_missing}")
        if not mongo_status.get('mongodb_connected', False):
            logger.warning(f"  - MongoDB not connected")
    
    logger.info("=" * 70)
    
    return True

if __name__ == "__main__":
    verify_integration()