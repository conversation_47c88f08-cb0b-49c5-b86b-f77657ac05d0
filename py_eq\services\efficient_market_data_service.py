"""
Efficient Market Data Service with MongoDB caching and live price updates
This service dramatically reduces API calls by:
1. Storing historical data in MongoDB (downloaded via YFinance)
2. Using SmartAPI only for live price updates
3. Using intelligent caching strategies
4. YFinance for historical data, SmartAPI for real-time prices
"""
import logging
import pymongo
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import os
from SmartApi import SmartConnect
import yfinance as yf
import time

from models.candle import Candle
from services.market_data_service import MarketDataServiceInterface
from services.centralized_mapping_client import CentralizedMappingClient


class EfficientMarketDataService(MarketDataServiceInterface):
    """
    Efficient market data service that minimizes API calls by using MongoDB caching
    Uses YFinance for historical data downloads and SmartAPI for live price updates
    """

    def __init__(self, logger: logging.Logger, smart_api=None, websocket_service=None):
        self.logger = logger
        self.smart_api = smart_api
        self.websocket_service = websocket_service  # WebSocket for real-time prices
        self.symbol_integration_service = None  # Will be set later if available
        
        # Initialize WebSocket service if feed token is available
        if smart_api and not websocket_service:
            try:
                feed_token = os.getenv("SMARTAPI_FEED_TOKEN")
                client_id = os.getenv("SMARTAPI_CLIENT_ID")
                api_key = os.getenv("SMARTAPI_API_KEY")
                
                if feed_token and client_id and api_key:
                    self.initialize_websocket(api_key, client_id, feed_token)
            except Exception as e:
                self.logger.warning(f"Failed to initialize WebSocket service: {e}")

        # MongoDB connection
        self.mongodb_client = None
        self.db = None
        self.mongodb_connected = False

        # Caching
        self.price_cache: Dict[str, Tuple[float, datetime]] = {}  # symbol -> (price, timestamp)
        self.candle_cache: Dict[str, Tuple[List[Candle], datetime]] = {}  # cache_key -> (candles, timestamp)

        # Enhanced cache settings to reduce API calls
        self.price_cache_duration = 60  # Increased to 60 seconds
        self.candle_cache_duration = 600  # Increased to 10 minutes

        # Enhanced rate limiting settings for SmartAPI
        self.api_call_delay = 3.0  # Increased to 3 seconds between API calls
        self.last_api_call = 0
        self.max_retries = 5  # Increased retries
        self.retry_delay = 5.0  # Increased initial retry delay
        self.max_retry_delay = 30.0  # Maximum retry delay

        # Circuit breaker pattern
        self.consecutive_failures = 0
        self.max_consecutive_failures = 10
        self.circuit_breaker_timeout = 300  # 5 minutes
        self.circuit_breaker_opened_at = None

        # Initialize centralized mapping client
        self.mapping_client = CentralizedMappingClient(logger=logger)

        # Legacy symbol mapping for fallback (will be removed gradually)
        self._legacy_symbol_mapping = {
            'RELIANCE': ('2885', 'NSE'),
            'HDFCBANK': ('1333', 'NSE'),
            'INFY': ('1594', 'NSE'),
            'TCS': ('11536', 'NSE'),
            'ICICIBANK': ('4963', 'NSE'),
            'SIEMENS': ('3150', 'NSE'),
            'AUBANK': ('21238', 'NSE'),
            'APOLLOTYRE': ('163', 'NSE'),
            'NMDC': ('15332', 'NSE'),
            'GODREJPROP': ('17875', 'NSE'),
            'ASHOKLEY': ('212', 'NSE'),
            'GODREJCP': ('1232', 'NSE'),
            'ICICIGI': ('21770', 'NSE'),
            # Additional symbols from stocks_to_monitor.csv
            'ASIANPAINT': ('236', 'NSE'),
            'BHARTIARTL': ('10604', 'NSE'),
            'M&M': ('519', 'NSE'),  # Mahindra & Mahindra - Note: Token may need updating if API errors occur
            'TATAMOTORS': ('3456', 'NSE'),
            'NESTLEIND': ('17963', 'NSE'),
            'INDHOTEL': ('1512', 'NSE'),
            'CUMMINSIND': ('1901', 'NSE'),
            'TECHM': ('13538', 'NSE'),
            # Common additional symbols
            'HDFC': ('1330', 'NSE'),
            'ITC': ('424', 'NSE'),
            'KOTAKBANK': ('492', 'NSE'),
            'LT': ('11483', 'NSE'),
            'SBIN': ('3045', 'NSE'),
            'HINDUNILVR': ('1394', 'NSE'),
            'BAJFINANCE': ('317', 'NSE'),
            'MARUTI': ('10999', 'NSE'),
            'AXISBANK': ('5900', 'NSE'),
            'TATASTEEL': ('3499', 'NSE'),
            'SUNPHARMA': ('3351', 'NSE'),
            'TITAN': ('3506', 'NSE'),
            'BAJAJFINSV': ('16675', 'NSE'),
            'WIPRO': ('3787', 'NSE'),
            'HCLTECH': ('7229', 'NSE'),
            'ULTRACEMCO': ('11532', 'NSE')
        }

        # Initialize MongoDB connection
        self._connect_mongodb()

    def _connect_mongodb(self):
        """Connect to MongoDB with retry logic and connection pooling"""
        import time

        max_retries = 3
        retry_delay = 2  # seconds

        for attempt in range(max_retries):
            try:
                connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017/")
                database_name = os.getenv("MONGODB_DATABASE_NAME", "trading_db")

                # Enhanced connection with pooling and timeout settings
                self.mongodb_client = pymongo.MongoClient(
                    connection_string,
                    serverSelectionTimeoutMS=5000,  # 5 second timeout
                    connectTimeoutMS=5000,
                    socketTimeoutMS=5000,
                    maxPoolSize=50,  # Connection pool size
                    minPoolSize=5,
                    maxIdleTimeMS=30000,  # 30 seconds
                    retryWrites=True
                )
                self.db = self.mongodb_client[database_name]

                # Test connection
                self.mongodb_client.admin.command('ping')
                self.mongodb_connected = True
                self.logger.info(f"✅ Connected to MongoDB for efficient data caching (attempt {attempt + 1})")

                # Create indexes for better performance
                self._create_indexes()
                return

            except Exception as e:
                self.logger.warning(f"⚠️ MongoDB connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"🔄 Retrying MongoDB connection in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.warning("❌ All MongoDB connection attempts failed. Will use API fallback.")
                    self.mongodb_connected = False

    def _create_indexes(self):
        """Create MongoDB indexes for better query performance"""
        try:
            if not self.mongodb_connected:
                return

            # Create indexes for common timeframes
            timeframes = ["5min", "15min", "1h", "1d"]
            for timeframe in timeframes:
                collection_name = f"{timeframe}_candles"
                collection = self.db[collection_name]

                # Create compound index on symbol and timestamp for faster queries
                collection.create_index([("symbol", 1), ("timestamp", 1)], background=True)
                # Create index on timestamp for time-based queries
                collection.create_index([("timestamp", 1)], background=True)

            self.logger.debug("📊 Created MongoDB indexes for better performance")

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to create MongoDB indexes: {e}")

    def _wait_for_rate_limit(self):
        """Enhanced rate limiting with circuit breaker pattern"""
        import time

        # Check circuit breaker
        if self._is_circuit_breaker_open():
            self.logger.warning("Circuit breaker is open, skipping API call")
            return False

        current_time = time.time()
        time_since_last_call = current_time - self.last_api_call

        if time_since_last_call < self.api_call_delay:
            sleep_time = self.api_call_delay - time_since_last_call
            self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            time.sleep(sleep_time)

        self.last_api_call = time.time()
        return True

    def _is_circuit_breaker_open(self):
        """Check if circuit breaker is open"""
        import time

        if self.circuit_breaker_opened_at is None:
            return False

        # Check if timeout has passed
        if time.time() - self.circuit_breaker_opened_at > self.circuit_breaker_timeout:
            self.logger.info("Circuit breaker timeout expired, resetting")
            self.circuit_breaker_opened_at = None
            self.consecutive_failures = 0
            return False

        return True

    def _open_circuit_breaker(self):
        """Open circuit breaker due to consecutive failures"""
        import time
        self.circuit_breaker_opened_at = time.time()
        self.logger.warning(f"Circuit breaker opened due to {self.consecutive_failures} consecutive failures. Will retry in {self.circuit_breaker_timeout} seconds")

    def _make_api_call_with_retry(self, api_func, *args, **kwargs):
        """Enhanced API call with exponential backoff and circuit breaker"""
        import time

        # Check circuit breaker first
        if not self._wait_for_rate_limit():
            return None

        current_retry_delay = self.retry_delay

        for attempt in range(self.max_retries):
            try:
                result = api_func(*args, **kwargs)
                # Success - reset consecutive failures
                self.consecutive_failures = 0
                return result

            except Exception as e:
                error_msg = str(e).lower()

                # Enhanced error detection for rate limiting
                is_rate_limit_error = any(phrase in error_msg for phrase in [
                    "access denied", "rate", "exceeding access rate",
                    "too many requests", "quota exceeded", "throttled"
                ])

                if is_rate_limit_error:
                    self.consecutive_failures += 1

                    # Check if we should open circuit breaker
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        self._open_circuit_breaker()
                        return None

                    if attempt < self.max_retries - 1:
                        self.logger.warning(f"Rate limit hit, retrying in {current_retry_delay} seconds (attempt {attempt + 1}/{self.max_retries})")
                        time.sleep(current_retry_delay)

                        # Exponential backoff with jitter
                        current_retry_delay = min(current_retry_delay * 2, self.max_retry_delay)
                        continue
                    else:
                        self.logger.error(f"Max retries reached for API call: {e}")
                        return None
                else:
                    # For other errors, don't retry but don't count as rate limit failure
                    self.logger.error(f"API call failed (non-rate-limit): {e}")
                    return None

        return None

    def get_last_price(self, symbol: str) -> Optional[float]:
        """
        Get live price with WebSocket priority and intelligent caching
        Priority: 1. WebSocket (real-time) 2. Cache 3. API fallback
        """
        # Priority 1: Try WebSocket first for real-time data (no rate limits!)
        if self.websocket_service:
            websocket_price = self.websocket_service.get_last_price(symbol)
            if websocket_price is not None:
                # Cache the WebSocket price for fallback
                self.price_cache[symbol] = (websocket_price, datetime.now())
                return websocket_price

        # Priority 2: Check cache if WebSocket unavailable
        if symbol in self.price_cache:
            cached_price, cached_time = self.price_cache[symbol]
            if (datetime.now() - cached_time).seconds < self.price_cache_duration:
                return cached_price

        # Priority 3: API fallback only if WebSocket and cache both fail
        self.logger.debug(f"📡 WebSocket unavailable for {symbol}, falling back to API")
        price = self._fetch_live_price(symbol)
        if price:
            self.price_cache[symbol] = (price, datetime.now())
            
            # Subscribe to this symbol in WebSocket for future updates
            if self.websocket_service and self.websocket_service.is_connected:
                # Get token info for subscription
                token_info = self.get_symbol_token(symbol)
                if token_info:
                    token, exchange = token_info
                    exchange_type = 1 if exchange == "NSE" else 2
                    self.websocket_service.subscribe_symbol(symbol, token, exchange_type)

        return price

    def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Alias for get_last_price to maintain compatibility
        """
        return self.get_last_price(symbol)

    def _fetch_live_price(self, symbol: str) -> Optional[float]:
        """
        Fetch live price from SmartAPI or YFinance with enhanced rate limiting
        NOTE: This method is now primarily a fallback when WebSocket is unavailable
        """
        # Check circuit breaker first
        if self._is_circuit_breaker_open():
            self.logger.debug(f"Circuit breaker open, skipping SmartAPI for {symbol}, using yfinance fallback")
            return self._fetch_price_from_yfinance(symbol)

        # Try SmartAPI first if available
        if self.smart_api:
            token_info = self.get_symbol_token(symbol)
            if not token_info:
                self.logger.debug(f"No token found for symbol: {symbol}, using yfinance fallback")
                return self._fetch_price_from_yfinance(symbol)
            else:
                token, exchange = token_info

                # Use rate-limited API call with correct symbol format
                def api_call():
                    # SmartAPI requires trading symbol with -EQ suffix for equity
                    trading_symbol = f"{symbol}-EQ" if exchange == "NSE" else symbol
                    return self.smart_api.ltpData(exchange, trading_symbol, token)

                response = self._make_api_call_with_retry(api_call)

                if response and response.get('status') and response.get('data'):
                    ltp = response['data'].get('ltp')
                    if ltp:
                        return float(ltp)
                elif response and not response.get('status'):
                    # Handle specific SmartAPI errors like "Failed to get symbol details"
                    error_msg = response.get('message', 'Unknown error')
                    error_code = response.get('errorcode', 'Unknown')
                    self.logger.debug(f"SmartAPI error for {symbol} (token: {token}): {error_msg} (Code: {error_code})")
                    # Fall through to yfinance fallback

        # Fallback to YFinance
        return self._fetch_price_from_yfinance(symbol)

    def _fetch_price_from_yfinance(self, symbol: str) -> Optional[float]:
        """Fetch price from YFinance as fallback"""
        try:
            yf_symbol = f"{symbol}.NS"
            ticker = yf.Ticker(yf_symbol)
            info = ticker.info
            price = info.get('currentPrice') or info.get('regularMarketPrice')
            if price:
                return float(price)
        except Exception as e:
            self.logger.debug(f"YFinance price fetch failed for {symbol}: {e}")

        return None

    def get_historical_data(self, symbol: str, timeframe: str, days: int = 3) -> List[Candle]:
        """
        Enhanced historical data retrieval with gap filling:
        1. Check memory cache first (fastest)
        2. Load from MongoDB (fast)
        3. Detect and fill gaps using SmartAPI
        4. Update current candle with live price
        5. Store new data in MongoDB for future use
        """
        cache_key = f"{symbol}_{timeframe}_{days}"

        # Step 1: Check memory cache first
        if cache_key in self.candle_cache:
            cached_candles, cached_time = self.candle_cache[cache_key]
            if (datetime.now() - cached_time).seconds < self.candle_cache_duration:
                updated_candles = self._update_latest_candle_with_live_price(cached_candles, symbol)
                self.logger.debug(f"📊 Served {len(updated_candles)} candles for {symbol} from memory cache")
                return updated_candles

        # Step 2: Load from MongoDB with gap detection
        candles = self._load_from_mongodb_with_gap_filling(symbol, timeframe, days)

        if candles and len(candles) > 0:
            # Cache the result in memory
            self.candle_cache[cache_key] = (candles, datetime.now())
            self.logger.info(f"📊 [CHART] Successfully loaded {len(candles)} candles for {symbol} from MongoDB with gap filling")
            return candles

        # Step 3: Fallback to yfinance only if MongoDB has no data
        self.logger.info(f"📡 [SIGNAL] No MongoDB data for {symbol}, fetching from yfinance...")
        candles = self._fetch_from_yfinance_and_store(symbol, timeframe, days)

        if candles:
            self.candle_cache[cache_key] = (candles, datetime.now())
            self.logger.info(f"📊 Fetched and cached {len(candles)} candles for {symbol} from yfinance")
            return candles

        self.logger.error(f"❌ All data sources failed for {symbol}")
        return []

    def _load_from_mongodb(self, symbol: str, timeframe: str, days: int) -> List[Candle]:
        """Load historical data from MongoDB"""
        if not self.mongodb_connected:
            return []

        try:
            collection_name = f"{timeframe}_candles"
            collection = self.db[collection_name]

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Query MongoDB
            cursor = collection.find(
                {
                    "symbol": symbol,
                    "timestamp": {
                        "$gte": start_date,
                        "$lte": end_date
                    }
                },
                sort=[("timestamp", pymongo.ASCENDING)]
            )

            candles = []
            for doc in cursor:
                candle = Candle(
                    timestamp=doc["timestamp"],
                    open=doc["open"],
                    high=doc["high"],
                    low=doc["low"],
                    close=doc["close"],
                    volume=doc.get("volume", 0)
                )
                candles.append(candle)

            return candles

        except Exception as e:
            self.logger.error(f"Error loading from MongoDB: {e}")
            return []

    def _load_from_mongodb_with_gap_filling(self, symbol: str, timeframe: str, days: int) -> List[Candle]:
        """Load from MongoDB and fill gaps using SmartAPI"""
        # First load existing data from MongoDB
        candles = self._load_from_mongodb_enhanced(symbol, timeframe, days)
        
        if not candles:
            return []
            
        # Check for gaps and fill them
        filled_candles = self._fill_data_gaps(symbol, timeframe, candles)
        
        # Update latest candle with live price
        return self._update_latest_candle_with_live_price(filled_candles, symbol)
    
    def _fill_data_gaps(self, symbol: str, timeframe: str, existing_candles: List[Candle]) -> List[Candle]:
        """Fill gaps in historical data using SmartAPI"""
        if not existing_candles or not self.smart_api:
            return existing_candles
            
        # Get the latest candle timestamp
        latest_candle_time = existing_candles[-1].timestamp
        current_time = datetime.now()
        
        # Check if there's a significant gap (more than 1 hour)
        time_gap = current_time - latest_candle_time
        if time_gap.total_seconds() < 3600:  # Less than 1 hour gap
            return existing_candles
            
        self.logger.info(f"📊 [GAP] Detected {time_gap} gap for {symbol}, filling with SmartAPI data...")
        
        # Fill gap using SmartAPI historical data
        gap_candles = self._fetch_gap_data_from_smartapi(symbol, timeframe, latest_candle_time, current_time)
        
        if gap_candles:
            # Merge existing and gap data
            all_candles = existing_candles + gap_candles
            # Remove duplicates and sort
            unique_candles = self._remove_duplicate_candles(all_candles)
            
            # Store new gap data in MongoDB
            if self.mongodb_connected:
                self._store_candles_in_mongodb(symbol, timeframe, gap_candles)
                
            self.logger.info(f"📊 [GAP] Filled {len(gap_candles)} candles for {symbol}")
            return unique_candles
            
        return existing_candles
    
    def _fetch_gap_data_from_smartapi(self, symbol: str, timeframe: str, start_time: datetime, end_time: datetime) -> List[Candle]:
        """Fetch gap data using SmartAPI historical data"""
        try:
            token_info = self.get_symbol_token(symbol)
            if not token_info:
                return []
                
            token, exchange = token_info
            
            # SmartAPI historical data parameters
            interval = "FIVE_MINUTE" if timeframe == "5min" else "FIFTEEN_MINUTE"
            
            def api_call():
                return self.smart_api.getCandleData({
                    "exchange": exchange,
                    "symboltoken": token,
                    "interval": interval,
                    "fromdate": start_time.strftime("%Y-%m-%d %H:%M"),
                    "todate": end_time.strftime("%Y-%m-%d %H:%M")
                })
            
            response = self._make_api_call_with_retry(api_call)
            
            if response and response.get('status') and response.get('data'):
                candles = []
                for candle_data in response['data']:
                    # SmartAPI returns: [timestamp, open, high, low, close, volume]
                    timestamp_str = candle_data[0]
                    candle_time = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S%z").replace(tzinfo=None)
                    
                    candle = Candle(
                        timestamp=candle_time,
                        open=float(candle_data[1]),
                        high=float(candle_data[2]),
                        low=float(candle_data[3]),
                        close=float(candle_data[4]),
                        volume=int(candle_data[5]) if len(candle_data) > 5 else 0
                    )
                    candles.append(candle)
                    
                return candles
                
        except Exception as e:
            self.logger.error(f"Error fetching gap data from SmartAPI for {symbol}: {e}")
            
        return []
    
    def _remove_duplicate_candles(self, candles: List[Candle]) -> List[Candle]:
        """Remove duplicate candles and sort by timestamp"""
        seen_timestamps = set()
        unique_candles = []
        
        for candle in sorted(candles, key=lambda c: c.timestamp):
            if candle.timestamp not in seen_timestamps:
                seen_timestamps.add(candle.timestamp)
                unique_candles.append(candle)
                
        return unique_candles

    def _load_from_mongodb_enhanced(self, symbol: str, timeframe: str, days: int) -> List[Candle]:
        """Enhanced MongoDB loading with better error handling, retry logic, and gap detection"""
        if not self.mongodb_connected:
            return []

        max_retries = 3
        retry_delay = 0.5  # seconds
        
        for attempt in range(max_retries):
            try:
                collection_name = f"{timeframe}_candles"
                collection = self.db[collection_name]

                # Calculate date range
                end_date = datetime.now()
                start_date = end_date - timedelta(days=days)

                # Query MongoDB with better indexing and projection to reduce data transfer
                cursor = collection.find(
                    {
                        "symbol": symbol,
                        "timestamp": {
                            "$gte": start_date,
                            "$lte": end_date
                        }
                    },
                    # Only retrieve the fields we need
                    projection={
                        "_id": 0,
                        "timestamp": 1,
                        "open": 1,
                        "high": 1,
                        "low": 1,
                        "close": 1,
                        "volume": 1
                    },
                    sort=[("timestamp", pymongo.ASCENDING)]
                ).limit(1000)  # Reasonable limit with index support

                # Use batch processing for better performance
                candles = []
                batch_size = 100
                current_batch = []
                
                for doc in cursor:
                    current_batch.append(doc)
                    
                    if len(current_batch) >= batch_size:
                        # Process the batch
                        for doc in current_batch:
                            candle = Candle(
                                timestamp=doc["timestamp"],
                                open=doc["open"],
                                high=doc["high"],
                                low=doc["low"],
                                close=doc["close"],
                                volume=doc.get("volume", 0)
                            )
                            candles.append(candle)
                        current_batch = []
                
                # Process any remaining documents
                for doc in current_batch:
                    candle = Candle(
                        timestamp=doc["timestamp"],
                        open=doc["open"],
                        high=doc["high"],
                        low=doc["low"],
                        close=doc["close"],
                        volume=doc.get("volume", 0)
                    )
                    candles.append(candle)

                # Since MongoDB has 30 days of data, we should have plenty for any reasonable request
                # Just check if we have any meaningful data
                if len(candles) == 0:
                    self.logger.warning(f"📊 [CHART] No MongoDB data found for {symbol} in last {days} days")
                    return []
                elif len(candles) < 5:
                    self.logger.warning(f"📊 [CHART] Very limited MongoDB data for {symbol}: {len(candles)} candles in last {days} days")
                    return []
                else:
                    # We have good data from MongoDB (30-day database)
                    self.logger.info(f"📊 [CHART] Loaded {len(candles)} candles for {symbol} from MongoDB (last {days} days from 30-day database)")
                    return candles

            except pymongo.errors.AutoReconnect:
                # Connection issue - retry
                if attempt < max_retries - 1:
                    self.logger.warning(f"MongoDB connection lost, retrying in {retry_delay}s (attempt {attempt+1}/{max_retries})")
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"Failed to reconnect to MongoDB after {max_retries} attempts")
                    return []
                    
            except pymongo.errors.ServerSelectionTimeoutError:
                # Server selection timeout - retry
                if attempt < max_retries - 1:
                    self.logger.warning(f"MongoDB server selection timeout, retrying in {retry_delay}s (attempt {attempt+1}/{max_retries})")
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"MongoDB server selection failed after {max_retries} attempts")
                    return []
                    
            except Exception as e:
                self.logger.error(f"Error loading from MongoDB (enhanced): {e}")
                return []
                
        return []  # If we get here, all retries failed

    def _fetch_from_yfinance_and_store(self, symbol: str, timeframe: str, days: int) -> List[Candle]:
        """Fetch data from yfinance and store in MongoDB for future use"""
        try:
            import yfinance as yf
            import pytz

            # Convert symbol to yfinance format
            yf_symbol = f"{symbol}.NS" if not symbol.endswith('.NS') else symbol

            # Calculate period
            if days <= 5:
                period = "5d"
            elif days <= 30:
                period = "1mo"
            elif days <= 90:
                period = "3mo"
            else:
                period = "6mo"

            # Determine interval
            interval = "15m" if timeframe == "15min" else "5m"

            self.logger.info(f"📡 [SIGNAL] Fetching {symbol} data from yfinance: period={period}, interval={interval}")

            # Fetch data with simple error handling
            ticker = yf.Ticker(yf_symbol)
            hist = ticker.history(period=period, interval=interval, prepost=False, auto_adjust=True)

            if hist.empty:
                self.logger.warning(f"⚠️ No yfinance data available for {symbol} (empty response)")
                return []

            # Convert to Candle objects with proper timezone handling
            candles = []
            ist_tz = pytz.timezone('Asia/Kolkata')

            for timestamp, row in hist.iterrows():
                # Handle timezone-aware timestamps properly
                if hasattr(timestamp, 'tz_localize'):
                    # If timezone-aware, convert to IST then make naive
                    if timestamp.tz is not None:
                        timestamp_naive = timestamp.tz_convert(ist_tz).tz_localize(None)
                    else:
                        # If timezone-naive, assume it's already in IST
                        timestamp_naive = timestamp
                else:
                    # Fallback for other timestamp types
                    timestamp_naive = timestamp.to_pydatetime()
                    if hasattr(timestamp_naive, 'tzinfo') and timestamp_naive.tzinfo is not None:
                        timestamp_naive = timestamp_naive.replace(tzinfo=None)

                candle = Candle(
                    timestamp=timestamp_naive,
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume']) if 'Volume' in row else 0
                )
                candles.append(candle)

            # Store in MongoDB for future use
            if candles and self.mongodb_connected:
                self._store_candles_in_mongodb(symbol, timeframe, candles)

            # Filter to requested days with timezone-naive comparison
            cutoff_date = datetime.now() - timedelta(days=days)
            filtered_candles = [c for c in candles if c.timestamp >= cutoff_date]

            self.logger.info(f"📊 Fetched {len(filtered_candles)} candles for {symbol} from yfinance")
            return filtered_candles

        except Exception as e:
            self.logger.error(f"Error fetching from yfinance: {e}")
            return []

    def _store_candles_in_mongodb(self, symbol: str, timeframe: str, candles: List[Candle]):
        """Store candles in MongoDB with upsert to avoid duplicates"""
        try:
            if not self.mongodb_connected:
                return

            collection_name = f"{timeframe}_candles"
            collection = self.db[collection_name]

            # Prepare documents for bulk upsert
            operations = []
            for candle in candles:
                doc = {
                    "symbol": symbol,
                    "timestamp": candle.timestamp,
                    "open": candle.open,
                    "high": candle.high,
                    "low": candle.low,
                    "close": candle.close,
                    "volume": candle.volume
                }

                # Use upsert to avoid duplicates
                operations.append(
                    pymongo.UpdateOne(
                        {"symbol": symbol, "timestamp": candle.timestamp},
                        {"$set": doc},
                        upsert=True
                    )
                )

            if operations:
                result = collection.bulk_write(operations)
                self.logger.info(f"📊 Stored {len(candles)} candles for {symbol} in MongoDB "
                               f"(inserted: {result.upserted_count}, modified: {result.modified_count})")

        except Exception as e:
            self.logger.error(f"Error storing candles in MongoDB: {e}")

    def _calculate_expected_candles(self, timeframe: str, days: int) -> int:
        """Calculate expected number of candles for given timeframe and days"""
        if timeframe == "5min":
            # 5-minute candles: 12 per hour, ~75 per trading day (6.25 hours)
            return days * 75
        elif timeframe == "15min":
            # 15-minute candles: 4 per hour, ~25 per trading day
            return days * 25
        else:
            # Default assumption
            return days * 50

    def _update_latest_candle_with_live_price(self, candles: List[Candle], symbol: str) -> List[Candle]:
        """Update the latest candle with current live price"""
        if not candles:
            return candles

        # Get current price
        current_price = self.get_last_price(symbol)
        if not current_price:
            return candles

        # Update the latest candle
        latest_candle = candles[-1]
        latest_candle.close = current_price

        # Update high/low if necessary
        if current_price > latest_candle.high:
            latest_candle.high = current_price
        if current_price < latest_candle.low:
            latest_candle.low = current_price

        return candles

    def _fetch_from_api_fallback(self, symbol: str, timeframe: str, days: int) -> List[Candle]:
        """Fallback method to fetch from YFinance when MongoDB is not available"""
        self.logger.warning(f"🐌 SLOW PATH: Fetching {days} days of {timeframe} data for {symbol} from YFinance")

        try:
            # Use YFinance for historical data
            yf_symbol = f"{symbol}.NS"
            ticker = yf.Ticker(yf_symbol)

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Map timeframe to YFinance interval
            interval_map = {
                "1min": "1m",
                "5min": "5m",
                "15min": "15m",
                "1h": "1h",
                "1hour": "1h",
                "1d": "1d",
                "1day": "1d"
            }

            yf_interval = interval_map.get(timeframe, "15m")

            # Download data from YFinance
            hist = ticker.history(
                start=start_date.strftime("%Y-%m-%d"),
                end=end_date.strftime("%Y-%m-%d"),
                interval=yf_interval,
                prepost=False,
                auto_adjust=True
            )

            if not hist.empty:
                import pytz
                candles = []
                ist_tz = pytz.timezone('Asia/Kolkata')

                for index, row in hist.iterrows():
                    try:
                        # Handle timezone-aware timestamps properly
                        if hasattr(index, 'tz_localize'):
                            # If timezone-aware, convert to IST then make naive
                            if index.tz is not None:
                                timestamp_naive = index.tz_convert(ist_tz).tz_localize(None)
                            else:
                                # If timezone-naive, assume it's already in IST
                                timestamp_naive = index
                        else:
                            # Fallback for other timestamp types
                            timestamp_naive = index.to_pydatetime() if hasattr(index, 'to_pydatetime') else index
                            if hasattr(timestamp_naive, 'tzinfo') and timestamp_naive.tzinfo is not None:
                                timestamp_naive = timestamp_naive.replace(tzinfo=None)

                        candle = Candle(
                            timestamp=timestamp_naive,
                            open=float(row['Open']),
                            high=float(row['High']),
                            low=float(row['Low']),
                            close=float(row['Close']),
                            volume=int(row['Volume']) if 'Volume' in row else 0
                        )
                        candles.append(candle)
                    except Exception as e:
                        self.logger.debug(f"Error processing candle data: {e}")
                        continue

                self.logger.info(f"📈 Downloaded {len(candles)} candles for {symbol} from YFinance")
                return candles
            else:
                self.logger.warning(f"No historical data available for {symbol} from YFinance")

        except Exception as e:
            self.logger.error(f"YFinance fallback failed for {symbol}: {e}")

        return []

    def get_symbol_token(self, symbol: str) -> Optional[Tuple[str, str]]:
        """Get token and exchange for a symbol using centralized mapping"""
        try:
            # Try symbol integration service first if available
            if self.symbol_integration_service:
                token_info = self.symbol_integration_service.get_symbol_token(symbol)
                if token_info:
                    return token_info
            
            # Try centralized mapping next
            token_info = self.mapping_client.get_smartapi_token(symbol, 'EQ')
            if token_info:
                return token_info

            # Fallback to legacy mapping
            legacy_result = self._legacy_symbol_mapping.get(symbol)
            if legacy_result:
                self.logger.debug(f"Using legacy mapping for {symbol}: {legacy_result}")
                return legacy_result

            self.logger.warning(f"No token mapping found for symbol: {symbol}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting token for {symbol}: {e}")
            # Fallback to legacy mapping on error
            return self._legacy_symbol_mapping.get(symbol)
            
    def set_symbol_integration_service(self, symbol_service):
        """Set the symbol integration service for more efficient token lookups"""
        self.symbol_integration_service = symbol_service
        self.logger.info("✅ Symbol integration service connected to market data service")

    def initialize_websocket(self, api_key: str, client_id: str, feed_token: str):
        """Initialize WebSocket service for real-time price updates"""
        try:
            from services.websocket_price_service import WebSocketPriceService
            
            self.websocket_service = WebSocketPriceService(
                api_key=api_key,
                client_id=client_id,
                feed_token=feed_token,
                logger=self.logger
            )
            
            # Connect to WebSocket
            self.websocket_service.connect()
            
            # Register tick callback
            self.websocket_service.register_tick_callback(self._on_websocket_tick)
            
            self.logger.info("WebSocket service initialized for real-time price updates")
            return True
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket service: {e}")
            self.websocket_service = None
            return False
            
    def _on_websocket_tick(self, symbol: str, tick_data: Dict):
        """Callback for WebSocket tick data"""
        # Update price cache with WebSocket data
        if 'ltp' in tick_data:
            self.price_cache[symbol] = (tick_data['ltp'], datetime.now())
            
    def subscribe_symbols_to_websocket(self, symbols: List[str]):
        """Subscribe to symbols for real-time WebSocket updates"""
        if not self.websocket_service or not self.websocket_service.is_connected:
            return False
            
        success_count = 0
        for symbol in symbols:
            token_info = self.get_symbol_token(symbol)
            if token_info:
                token, exchange = token_info
                exchange_type = 1 if exchange == "NSE" else 2
                if self.websocket_service.subscribe_symbol(symbol, token, exchange_type):
                    success_count += 1
                    
        return success_count == len(symbols)
        
    def close(self):
        """Close connections"""
        # Close MongoDB connection
        if self.mongodb_client:
            self.mongodb_client.close()
            self.logger.info("MongoDB connection closed")
            
        # Close WebSocket connection
        if self.websocket_service:
            self.websocket_service.disconnect()
            self.logger.info("WebSocket connection closed")
