"""
Chart Generator for Telegram Bot

This module creates beautiful, mobile-optimized charts for trading reports
that can be sent via Telegram bot messages.
"""

import io
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# Handle imports for both standalone and package execution
try:
    from ..config.telegram_config import telegram_config
    from .report_generator import TradeRecord, TradingMetrics
except ImportError:
    # Fallback for standalone execution
    import sys
    from pathlib import Path

    # Add parent directory to path
    parent_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(parent_dir))

    from config.telegram_config import telegram_config
    from services.report_generator import TradeRecord, TradingMetrics


class TelegramChartGenerator:
    """
    Generates optimized charts for Telegram bot messages
    """
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        self.config = telegram_config
        
        # Set up matplotlib for better mobile viewing
        plt.style.use('seaborn-v0_8-darkgrid')
        sns.set_palette("husl")
        
        # Configure default figure settings
        self.fig_size = (self.config.chart_width/100, self.config.chart_height/100)
        self.dpi = self.config.chart_dpi
        
    def create_daily_pnl_chart(self, trades: List[TradeRecord], metrics: TradingMetrics) -> io.BytesIO:
        """Create daily P&L breakdown chart"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6), dpi=self.dpi)
        
        # P&L Bar Chart
        categories = ['Gross Profit', 'Gross Loss', 'Net P&L']
        values = [metrics.gross_profit, -metrics.gross_loss, metrics.total_pnl]
        colors = ['#2ecc71', '#e74c3c', '#2ecc71' if metrics.total_pnl >= 0 else '#e74c3c']
        
        bars = ax1.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
        ax1.set_title('📊 Daily P&L Breakdown', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('Amount (₹)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (abs(height) * 0.01),
                    f'₹{value:,.0f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontweight='bold', fontsize=10)
        
        # Win/Loss Pie Chart
        if metrics.total_trades > 0:
            win_loss_data = [metrics.winning_trades, metrics.losing_trades]
            win_loss_labels = [f'Wins ({metrics.winning_trades})', f'Losses ({metrics.losing_trades})']
            colors_pie = ['#2ecc71', '#e74c3c']
            
            wedges, texts, autotexts = ax2.pie(win_loss_data, labels=win_loss_labels, colors=colors_pie,
                                              autopct='%1.1f%%', startangle=90, textprops={'fontsize': 10})
            ax2.set_title(f'🎯 Win Rate: {metrics.win_rate:.1f}%', fontsize=14, fontweight='bold', pad=20)
        else:
            ax2.text(0.5, 0.5, 'No trades today', ha='center', va='center', transform=ax2.transAxes,
                    fontsize=12, style='italic')
            ax2.set_title('🎯 No Trades Today', fontsize=14, fontweight='bold', pad=20)
        
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def create_strategy_performance_chart(self, metrics: TradingMetrics) -> io.BytesIO:
        """Create strategy performance comparison chart"""
        if not metrics.strategy_performance:
            return self._create_no_data_chart("No strategy data available")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6), dpi=self.dpi)
        
        strategies = list(metrics.strategy_performance.keys())
        pnl_values = [metrics.strategy_performance[s]['pnl'] for s in strategies]
        trade_counts = [metrics.strategy_performance[s]['trades'] for s in strategies]
        
        # Strategy P&L Bar Chart
        colors = ['#2ecc71' if pnl >= 0 else '#e74c3c' for pnl in pnl_values]
        bars = ax1.bar(strategies, pnl_values, color=colors, alpha=0.8, edgecolor='white', linewidth=2)
        ax1.set_title('🎯 Strategy P&L Performance', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('P&L (₹)', fontsize=12)
        ax1.tick_params(axis='x', rotation=45)
        ax1.grid(True, alpha=0.3)
        
        # Add value labels
        for bar, value in zip(bars, pnl_values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (abs(height) * 0.01),
                    f'₹{value:,.0f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontweight='bold', fontsize=9)
        
        # Trade Distribution Pie Chart
        colors_pie = plt.cm.Set3(np.linspace(0, 1, len(strategies)))
        wedges, texts, autotexts = ax2.pie(trade_counts, labels=strategies, colors=colors_pie,
                                          autopct='%1.0f', startangle=90, textprops={'fontsize': 9})
        ax2.set_title('📊 Trade Distribution', fontsize=14, fontweight='bold', pad=20)
        
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def create_weekly_trend_chart(self, daily_pnl: Dict[str, float]) -> io.BytesIO:
        """Create weekly P&L trend chart"""
        if not daily_pnl:
            return self._create_no_data_chart("No weekly data available")
        
        fig, ax = plt.subplots(figsize=(12, 6), dpi=self.dpi)
        
        dates = list(daily_pnl.keys())
        values = list(daily_pnl.values())
        
        # Convert dates to datetime objects for better formatting
        date_objects = [datetime.strptime(date, '%Y-%m-%d') for date in dates]
        
        # Create line chart with area fill
        ax.plot(date_objects, values, marker='o', linewidth=3, markersize=8, color='#3498db')
        ax.fill_between(date_objects, values, alpha=0.3, color='#3498db')
        
        # Color positive and negative areas differently
        ax.fill_between(date_objects, 0, values, where=[v >= 0 for v in values], 
                       color='#2ecc71', alpha=0.3, interpolate=True)
        ax.fill_between(date_objects, 0, values, where=[v < 0 for v in values], 
                       color='#e74c3c', alpha=0.3, interpolate=True)
        
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.set_title('📈 Weekly P&L Trend', fontsize=16, fontweight='bold', pad=20)
        ax.set_ylabel('Daily P&L (₹)', fontsize=12)
        ax.set_xlabel('Date', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))
        plt.xticks(rotation=45)
        
        # Add value annotations
        for date, value in zip(date_objects, values):
            ax.annotate(f'₹{value:,.0f}', (date, value), textcoords="offset points",
                       xytext=(0,10), ha='center', fontsize=9, fontweight='bold')
        
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def create_balance_trend_chart(self, balance_history: List[Tuple[datetime, float]]) -> io.BytesIO:
        """Create account balance trend chart"""
        if not balance_history:
            return self._create_no_data_chart("No balance history available")
        
        fig, ax = plt.subplots(figsize=(12, 6), dpi=self.dpi)
        
        dates, balances = zip(*balance_history)
        
        ax.plot(dates, balances, marker='o', linewidth=3, markersize=6, color='#9b59b6')
        ax.fill_between(dates, balances, alpha=0.3, color='#9b59b6')
        
        ax.set_title('💰 Account Balance Trend', fontsize=16, fontweight='bold', pad=20)
        ax.set_ylabel('Balance (₹)', fontsize=12)
        ax.set_xlabel('Date', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Format y-axis to show currency
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'₹{x:,.0f}'))
        
        # Format x-axis
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def create_risk_metrics_chart(self, metrics: TradingMetrics) -> io.BytesIO:
        """Create risk metrics visualization"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10), dpi=self.dpi)
        
        # Profit Factor Gauge
        self._create_gauge_chart(ax1, metrics.profit_factor, "Profit Factor", 
                                max_value=3.0, good_threshold=1.5)
        
        # Win Rate Gauge
        self._create_gauge_chart(ax2, metrics.win_rate, "Win Rate (%)", 
                                max_value=100, good_threshold=60)
        
        # Expectancy Bar
        expectancy_color = '#2ecc71' if metrics.expectancy >= 0 else '#e74c3c'
        ax3.bar(['Expectancy'], [metrics.expectancy], color=expectancy_color, alpha=0.8)
        ax3.set_title('💡 Trade Expectancy', fontsize=12, fontweight='bold')
        ax3.set_ylabel('Expected Return (₹)')
        ax3.grid(True, alpha=0.3)
        
        # Drawdown visualization
        if metrics.max_drawdown > 0:
            ax4.bar(['Max Drawdown'], [-metrics.max_drawdown], color='#e74c3c', alpha=0.8)
            ax4.set_title('📉 Maximum Drawdown', fontsize=12, fontweight='bold')
            ax4.set_ylabel('Drawdown (₹)')
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, 'No Drawdown', ha='center', va='center', 
                    transform=ax4.transAxes, fontsize=12, style='italic')
            ax4.set_title('📉 No Drawdown', fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def _create_gauge_chart(self, ax, value: float, title: str, max_value: float, good_threshold: float):
        """Create a gauge-style chart"""
        # Create semicircle
        theta = np.linspace(0, np.pi, 100)
        r = 1
        
        # Background arc
        ax.plot(r * np.cos(theta), r * np.sin(theta), 'lightgray', linewidth=10)
        
        # Value arc
        value_ratio = min(value / max_value, 1.0)
        theta_value = np.linspace(0, np.pi * value_ratio, int(100 * value_ratio))
        
        color = '#2ecc71' if value >= good_threshold else '#f39c12' if value >= good_threshold * 0.7 else '#e74c3c'
        ax.plot(r * np.cos(theta_value), r * np.sin(theta_value), color, linewidth=10)
        
        # Add value text
        ax.text(0, -0.3, f'{value:.1f}', ha='center', va='center', fontsize=16, fontweight='bold')
        ax.text(0, -0.5, title, ha='center', va='center', fontsize=10)
        
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-0.7, 1.2)
        ax.set_aspect('equal')
        ax.axis('off')
    
    def _create_no_data_chart(self, message: str) -> io.BytesIO:
        """Create a chart showing no data message"""
        fig, ax = plt.subplots(figsize=(8, 6), dpi=self.dpi)
        ax.text(0.5, 0.5, message, ha='center', va='center', transform=ax.transAxes,
                fontsize=16, style='italic', color='gray')
        ax.set_title('📊 No Data Available', fontsize=18, fontweight='bold', pad=20)
        ax.axis('off')
        plt.tight_layout()
        return self._save_chart_to_buffer(fig)
    
    def _save_chart_to_buffer(self, fig) -> io.BytesIO:
        """Save matplotlib figure to BytesIO buffer"""
        buffer = io.BytesIO()
        fig.savefig(buffer, format='png', dpi=self.dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        buffer.seek(0)
        plt.close(fig)  # Free memory
        return buffer
    
    def create_summary_image(self, metrics: TradingMetrics, date: datetime) -> io.BytesIO:
        """Create a summary image with key metrics"""
        # Create image with PIL for better text control
        width, height = 800, 600
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to load a nice font
            title_font = ImageFont.truetype("arial.ttf", 36)
            metric_font = ImageFont.truetype("arial.ttf", 24)
            value_font = ImageFont.truetype("arial.ttf", 28)
        except:
            # Fallback to default font
            title_font = ImageFont.load_default()
            metric_font = ImageFont.load_default()
            value_font = ImageFont.load_default()
        
        # Title
        title = f"Trading Summary - {date.strftime('%Y-%m-%d')}"
        draw.text((width//2, 50), title, font=title_font, fill='black', anchor='mm')
        
        # Metrics
        y_pos = 150
        spacing = 80
        
        metrics_data = [
            ("Total P&L", f"₹{metrics.total_pnl:,.2f}", '#2ecc71' if metrics.total_pnl >= 0 else '#e74c3c'),
            ("Total Trades", str(metrics.total_trades), '#3498db'),
            ("Win Rate", f"{metrics.win_rate:.1f}%", '#2ecc71' if metrics.win_rate >= 50 else '#e74c3c'),
            ("Profit Factor", f"{metrics.profit_factor:.2f}", '#2ecc71' if metrics.profit_factor >= 1 else '#e74c3c'),
            ("Max Drawdown", f"₹{metrics.max_drawdown:,.2f}", '#e74c3c'),
        ]
        
        for i, (label, value, color) in enumerate(metrics_data):
            y = y_pos + i * spacing
            draw.text((100, y), label, font=metric_font, fill='black')
            draw.text((width - 100, y), value, font=value_font, fill=color, anchor='rm')
        
        # Save to buffer
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        return buffer
