"""
Helper functions for trading operations
"""
import os
import math
import pytz
from datetime import datetime, time
from typing import List, <PERSON><PERSON>


def is_market_open() -> bool:
    """
    Check if the Indian stock market is currently open
    Enhanced with better timezone handling and detailed logging

    Returns:
        bool: True if market is open, False otherwise
    """
    import logging
    logger = logging.getLogger('MarketHours')

    # Get current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    now = datetime.now(ist)
    current_time = now.time()

    # Log current time for debugging (both print and logger)
    time_msg = f"🕐 Current IST time: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}"
    print(time_msg)
    logger.info(f"MARKET_HOURS_CHECK: {time_msg}")

    # Check for override environment variable (for testing) - with warning
    override_value = os.getenv("OVERRIDE_MARKET_OPEN")
    if override_value == "true":
        override_msg = "⚠️ WARNING: Market hours validation is OVERRIDDEN! This should only be used for testing."
        print(override_msg)
        print("⚠️ Remove OVERRIDE_MARKET_OPEN=true from .env file for production use.")
        logger.warning(f"MARKET_HOURS_OVERRIDE: {override_msg}")
        logger.warning("MARKET_HOURS_OVERRIDE: Market validation bypassed - TESTING MODE ACTIVE")
        return True

    # Log that no override is active
    logger.info(f"MARKET_HOURS_CHECK: No override detected (OVERRIDE_MARKET_OPEN={override_value})")

    # Check if it's a weekday (Monday to Friday)
    if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
        weekday_name = now.strftime('%A')
        weekend_msg = f"📅 Market is closed - Today is {weekday_name} (Weekend)"
        print(weekend_msg)
        logger.info(f"MARKET_HOURS_RESULT: {weekend_msg}")
        return False

    # Market hours: 9:15 AM to 3:30 PM IST
    market_open = time(9, 15)
    market_close = time(15, 30)

    is_open = market_open <= current_time < market_close

    # Detailed logging for market status
    current_minutes = current_time.hour * 60 + current_time.minute
    open_minutes = market_open.hour * 60 + market_open.minute
    close_minutes = market_close.hour * 60 + market_close.minute

    if is_open:
        open_msg = f"✅ Market is OPEN (Current: {current_time.strftime('%H:%M:%S')}, Hours: 09:15-15:30)"
        print(open_msg)
        logger.info(f"MARKET_HOURS_RESULT: {open_msg}")
        logger.info(f"MARKET_HOURS_DETAIL: Current minutes: {current_minutes}, Open: {open_minutes}, Close: {close_minutes}")
    else:
        closed_msg = f"❌ Market is CLOSED (Current: {current_time.strftime('%H:%M:%S')}, Hours: 09:15-15:30)"
        print(closed_msg)
        logger.info(f"MARKET_HOURS_RESULT: {closed_msg}")
        logger.info(f"MARKET_HOURS_DETAIL: Current minutes: {current_minutes}, Open: {open_minutes}, Close: {close_minutes}")

        if current_time < market_open:
            minutes_to_open = (datetime.combine(now.date(), market_open) - datetime.combine(now.date(), current_time)).seconds // 60
            wait_msg = f"⏰ Market opens in {minutes_to_open} minutes"
            print(wait_msg)
            logger.info(f"MARKET_HOURS_DETAIL: {wait_msg}")
        else:
            closed_day_msg = "📴 Market has closed for the day"
            print(closed_day_msg)
            logger.info(f"MARKET_HOURS_DETAIL: {closed_day_msg}")

    return is_open


def is_in_entry_time_window(timeframe: str = "15min") -> bool:
    """
    Check if current time is within the entry time window
    Enhanced with detailed logging

    Args:
        timeframe: Trading timeframe ("5min" or "15min")

    Returns:
        bool: True if within entry window, False otherwise
    """
    import logging
    logger = logging.getLogger('EntryTimeWindow')

    # Check for override environment variable (for testing)
    override_value = os.getenv("OVERRIDE_ENTRY_WINDOW")
    if override_value == "true":
        override_msg = f"⚠️ WARNING: Entry time window validation is OVERRIDDEN for {timeframe} strategy!"
        print(override_msg)
        logger.warning(f"ENTRY_WINDOW_OVERRIDE: {override_msg}")
        return True

    # Log that no override is active
    logger.info(f"ENTRY_WINDOW_CHECK: No override detected (OVERRIDE_ENTRY_WINDOW={override_value})")

    # Get current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    now = datetime.now(ist)
    current_time = now.time()

    # Entry windows based on timeframe
    if timeframe == "5min":
        # 5-min strategy: 9:15 AM to 10:05 AM
        entry_start = time(9, 15)
        entry_end = time(10, 5)
    else:  # 15min
        # 15-min strategy: 9:15 AM to 10:15 AM
        entry_start = time(9, 15)
        entry_end = time(10, 15)

    is_in_window = entry_start <= current_time <= entry_end

    # Detailed logging for entry window status
    current_minutes = current_time.hour * 60 + current_time.minute
    start_minutes = entry_start.hour * 60 + entry_start.minute
    end_minutes = entry_end.hour * 60 + entry_end.minute

    if is_in_window:
        window_msg = f"✅ ENTRY WINDOW OPEN for {timeframe} strategy (Current: {current_time.strftime('%H:%M:%S')}, Window: {entry_start.strftime('%H:%M')}-{entry_end.strftime('%H:%M')})"
        print(window_msg)
        logger.info(f"ENTRY_WINDOW_RESULT: {window_msg}")
        logger.info(f"ENTRY_WINDOW_DETAIL: Current minutes: {current_minutes}, Start: {start_minutes}, End: {end_minutes}")
    else:
        window_msg = f"❌ ENTRY WINDOW CLOSED for {timeframe} strategy (Current: {current_time.strftime('%H:%M:%S')}, Window: {entry_start.strftime('%H:%M')}-{entry_end.strftime('%H:%M')})"
        print(window_msg)
        logger.info(f"ENTRY_WINDOW_RESULT: {window_msg}")
        logger.info(f"ENTRY_WINDOW_DETAIL: Current minutes: {current_minutes}, Start: {start_minutes}, End: {end_minutes}")

        if current_time < entry_start:
            minutes_to_start = (datetime.combine(now.date(), entry_start) - datetime.combine(now.date(), current_time)).seconds // 60
            wait_msg = f"⏰ Entry window opens in {minutes_to_start} minutes"
            print(wait_msg)
            logger.info(f"ENTRY_WINDOW_DETAIL: {wait_msg}")
        else:
            closed_msg = f"📴 Entry window has closed for {timeframe} strategy"
            print(closed_msg)
            logger.info(f"ENTRY_WINDOW_DETAIL: {closed_msg}")

    return is_in_window


def is_end_of_trading_day() -> bool:
    """
    Check if it's near the end of the trading day (3:25 PM - 3:30 PM IST)

    Returns:
        bool: True if near end of day, False otherwise
    """
    # Get current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    now = datetime.now(ist)
    current_time = now.time()

    # End of day window: 3:25 PM to 3:30 PM
    end_start = time(15, 25)
    end_close = time(15, 30)

    return end_start <= current_time < end_close


def calculate_ema(prices: List[float], period: int) -> List[float]:
    """
    Calculate Exponential Moving Average with improved accuracy

    Args:
        prices: List of prices
        period: EMA period

    Returns:
        List of EMA values
    """
    if len(prices) < period:
        return []

    # Calculate multiplier
    multiplier = 2.0 / (period + 1)

    # Initialize EMA with SMA for the first period
    ema_values = []
    sma = sum(prices[:period]) / period
    ema_values.append(sma)

    # Calculate EMA for the rest of the prices using the correct formula:
    # EMA = Current Price * multiplier + Previous EMA * (1 - multiplier)
    for i in range(period, len(prices)):
        ema = prices[i] * multiplier + ema_values[-1] * (1 - multiplier)
        ema_values.append(ema)

    return ema_values


def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
    """
    Calculate Relative Strength Index

    Args:
        prices: List of prices
        period: RSI period (default 14)

    Returns:
        List of RSI values
    """
    if len(prices) < period + 1:
        return []

    # Calculate price changes
    changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]

    rsi_values = []

    for i in range(period - 1, len(changes)):
        gains = []
        losses = []

        # Get gains and losses for the period
        for j in range(i - period + 1, i + 1):
            if changes[j] > 0:
                gains.append(changes[j])
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(changes[j]))

        # Calculate average gain and loss
        avg_gain = sum(gains) / period
        avg_loss = sum(losses) / period

        # Calculate RSI
        if avg_loss == 0:
            rsi = 100.0
        else:
            rs = avg_gain / avg_loss
            rsi = 100.0 - (100.0 / (1.0 + rs))

        rsi_values.append(rsi)

    return rsi_values


def calculate_quantity(entry_price: float, stop_loss: float, base_amount: float = 800.0, margin_multiplier: int = 4) -> int:
    """
    Calculate quantity using the formula: base_amount/(entry_price - stop_loss) * margin_multiplier

    Args:
        entry_price: Entry price for the trade
        stop_loss: Stop loss price
        base_amount: Base amount for calculation (default 800)
        margin_multiplier: Margin multiplier (default 4x)

    Returns:
        int: Calculated quantity
    """
    risk = abs(entry_price - stop_loss)
    if risk == 0:
        return 1

    base_quantity = int(math.floor(base_amount / risk))
    quantity = base_quantity * margin_multiplier



    return max(quantity, 1)  # Ensure minimum quantity of 1


def format_price(price: float, decimals: int = 2) -> float:
    """
    Format price to specified decimal places

    Args:
        price: Price to format
        decimals: Number of decimal places

    Returns:
        float: Formatted price
    """
    return round(price, decimals)


def find_support_resistance_levels(highs: List[float], lows: List[float], volumes: List[float] = None, tolerance: float = 0.005) -> Tuple[List[float], List[float]]:
    """
    Find support and resistance levels from price data with volume consideration

    Args:
        highs: List of high prices
        lows: List of low prices
        volumes: List of volumes (optional)
        tolerance: Tolerance for grouping nearby levels (default 0.5%)

    Returns:
        Tuple of (support_levels, resistance_levels)
    """
    # Use default volumes if not provided
    if volumes is None:
        volumes = [1.0] * len(highs)
    
    # Ensure all lists have the same length
    min_length = min(len(highs), len(lows), len(volumes))
    highs = highs[:min_length]
    lows = lows[:min_length]
    volumes = volumes[:min_length]

    # Find potential support levels (local lows) with volume weighting
    support_candidates = []
    for i in range(1, len(lows) - 1):
        if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
            # Store level with its volume for weighting
            support_candidates.append((lows[i], volumes[i]))

    # Find potential resistance levels (local highs) with volume weighting
    resistance_candidates = []
    for i in range(1, len(highs) - 1):
        if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
            # Store level with its volume for weighting
            resistance_candidates.append((highs[i], volumes[i]))

    # Group nearby levels with volume weighting
    grouped_supports = _group_levels_with_volume(support_candidates, tolerance)
    grouped_resistances = _group_levels_with_volume(resistance_candidates, tolerance)

    return grouped_supports, grouped_resistances

def _group_levels_with_volume(level_volume_pairs: List[Tuple[float, float]], tolerance: float) -> List[float]:
    """
    Group nearby price levels within tolerance, weighted by volume

    Args:
        level_volume_pairs: List of (price_level, volume) pairs
        tolerance: Tolerance for grouping

    Returns:
        List of grouped levels
    """
    if not level_volume_pairs:
        return []

    # Sort levels by price
    sorted_pairs = sorted(level_volume_pairs, key=lambda x: x[0])
    grouped = []
    current_group = [sorted_pairs[0]]

    for i in range(1, len(sorted_pairs)):
        current_level, _ = sorted_pairs[i]
        first_level, _ = current_group[0]
        
        # Check if current level is within tolerance of the group
        if (current_level - first_level) / first_level <= tolerance:
            current_group.append(sorted_pairs[i])
        else:
            # Calculate volume-weighted average of current group
            total_volume = sum(volume for _, volume in current_group)
            if total_volume > 0:
                weighted_avg = sum(level * volume for level, volume in current_group) / total_volume
            else:
                weighted_avg = sum(level for level, _ in current_group) / len(current_group)
                
            grouped.append(weighted_avg)
            current_group = [sorted_pairs[i]]

    # Add the last group
    if current_group:
        total_volume = sum(volume for _, volume in current_group)
        if total_volume > 0:
            weighted_avg = sum(level * volume for level, volume in current_group) / total_volume
        else:
            weighted_avg = sum(level for level, _ in current_group) / len(current_group)
            
        grouped.append(weighted_avg)

    return grouped


def _group_levels(levels: List[float], tolerance: float) -> List[float]:
    """
    Group nearby price levels within tolerance

    Args:
        levels: List of price levels
        tolerance: Tolerance for grouping

    Returns:
        List of grouped levels
    """
    if not levels:
        return []

    # Sort levels
    sorted_levels = sorted(levels)
    grouped = []
    current_group = [sorted_levels[0]]

    for i in range(1, len(sorted_levels)):
        # Check if current level is within tolerance of the group
        if (sorted_levels[i] - current_group[0]) / current_group[0] <= tolerance:
            current_group.append(sorted_levels[i])
        else:
            # Add average of current group and start new group
            grouped.append(sum(current_group) / len(current_group))
            current_group = [sorted_levels[i]]

    # Add the last group
    if current_group:
        grouped.append(sum(current_group) / len(current_group))

    return grouped


def calculate_average_volume(volumes: List[float], period: int = 10) -> List[float]:
    """
    Calculate average volume over a period

    Args:
        volumes: List of volume values
        period: Period for average calculation

    Returns:
        List of average volume values
    """
    if len(volumes) < period:
        return []

    avg_volumes = []
    for i in range(period - 1, len(volumes)):
        avg_vol = sum(volumes[i - period + 1:i + 1]) / period
        avg_volumes.append(avg_vol)

    return avg_volumes
