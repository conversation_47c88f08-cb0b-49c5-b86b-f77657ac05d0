"""
Configuration management for the trading system
"""
import os
from dataclasses import dataclass
from typing import Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


@dataclass
class SmartAPIConfig:
    """SmartAPI configuration"""
    api_key: str
    username: str
    password: str
    totp_token: str

    @classmethod
    def from_env(cls) -> 'SmartAPIConfig':
        """Create config from environment variables"""
        return cls(
            api_key=os.getenv('SMARTAPI_API_KEY', ''),
            username=os.getenv('SMARTAPI_USERNAME', ''),
            password=os.getenv('SMARTAPI_PASSWORD', ''),
            totp_token=os.getenv('SMARTAPI_TOTP_TOKEN', '')
        )

    def validate(self) -> bool:
        """Validate that all required fields are present"""
        return all([self.api_key, self.username, self.password, self.totp_token])


@dataclass
class TradingConfig:
    """Trading configuration"""
    max_trades_per_day: int = 3  # Total trades per day across ALL strategies
    max_risk_per_trade: float = 800.0
    margin_multiplier: int = 4
    paper_trading: bool = False  # Production mode - no paper trading
    initial_balance: float = 80000.0  # Not used in production - real balance from SmartAPI
    square_off_time: str = "15:12"  # Time to square off all positions (before Angel One's 15:15 auto square-off)

    # Risk management
    risk_reward_ratio: float = 3.0
    stop_loss_buffer: float = 0.002  # 0.2%

    # Intraday-specific stop loss settings
    intraday_stop_loss_percent: float = 0.008  # 0.8% for intraday (tighter than swing trading)
    max_intraday_stop_loss_percent: float = 0.012  # 1.2% maximum for volatile stocks
    atr_stop_loss_multiplier: float = 1.5  # ATR multiplier for dynamic stop loss (reduced from 2.0)
    use_atr_based_stops: bool = True  # Enable ATR-based dynamic stop losses

    # Technical indicators
    ema_fast_period: int = 9
    ema_slow_period: int = 20
    ema_trend_period: int = 50
    rsi_period: int = 14
    volume_period: int = 10

    # Entry conditions
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0
    rsi_neutral: float = 50.0

    @classmethod
    def from_env(cls) -> 'TradingConfig':
        """Create config from environment variables"""
        # Production mode - always use real trading
        paper_trading_env = os.getenv('PAPER_TRADING', 'false').lower()
        real_trading_env = os.getenv('REAL_TRADING', 'true').lower()

        # Force real trading for production
        if real_trading_env == 'true':
            paper_trading = False
        else:
            paper_trading = False  # Default to real trading in production

        return cls(
            max_trades_per_day=int(os.getenv('MAX_TRADES_PER_DAY', '3')),
            max_risk_per_trade=float(os.getenv('MAX_RISK_PER_TRADE', '800.0')),
            margin_multiplier=int(os.getenv('MARGIN_MULTIPLIER', '4')),
            paper_trading=paper_trading,
            initial_balance=float(os.getenv('INITIAL_BALANCE', '80000.0')),
            square_off_time=os.getenv('SQUARE_OFF_TIME', '15:12'),
            risk_reward_ratio=float(os.getenv('RISK_REWARD_RATIO', '3.0')),
            stop_loss_buffer=float(os.getenv('STOP_LOSS_BUFFER', '0.002')),
            intraday_stop_loss_percent=float(os.getenv('INTRADAY_STOP_LOSS_PERCENT', '0.008')),
            max_intraday_stop_loss_percent=float(os.getenv('MAX_INTRADAY_STOP_LOSS_PERCENT', '0.012')),
            atr_stop_loss_multiplier=float(os.getenv('ATR_STOP_LOSS_MULTIPLIER', '1.5')),
            use_atr_based_stops=os.getenv('USE_ATR_BASED_STOPS', 'true').lower() == 'true',
            ema_fast_period=int(os.getenv('EMA_FAST_PERIOD', '9')),
            ema_slow_period=int(os.getenv('EMA_SLOW_PERIOD', '20')),
            ema_trend_period=int(os.getenv('EMA_TREND_PERIOD', '50')),
            rsi_period=int(os.getenv('RSI_PERIOD', '14')),
            volume_period=int(os.getenv('VOLUME_PERIOD', '10')),
            rsi_oversold=float(os.getenv('RSI_OVERSOLD', '30.0')),
            rsi_overbought=float(os.getenv('RSI_OVERBOUGHT', '70.0')),
            rsi_neutral=float(os.getenv('RSI_NEUTRAL', '50.0'))
        )


@dataclass
class MongoDBConfig:
    """MongoDB configuration"""
    connection_string: str
    database_name: str

    @classmethod
    def from_env(cls) -> 'MongoDBConfig':
        """Create config from environment variables"""
        return cls(
            connection_string=os.getenv('MONGODB_CONNECTION_STRING', 'mongodb://localhost:27017/'),
            database_name=os.getenv('MONGODB_DATABASE_NAME', 'trading_db')
        )


@dataclass
class LoggingConfig:
    """Logging configuration"""
    log_level: str = 'INFO'
    log_file: Optional[str] = None

    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create config from environment variables"""
        return cls(
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            log_file=os.getenv('LOG_FILE')
        )


@dataclass
class Config:
    """Main configuration class"""
    smartapi: SmartAPIConfig
    trading: TradingConfig
    mongodb: MongoDBConfig
    logging: LoggingConfig

    @classmethod
    def from_env(cls) -> 'Config':
        """Create complete config from environment variables"""
        return cls(
            smartapi=SmartAPIConfig.from_env(),
            trading=TradingConfig.from_env(),
            mongodb=MongoDBConfig.from_env(),
            logging=LoggingConfig.from_env()
        )

    def validate(self) -> bool:
        """Validate configuration with enhanced real trading checks"""
        # Basic trading parameter validation
        if self.trading.max_trades_per_day <= 0:
            return False

        if self.trading.max_risk_per_trade <= 0:
            return False

        # Enhanced validation for real trading
        if not self.trading.paper_trading:
            # Real trading requires complete SmartAPI configuration
            if not self.smartapi.validate():
                print("❌ REAL TRADING ERROR: SmartAPI configuration is incomplete!")
                print("💡 Required environment variables:")
                print("   - SMARTAPI_API_KEY")
                print("   - SMARTAPI_USERNAME")
                print("   - SMARTAPI_PASSWORD")
                print("   - SMARTAPI_TOTP_TOKEN")
                print("🔧 Please check your .env file or switch to paper trading (PAPER_TRADING=true)")
                return False

            # Additional safety checks for real trading
            print("⚠️  REAL TRADING MODE ENABLED!")
            print("💰 This will use REAL MONEY from your Angel One account")
            print("📊 Make sure you have sufficient balance and understand the risks")

        else:
            # Paper trading mode - SmartAPI is optional
            if not self.smartapi.validate():
                print("📝 Paper Trading Mode: SmartAPI not configured (using simulated data)")
            else:
                print("📝 Paper Trading Mode: SmartAPI configured (using real market data)")

        return True


# Global config instance
config = Config.from_env()
