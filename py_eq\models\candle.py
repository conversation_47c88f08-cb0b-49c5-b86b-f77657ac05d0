"""
Candle model for representing price data with technical indicators
"""
from datetime import datetime
from dataclasses import dataclass
from typing import Optional


@dataclass
class Candle:
    """Represents a price candle with technical indicators"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float

    # Technical indicators
    ema9: Optional[float] = None
    ema20: Optional[float] = None
    ema50: Optional[float] = None
    rsi: Optional[float] = None
    avg_volume: Optional[float] = None

    def __post_init__(self):
        """Validate candle data"""
        if self.high < max(self.open, self.close):
            raise ValueError("High price cannot be less than open or close")
        if self.low > min(self.open, self.close):
            raise ValueError("Low price cannot be greater than open or close")
        if self.volume < 0:
            raise ValueError("Volume cannot be negative")

    @property
    def is_bullish(self) -> bool:
        """Returns True if the candle is bullish (close > open)"""
        return self.close > self.open

    @property
    def is_bearish(self) -> bool:
        """Returns True if the candle is bearish (close < open)"""
        return self.close < self.open

    @property
    def body_size(self) -> float:
        """Returns the size of the candle body"""
        return abs(self.close - self.open)

    @property
    def upper_shadow(self) -> float:
        """Returns the size of the upper shadow"""
        return self.high - max(self.open, self.close)

    @property
    def lower_shadow(self) -> float:
        """Returns the size of the lower shadow"""
        return min(self.open, self.close) - self.low

    def to_dict(self) -> dict:
        """Convert candle to dictionary"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'ema9': self.ema9,
            'ema20': self.ema20,
            'ema50': self.ema50,
            'rsi': self.rsi,
            'avg_volume': self.avg_volume
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Candle':
        """Create candle from dictionary"""
        if isinstance(data['timestamp'], str):
            # Parse ISO string and ensure timezone-naive
            timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
            if timestamp.tzinfo is not None:
                timestamp = timestamp.replace(tzinfo=None)
        else:
            timestamp = data['timestamp']
            # Ensure timezone-naive
            if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo is not None:
                timestamp = timestamp.replace(tzinfo=None)

        return cls(
            timestamp=timestamp,
            open=data['open'],
            high=data['high'],
            low=data['low'],
            close=data['close'],
            volume=data['volume'],
            ema9=data.get('ema9'),
            ema20=data.get('ema20'),
            ema50=data.get('ema50'),
            rsi=data.get('rsi'),
            avg_volume=data.get('avg_volume')
        )
