#!/usr/bin/env python3
"""
Telegram Bot Configuration Setup Script

This script helps you configure the Telegram bot for trading reports.
It will guide you through the setup process step by step.
"""

import os
import sys
import requests
from datetime import datetime

def print_header():
    """Print setup header"""
    print("=" * 80)
    print("🤖 TELEGRAM TRADING BOT CONFIGURATION SETUP")
    print("=" * 80)
    print("This script will help you configure your Telegram bot for trading reports.")
    print("You'll need to create a Telegram bot and get your chat ID.")
    print("=" * 80)

def create_telegram_bot():
    """Guide user through creating a Telegram bot"""
    print("\n📱 STEP 1: CREATE TELEGRAM BOT")
    print("-" * 40)
    print("1. Open Telegram and search for '@BotFather'")
    print("2. Start a chat with Bo<PERSON><PERSON><PERSON>")
    print("3. Send the command: /newbot")
    print("4. Follow the instructions to create your bot")
    print("5. Copy the bot token (looks like: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz)")
    print()
    
    bot_token = input("Enter your bot token: ").strip()
    
    if not bot_token or ':' not in bot_token:
        print("❌ Invalid bot token format. Please try again.")
        return None
    
    # Test bot token
    try:
        response = requests.get(f"https://api.telegram.org/bot{bot_token}/getMe", timeout=10)
        if response.status_code == 200:
            bot_info = response.json()
            if bot_info.get('ok'):
                print(f"✅ Bot token valid! Bot name: {bot_info['result']['first_name']}")
                return bot_token
            else:
                print("❌ Invalid bot token. Please check and try again.")
                return None
        else:
            print("❌ Failed to validate bot token. Please check and try again.")
            return None
    except Exception as e:
        print(f"❌ Error validating bot token: {e}")
        return None

def get_chat_id():
    """Guide user through getting their chat ID"""
    print("\n💬 STEP 2: GET YOUR CHAT ID")
    print("-" * 40)
    print("1. Open Telegram and search for '@userinfobot'")
    print("2. Start a chat with userinfobot")
    print("3. Send any message to the bot")
    print("4. Copy your chat ID (looks like: 123456789)")
    print()
    print("Alternative method:")
    print("1. Send a message to your bot (the one you just created)")
    print("2. Visit: https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates")
    print("3. Look for 'chat':{'id': YOUR_CHAT_ID}")
    print()
    
    chat_id = input("Enter your chat ID: ").strip()
    
    if not chat_id or not chat_id.isdigit():
        print("❌ Invalid chat ID format. Should be a number.")
        return None
    
    return chat_id

def get_authorized_users():
    """Get list of authorized users"""
    print("\n👥 STEP 3: AUTHORIZED USERS (OPTIONAL)")
    print("-" * 40)
    print("Enter chat IDs of users who can use the bot (comma-separated)")
    print("Leave empty to use only your chat ID")
    print()
    
    users_input = input("Authorized user IDs (optional): ").strip()
    
    if users_input:
        try:
            users = [user.strip() for user in users_input.split(',') if user.strip()]
            # Validate all are numbers
            for user in users:
                if not user.isdigit():
                    print(f"❌ Invalid user ID: {user}")
                    return []
            return users
        except:
            print("❌ Invalid format. Please use comma-separated numbers.")
            return []
    
    return []

def get_admin_users():
    """Get list of admin users"""
    print("\n👑 STEP 4: ADMIN USERS (OPTIONAL)")
    print("-" * 40)
    print("Enter chat IDs of admin users (comma-separated)")
    print("Admins can access bot settings and advanced features")
    print("Leave empty to use only your chat ID as admin")
    print()
    
    admins_input = input("Admin user IDs (optional): ").strip()
    
    if admins_input:
        try:
            admins = [admin.strip() for admin in admins_input.split(',') if admin.strip()]
            # Validate all are numbers
            for admin in admins:
                if not admin.isdigit():
                    print(f"❌ Invalid admin ID: {admin}")
                    return []
            return admins
        except:
            print("❌ Invalid format. Please use comma-separated numbers.")
            return []
    
    return []

def configure_settings():
    """Configure bot settings"""
    print("\n⚙️ STEP 5: BOT SETTINGS")
    print("-" * 40)
    
    settings = {}
    
    # Daily reports
    daily = input("Enable daily reports? (y/n) [y]: ").strip().lower()
    settings['daily_reports'] = daily != 'n'
    
    # Weekly reports
    weekly = input("Enable weekly reports? (y/n) [y]: ").strip().lower()
    settings['weekly_reports'] = weekly != 'n'
    
    # Monthly reports
    monthly = input("Enable monthly reports? (y/n) [y]: ").strip().lower()
    settings['monthly_reports'] = monthly != 'n'
    
    # Trade alerts
    alerts = input("Enable trade alerts? (y/n) [y]: ").strip().lower()
    settings['trade_alerts'] = alerts != 'n'
    
    # Balance updates
    balance = input("Enable balance alerts? (y/n) [y]: ").strip().lower()
    settings['balance_updates'] = balance != 'n'
    
    return settings

def create_env_file(bot_token, chat_id, authorized_users, admin_users, settings):
    """Create .env file with configuration"""
    env_content = f"""# Telegram Bot Configuration
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# Bot credentials
TELEGRAM_BOT_TOKEN={bot_token}
TELEGRAM_CHAT_ID={chat_id}

# Authorized users (comma-separated chat IDs)
TELEGRAM_AUTHORIZED_USERS={','.join(authorized_users) if authorized_users else chat_id}

# Admin users (comma-separated chat IDs)
TELEGRAM_ADMIN_USERS={','.join(admin_users) if admin_users else chat_id}

# Bot settings
TELEGRAM_DAILY_REPORTS={'true' if settings['daily_reports'] else 'false'}
TELEGRAM_WEEKLY_REPORTS={'true' if settings['weekly_reports'] else 'false'}
TELEGRAM_MONTHLY_REPORTS={'true' if settings['monthly_reports'] else 'false'}
TELEGRAM_TRADE_ALERTS={'true' if settings['trade_alerts'] else 'false'}
TELEGRAM_BALANCE_UPDATES={'true' if settings['balance_updates'] else 'false'}

# Report timing (24-hour format)
TELEGRAM_DAILY_REPORT_TIME=16:00
TELEGRAM_WEEKLY_REPORT_TIME=17:00
TELEGRAM_MONTHLY_REPORT_TIME=18:00

# Alert thresholds
TELEGRAM_MAX_DAILY_LOSS=5000
TELEGRAM_MAX_DRAWDOWN=10000
TELEGRAM_MIN_BALANCE=300000
"""
    
    env_file_path = os.path.join(os.path.dirname(__file__), '.env.telegram')
    
    try:
        with open(env_file_path, 'w') as f:
            f.write(env_content)
        
        print(f"\n✅ Configuration saved to: {env_file_path}")
        return env_file_path
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        return None

def test_bot_connection(bot_token, chat_id):
    """Test bot connection by sending a test message"""
    print("\n🧪 STEP 6: TESTING BOT CONNECTION")
    print("-" * 40)
    
    test_message = f"""🧪 **Bot Configuration Test**

✅ Your Telegram bot is properly configured!
🕐 Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This is a test message to verify everything is working correctly.

You can now use the following commands:
• /start - Main menu
• /status - Current trading status
• /daily - Daily report
• /help - Show all commands

Happy trading! 📈🤖"""
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': test_message,
            'parse_mode': 'Markdown'
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('ok'):
                print("✅ Test message sent successfully!")
                print("Check your Telegram to see the test message.")
                return True
            else:
                print(f"❌ Failed to send test message: {result.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test message: {e}")
        return False

def show_next_steps():
    """Show next steps after configuration"""
    print("\n🎉 CONFIGURATION COMPLETE!")
    print("=" * 80)
    print("Your Telegram bot is now configured and ready to use!")
    print()
    print("📋 NEXT STEPS:")
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("2. Test the bot:")
    print("   python telegram_bot_launcher.py --test")
    print()
    print("3. Start the bot:")
    print("   python telegram_bot_launcher.py")
    print()
    print("4. Or integrate with trading system:")
    print("   python main.py")
    print()
    print("📱 BOT COMMANDS:")
    print("• /start - Main menu with interactive buttons")
    print("• /status - Live trading status and P&L")
    print("• /daily - Today's trading report with charts")
    print("• /weekly - Weekly performance analysis")
    print("• /monthly - Monthly risk metrics")
    print("• /trades - Recent trades list")
    print("• /balance - Account balance and risk info")
    print("• /strategies - Strategy performance breakdown")
    print("• /alerts - Configure trading alerts")
    print("• /help - Show all available commands")
    print()
    print("🔔 The bot will automatically send:")
    print("• Real-time order placement notifications")
    print("• Daily reports after market close")
    print("• Weekly reports on Fridays")
    print("• Monthly reports at month-end")
    print("• Balance and risk alerts")
    print()
    print("Happy trading! 📈🤖")
    print("=" * 80)

def main():
    """Main setup function"""
    print_header()
    
    # Step 1: Create bot and get token
    bot_token = create_telegram_bot()
    if not bot_token:
        print("❌ Setup failed. Please try again.")
        return
    
    # Step 2: Get chat ID
    chat_id = get_chat_id()
    if not chat_id:
        print("❌ Setup failed. Please try again.")
        return
    
    # Step 3: Get authorized users
    authorized_users = get_authorized_users()
    if not authorized_users:
        authorized_users = [chat_id]
    
    # Step 4: Get admin users
    admin_users = get_admin_users()
    if not admin_users:
        admin_users = [chat_id]
    
    # Step 5: Configure settings
    settings = configure_settings()
    
    # Create .env file
    env_file = create_env_file(bot_token, chat_id, authorized_users, admin_users, settings)
    if not env_file:
        print("❌ Setup failed. Please try again.")
        return
    
    # Test bot connection
    if test_bot_connection(bot_token, chat_id):
        show_next_steps()
    else:
        print("⚠️ Configuration saved but test message failed.")
        print("Please check your bot token and chat ID.")

if __name__ == "__main__":
    main()
