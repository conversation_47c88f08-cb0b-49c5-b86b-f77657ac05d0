"""
Daily Trade Tracker Service - Persistent trade tracking across system restarts
Prevents exceeding daily trade limits and ensures proper order state management
"""

import json
import os
import logging
from datetime import datetime, date
from typing import Dict, Set, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum


class TradeStatus(Enum):
    """Trade status enumeration"""
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"


@dataclass
class TradeRecord:
    """Individual trade record"""
    trade_id: str
    symbol: str
    timestamp: str
    status: TradeStatus
    order_id: Optional[str] = None
    quantity: int = 0
    price: float = 0.0
    strategy: str = "UNKNOWN"
    notes: str = ""

    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['status'] = self.status.value
        return data

    @classmethod
    def from_dict(cls, data: Dict) -> 'TradeRecord':
        """Create from dictionary"""
        data['status'] = TradeStatus(data['status'])
        return cls(**data)


class DailyTradeTracker:
    """
    Persistent daily trade tracking service
    
    Features:
    - Persistent storage across system restarts
    - Daily trade limit enforcement (max 3 trades)
    - Symbol-based duplicate prevention
    - Order state tracking
    - Automatic daily reset
    """

    def __init__(self, logger: logging.Logger, data_dir: str = "data", max_trades_per_day: int = 3):
        self.logger = logger
        self.data_dir = data_dir
        self.max_trades_per_day = max_trades_per_day
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Current state
        self.current_date = date.today()
        self.trades_today: Dict[str, TradeRecord] = {}  # trade_id -> TradeRecord
        self.traded_symbols: Set[str] = set()  # Symbols traded today
        self.daily_trade_count = 0
        
        # Load existing state
        self._load_daily_state()
        
        # Check if we need to reset for new day
        self._check_daily_reset()
        
        self.logger.info(f"📊 Daily Trade Tracker initialized: {self.daily_trade_count}/{self.max_trades_per_day} trades used today")
        if self.traded_symbols:
            self.logger.info(f"📈 Symbols already traded today: {', '.join(sorted(self.traded_symbols))}")

    def _get_state_file_path(self) -> str:
        """Get the path to the daily state file"""
        return os.path.join(self.data_dir, f"daily_trades_{self.current_date.isoformat()}.json")

    def _load_daily_state(self):
        """Load daily state from file"""
        state_file = self._get_state_file_path()
        
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)
                
                # Load trade records
                trades_data = state_data.get('trades', {})
                for trade_id, trade_data in trades_data.items():
                    try:
                        trade_record = TradeRecord.from_dict(trade_data)
                        self.trades_today[trade_id] = trade_record
                        
                        # Add to traded symbols if trade was executed
                        if trade_record.status == TradeStatus.EXECUTED:
                            self.traded_symbols.add(trade_record.symbol)
                    except Exception as e:
                        self.logger.error(f"Error loading trade record {trade_id}: {e}")
                
                # Update counters
                self.daily_trade_count = len([t for t in self.trades_today.values() if t.status == TradeStatus.EXECUTED])
                
                self.logger.info(f"📄 Loaded daily state: {len(self.trades_today)} total records, {self.daily_trade_count} executed trades")
                
            except Exception as e:
                self.logger.error(f"Error loading daily state: {e}")
                self._initialize_empty_state()
        else:
            self._initialize_empty_state()

    def _initialize_empty_state(self):
        """Initialize empty state for new day"""
        self.trades_today = {}
        self.traded_symbols = set()
        self.daily_trade_count = 0
        self.logger.info("📅 Initialized empty state for new trading day")

    def _check_daily_reset(self):
        """Check if we need to reset for a new day"""
        today = date.today()
        
        if today != self.current_date:
            self.logger.info(f"🔄 New trading day detected: {self.current_date} → {today}")
            
            # Archive previous day's data
            self._archive_previous_day()
            
            # Reset for new day
            self.current_date = today
            self._initialize_empty_state()
            self._save_daily_state()

    def _archive_previous_day(self):
        """Archive previous day's data"""
        try:
            old_state_file = self._get_state_file_path()
            if os.path.exists(old_state_file):
                archive_dir = os.path.join(self.data_dir, "archive")
                os.makedirs(archive_dir, exist_ok=True)
                
                archive_file = os.path.join(archive_dir, f"trades_{self.current_date.isoformat()}.json")
                os.rename(old_state_file, archive_file)
                
                self.logger.info(f"📦 Archived previous day's trades to {archive_file}")
        except Exception as e:
            self.logger.error(f"Error archiving previous day's data: {e}")

    def _save_daily_state(self):
        """Save current state to file"""
        try:
            state_data = {
                'date': self.current_date.isoformat(),
                'daily_trade_count': self.daily_trade_count,
                'max_trades_per_day': self.max_trades_per_day,
                'traded_symbols': list(self.traded_symbols),
                'trades': {trade_id: trade.to_dict() for trade_id, trade in self.trades_today.items()},
                'last_updated': datetime.now().isoformat()
            }
            
            state_file = self._get_state_file_path()
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Error saving daily state: {e}")

    def can_place_trade(self, symbol: str) -> Tuple[bool, str]:
        """
        Check if a trade can be placed for the given symbol
        
        Returns:
            Tuple[bool, str]: (can_trade, reason)
        """
        # Check daily limit
        if self.daily_trade_count >= self.max_trades_per_day:
            return False, f"Daily trade limit reached: {self.daily_trade_count}/{self.max_trades_per_day}"
        
        # Check if symbol already traded today
        if symbol in self.traded_symbols:
            return False, f"Symbol {symbol} already traded today"
        
        # Check for pending trades in the same symbol
        pending_trades = [t for t in self.trades_today.values() 
                         if t.symbol == symbol and t.status == TradeStatus.PENDING]
        if pending_trades:
            return False, f"Pending trade exists for {symbol}"
        
        return True, "Trade allowed"

    def record_trade_attempt(self, symbol: str, strategy: str = "UNKNOWN") -> str:
        """
        Record a trade attempt (before order placement)
        
        Returns:
            str: Trade ID for tracking
        """
        # Generate unique trade ID
        trade_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        # Create trade record
        trade_record = TradeRecord(
            trade_id=trade_id,
            symbol=symbol,
            timestamp=datetime.now().isoformat(),
            status=TradeStatus.PENDING,
            strategy=strategy,
            notes="Trade attempt recorded"
        )
        
        # Store the record
        self.trades_today[trade_id] = trade_record
        
        # Save state
        self._save_daily_state()
        
        self.logger.info(f"📝 Trade attempt recorded: {trade_id} for {symbol}")
        return trade_id

    def update_trade_status(self, trade_id: str, status: TradeStatus, order_id: Optional[str] = None, 
                           quantity: int = 0, price: float = 0.0, notes: str = "") -> bool:
        """
        Update trade status after order placement/execution
        
        Returns:
            bool: Success status
        """
        if trade_id not in self.trades_today:
            self.logger.error(f"Trade ID {trade_id} not found")
            return False
        
        trade_record = self.trades_today[trade_id]
        old_status = trade_record.status
        
        # Update trade record
        trade_record.status = status
        if order_id:
            trade_record.order_id = order_id
        if quantity > 0:
            trade_record.quantity = quantity
        if price > 0:
            trade_record.price = price
        if notes:
            trade_record.notes = notes
        
        # Update counters if trade was executed
        if status == TradeStatus.EXECUTED and old_status != TradeStatus.EXECUTED:
            self.daily_trade_count += 1
            self.traded_symbols.add(trade_record.symbol)
            
            self.logger.info(f"✅ Trade executed: {trade_record.symbol} (Count: {self.daily_trade_count}/{self.max_trades_per_day})")
        
        # Save state
        self._save_daily_state()
        
        self.logger.info(f"📊 Trade status updated: {trade_id} {old_status.value} → {status.value}")
        return True

    def get_daily_summary(self) -> Dict:
        """Get summary of today's trading activity"""
        executed_trades = [t for t in self.trades_today.values() if t.status == TradeStatus.EXECUTED]
        pending_trades = [t for t in self.trades_today.values() if t.status == TradeStatus.PENDING]
        rejected_trades = [t for t in self.trades_today.values() if t.status == TradeStatus.REJECTED]

        return {
            'date': self.current_date.isoformat(),
            'total_trades': len(self.trades_today),
            'executed_trades': len(executed_trades),
            'pending_trades': len(pending_trades),
            'rejected_trades': len(rejected_trades),
            'daily_trade_count': self.daily_trade_count,
            'max_trades_per_day': self.max_trades_per_day,
            'remaining_trades': max(0, self.max_trades_per_day - self.daily_trade_count),
            'traded_symbols': list(self.traded_symbols),
            'can_trade_more': self.daily_trade_count < self.max_trades_per_day
        }

    def get_trades_by_status(self, status: TradeStatus) -> List[TradeRecord]:
        """Get all trades with specific status"""
        return [t for t in self.trades_today.values() if t.status == status]

    def cleanup_old_files(self, days_to_keep: int = 30):
        """Clean up old trade files"""
        try:
            archive_dir = os.path.join(self.data_dir, "archive")
            if not os.path.exists(archive_dir):
                return

            cutoff_date = date.today().replace(day=1)  # Keep current month

            for filename in os.listdir(archive_dir):
                if filename.startswith("trades_") and filename.endswith(".json"):
                    try:
                        file_date_str = filename.replace("trades_", "").replace(".json", "")
                        file_date = date.fromisoformat(file_date_str)

                        if file_date < cutoff_date:
                            file_path = os.path.join(archive_dir, filename)
                            os.remove(file_path)
                            self.logger.info(f"🗑️ Cleaned up old trade file: {filename}")
                    except Exception as e:
                        self.logger.warning(f"Error processing file {filename}: {e}")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def force_reset_daily_count(self, reason: str = "Manual reset"):
        """Force reset daily trade count (emergency use only)"""
        old_count = self.daily_trade_count
        self.daily_trade_count = 0
        self.traded_symbols.clear()
        self.trades_today.clear()

        self._save_daily_state()

        self.logger.warning(f"⚠️ FORCE RESET: Daily trade count reset from {old_count} to 0. Reason: {reason}")

    def get_pending_trades(self) -> List[TradeRecord]:
        """Get all pending trades that need to be checked"""
        return self.get_trades_by_status(TradeStatus.PENDING)

    def cleanup_stale_pending_trades(self, max_age_minutes: int = 5) -> int:
        """
        Clean up stale pending trades that are older than max_age_minutes

        Args:
            max_age_minutes: Maximum age in minutes for pending trades

        Returns:
            int: Number of trades cleaned up
        """
        from datetime import datetime, timedelta

        current_time = datetime.now()
        cutoff_time = current_time - timedelta(minutes=max_age_minutes)

        stale_trades = []
        for trade_id, trade in self.trades_today.items():
            if trade.status == TradeStatus.PENDING:
                try:
                    trade_time = datetime.fromisoformat(trade.timestamp)
                    if trade_time < cutoff_time:
                        stale_trades.append(trade_id)
                except Exception as e:
                    self.logger.error(f"Error parsing timestamp for trade {trade_id}: {e}")
                    # If we can't parse the timestamp, consider it stale
                    stale_trades.append(trade_id)

        # Update stale trades to REJECTED status
        cleaned_count = 0
        for trade_id in stale_trades:
            if self.update_trade_status(
                trade_id=trade_id,
                status=TradeStatus.REJECTED,
                notes=f"Auto-rejected: Stale pending trade (older than {max_age_minutes} minutes)"
            ):
                cleaned_count += 1
                self.logger.info(f"🧹 Cleaned up stale pending trade: {trade_id}")

        if cleaned_count > 0:
            self.logger.info(f"🧹 Cleaned up {cleaned_count} stale pending trades")

        return cleaned_count

    def force_clear_all_pending_trades(self, reason: str = "Manual cleanup") -> int:
        """
        Force clear all pending trades by marking them as REJECTED

        Args:
            reason: Reason for clearing trades

        Returns:
            int: Number of trades cleared
        """
        pending_trades = self.get_pending_trades()
        cleared_count = 0

        for trade in pending_trades:
            if self.update_trade_status(
                trade_id=trade.trade_id,
                status=TradeStatus.REJECTED,
                notes=f"Force cleared: {reason}"
            ):
                cleared_count += 1

        if cleared_count > 0:
            self.logger.warning(f"⚠️ FORCE CLEARED {cleared_count} pending trades. Reason: {reason}")

        return cleared_count
