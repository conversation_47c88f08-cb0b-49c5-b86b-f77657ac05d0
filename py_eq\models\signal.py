"""
Signal model for trading signals
"""
import csv
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional


class SignalType(Enum):
    """Signal type enumeration"""
    BUY = "BUY"
    SELL = "SELL"


@dataclass
class Signal:
    """Represents a trading signal for a specific stock"""
    symbol: str
    signal_type: SignalType
    timeframe: str = "15min"  # Default to 15min
    preferred_strategy: str = "ALL"  # Strategy preference for virtual account assignment

    # Trading parameters - will be set by strategies
    price: Optional[float] = None  # Entry price
    stop_loss: Optional[float] = None  # Stop loss price
    target: Optional[float] = None  # Target price
    entry_price: Optional[float] = None  # Alias for price
    confidence: float = 1.0  # Signal confidence (0.0 to 1.0)
    strategy: Optional[str] = None  # Strategy that generated this signal

    def __post_init__(self):
        """Validate signal data"""
        if not self.symbol:
            raise ValueError("Symbol cannot be empty")
        if self.timeframe not in ["5min", "15min"]:
            raise ValueError("Timeframe must be either '5min' or '15min'")

        # Sync entry_price with price if one is set
        if self.price is not None and self.entry_price is None:
            self.entry_price = self.price
        elif self.entry_price is not None and self.price is None:
            self.price = self.entry_price

    @property
    def is_buy_signal(self) -> bool:
        """Returns True if this is a buy signal"""
        return self.signal_type == SignalType.BUY

    @property
    def is_sell_signal(self) -> bool:
        """Returns True if this is a sell signal"""
        return self.signal_type == SignalType.SELL

    def to_dict(self) -> dict:
        """Convert signal to dictionary"""
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type.value,
            'timeframe': self.timeframe,
            'preferred_strategy': self.preferred_strategy,
            'price': self.price,
            'stop_loss': self.stop_loss,
            'target': self.target,
            'entry_price': self.entry_price,
            'confidence': self.confidence,
            'strategy': self.strategy
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Signal':
        """Create signal from dictionary"""
        return cls(
            symbol=data['symbol'],
            signal_type=SignalType(data['signal_type']),
            timeframe=data.get('timeframe', '15min'),
            preferred_strategy=data.get('preferred_strategy', 'ALL'),
            price=data.get('price'),
            stop_loss=data.get('stop_loss'),
            target=data.get('target'),
            entry_price=data.get('entry_price'),
            confidence=data.get('confidence', 1.0),
            strategy=data.get('strategy')
        )


def read_signals_from_csv(file_path: str, signal_type: SignalType) -> List[Signal]:
    """
    Read stock symbols from a CSV file and create signals

    Args:
        file_path: Path to the CSV file
        signal_type: Type of signal to create (BUY or SELL)

    Returns:
        List of Signal objects
    """
    signals = []

    try:
        with open(file_path, 'r', newline='', encoding='utf-8') as file:
            reader = csv.reader(file)
            records = list(reader)

            if not records:
                raise ValueError("CSV file is empty")

            # Determine timeframe based on signal type
            timeframe = "15min" if signal_type == SignalType.BUY else "5min"

            # Check if first row might be a header
            start_idx = 0
            if (len(records) > 1 and
                records[0][0].upper() in ['SYMBOL', 'STOCK', 'NAME']):
                start_idx = 1

            # Create signals from records
            for i in range(start_idx, len(records)):
                if len(records[i]) < 1 or not records[i][0].strip():
                    continue

                symbol = records[i][0].strip().upper()
                signal = Signal(
                    symbol=symbol,
                    signal_type=signal_type,
                    timeframe=timeframe
                )
                signals.append(signal)

    except FileNotFoundError:
        raise FileNotFoundError(f"CSV file not found: {file_path}")
    except Exception as e:
        raise Exception(f"Error reading CSV file: {e}")

    return signals



