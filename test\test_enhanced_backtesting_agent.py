#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Backtesting Agent

Tests all major features:
- Multi-strategy and multi-timeframe backtesting
- Smart backtesting modes
- Performance metrics calculation
- Capital and risk modeling
- Configuration loading
- Result processing
"""

import os
import sys
import pytest
import asyncio
import tempfile
import yaml
import json
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

# Add the parent directory to the path to import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.enhanced_backtesting_agent import (
    EnhancedBacktestingAgent,
    BacktestConfig,
    BacktestMode,
    MarketRegime,
    TradeResult,
    PerformanceMetrics,
    BacktestResults
)

from agents.backtesting_core import (
    SignalGenerator,
    TradeExecutor,
    TradingSignal
)

class TestEnhancedBacktestingAgent:
    """Test suite for Enhanced Backtesting Agent"""
    
    @pytest.fixture
    def sample_config(self):
        """Create sample configuration for testing"""
        return {
            'general': {
                'agent_name': 'Test Backtesting Agent',
                'version': '2.0.0',
                'data_directory': 'test_data/features',
                'output_directory': 'test_data/backtest',
                'strategies_config': 'test_config/strategies.yaml',
                'symbols_config': 'test_config/symbols.json',
                'max_symbols': 5,
                'max_strategies': 10,
            },
            'multi_strategy': {
                'enable_individual_strategies': True,
                'enable_batch_processing': True,
            },
            'multi_timeframe': {
                'timeframes': ['5min', '15min'],
                'process_timeframes_parallel': True,
                'asset_types': {
                    'underlying': ['NIFTY', 'BANKNIFTY'],
                    'options': {
                        'option_types': ['CE', 'PE'],
                        'expiry_types': ['weekly', 'monthly']
                    }
                }
            },
            'backtesting_modes': {
                'enable_deterministic': True,
                'enable_probabilistic': False,
                'enable_adaptive_ai': False,
            },
            'capital_risk_modeling': {
                'initial_capital': 100000.0,
                'position_sizing': {
                    'risk_per_trade_pct': 1.0,
                },
                'risk_management': {
                    'max_concurrent_trades': 5,
                    'max_drawdown_pct': 10.0,
                    'risk_reward_ratios': [[1, 1.5], [1, 2.0]],
                },
                'transaction_costs': {
                    'brokerage_pct': 0.03,
                    'brokerage_flat': 20.0,
                    'stt_pct': 0.025,
                    'slippage_pct': 0.02,
                }
            },
            'performance': {
                'parallel_processing': {
                    'enable_multiprocessing': True,
                    'max_workers': 2,
                    'chunk_size': 10000,
                },
                'gpu_acceleration': {
                    'enable_gpu': False,  # Disable for testing
                }
            },
            'result_logging': {
                'output_format': 'parquet',
                'compression': 'snappy',
                'versioning': {
                    'enable_versioning': True,
                }
            }
        }
    
    @pytest.fixture
    def sample_strategies(self):
        """Create sample strategies for testing"""
        return {
            'strategies': [
                {
                    'name': 'Test_RSI_Strategy',
                    'long': 'rsi_14 < 30',
                    'short': 'rsi_14 > 70',
                    'capital': 100000
                },
                {
                    'name': 'Test_EMA_Strategy',
                    'long': 'ema_5 > ema_20',
                    'short': 'ema_5 < ema_20',
                    'capital': 100000
                }
            ]
        }
    
    @pytest.fixture
    def sample_symbols(self):
        """Create sample symbols for testing"""
        return ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICIBANK']
    
    @pytest.fixture
    def sample_feature_data(self):
        """Create sample feature data for testing"""
        # Generate sample OHLCV data with technical indicators
        dates = [datetime.now() - timedelta(days=i) for i in range(100, 0, -1)]
        
        data = []
        for i, date in enumerate(dates):
            # Generate realistic price data
            base_price = 1000 + np.sin(i * 0.1) * 100
            
            row = {
                'datetime': date,
                'symbol': 'RELIANCE',
                'open': base_price + np.random.normal(0, 5),
                'high': base_price + abs(np.random.normal(10, 5)),
                'low': base_price - abs(np.random.normal(10, 5)),
                'close': base_price + np.random.normal(0, 5),
                'volume': int(1000000 + np.random.normal(0, 100000)),
                
                # Technical indicators
                'rsi_14': 30 + np.random.normal(20, 15),
                'ema_5': base_price + np.random.normal(0, 2),
                'ema_20': base_price + np.random.normal(0, 5),
                'macd': np.random.normal(0, 2),
                'macd_signal': np.random.normal(0, 1.5),
                'bb_upper': base_price + 20,
                'bb_lower': base_price - 20,
                'atr': abs(np.random.normal(15, 5)),
                'vwap': base_price + np.random.normal(0, 3),
            }
            data.append(row)
        
        return pl.DataFrame(data)
    
    @pytest.fixture
    async def temp_directories(self):
        """Create temporary directories for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create subdirectories
            data_dir = os.path.join(temp_dir, 'data', 'features')
            output_dir = os.path.join(temp_dir, 'data', 'backtest')
            config_dir = os.path.join(temp_dir, 'config')
            
            os.makedirs(data_dir, exist_ok=True)
            os.makedirs(output_dir, exist_ok=True)
            os.makedirs(config_dir, exist_ok=True)
            
            yield {
                'temp_dir': temp_dir,
                'data_dir': data_dir,
                'output_dir': output_dir,
                'config_dir': config_dir,
            }
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, temp_directories, sample_config, sample_strategies, sample_symbols):
        """Test agent initialization"""
        # Setup test files
        config_path = os.path.join(temp_directories['config_dir'], 'enhanced_backtesting_config.yaml')
        strategies_path = os.path.join(temp_directories['config_dir'], 'strategies.yaml')
        symbols_path = os.path.join(temp_directories['config_dir'], 'symbols.json')
        
        # Update config paths
        sample_config['general']['data_directory'] = temp_directories['data_dir']
        sample_config['general']['output_directory'] = temp_directories['output_dir']
        sample_config['general']['strategies_config'] = strategies_path
        sample_config['general']['symbols_config'] = symbols_path
        
        # Save test files
        with open(config_path, 'w') as f:
            yaml.dump(sample_config, f)
        
        with open(strategies_path, 'w') as f:
            yaml.dump(sample_strategies, f)
        
        with open(symbols_path, 'w') as f:
            json.dump(sample_symbols, f)
        
        # Test initialization
        agent = EnhancedBacktestingAgent(config_path)
        success = await agent.initialize()
        
        assert success is True
        assert agent.config is not None
        assert agent.config.agent_name == 'Test Backtesting Agent'
        assert len(agent.strategies) == 2
        assert len(agent.symbols) == 5
    
    @pytest.mark.asyncio
    async def test_config_creation_from_dict(self):
        """Test configuration creation from dictionary"""
        agent = EnhancedBacktestingAgent()
        
        config_dict = {
            'general': {
                'agent_name': 'Test Agent',
                'version': '1.0.0',
                'initial_capital': 50000.0,
            },
            'multi_timeframe': {
                'timeframes': ['1min', '5min'],
            },
            'capital_risk_modeling': {
                'initial_capital': 50000.0,
                'position_sizing': {
                    'risk_per_trade_pct': 2.0,
                }
            }
        }
        
        config = agent._create_config_from_dict(config_dict)
        
        assert config.agent_name == 'Test Agent'
        assert config.version == '1.0.0'
        assert config.initial_capital == 50000.0
        assert config.risk_per_trade_pct == 2.0
        assert config.timeframes == ['1min', '5min']
    
    @pytest.mark.asyncio
    async def test_signal_generation(self, sample_feature_data):
        """Test signal generation"""
        signal_generator = SignalGenerator(enable_gpu=False)
        
        strategy = {
            'name': 'Test_Strategy',
            'long': 'rsi_14 < 30',
            'short': 'rsi_14 > 70',
        }
        
        signals = await signal_generator.generate_signals(sample_feature_data, strategy)
        
        assert isinstance(signals, list)
        # Should generate some signals given the random RSI values
        assert len(signals) >= 0
        
        # Check signal structure if any signals generated
        if signals:
            signal = signals[0]
            assert isinstance(signal, TradingSignal)
            assert signal.signal_type in ['long', 'short']
            assert signal.entry_price > 0
            assert signal.symbol == 'RELIANCE'
    
    @pytest.mark.asyncio
    async def test_trade_execution(self, sample_feature_data):
        """Test trade execution"""
        config = {
            'initial_capital': 100000,
            'risk_per_trade_pct': 1.0,
            'brokerage_pct': 0.03,
            'brokerage_flat': 20.0,
            'stt_pct': 0.025,
            'slippage_pct': 0.02,
        }
        
        trade_executor = TradeExecutor(config)
        
        # Create test signals
        signals = [
            TradingSignal(
                timestamp=sample_feature_data['datetime'][10],
                symbol='RELIANCE',
                signal_type='long',
                entry_price=1000.0,
                confidence=1.0
            )
        ]
        
        strategy = {'name': 'Test_Strategy', 'capital': 100000}
        risk_reward_ratio = [2.0, 4.0]  # 2% risk, 4% reward
        
        trades = await trade_executor.execute_trades(
            sample_feature_data, signals, strategy, risk_reward_ratio
        )
        
        assert isinstance(trades, list)
        # Should execute at least one trade
        if trades:
            trade = trades[0]
            assert 'trade_id' in trade
            assert 'pnl' in trade
            assert 'entry_time' in trade
            assert 'exit_time' in trade
    
    def test_performance_metrics_calculation(self):
        """Test performance metrics calculation"""
        agent = EnhancedBacktestingAgent()
        agent.config = BacktestConfig()
        
        # Create sample trades
        trades = [
            {
                'pnl': 100,
                'pnl_pct': 1.0,
                'holding_period': 2.0,
                'position_size': 10000,
            },
            {
                'pnl': -50,
                'pnl_pct': -0.5,
                'holding_period': 1.5,
                'position_size': 10000,
            },
            {
                'pnl': 200,
                'pnl_pct': 2.0,
                'holding_period': 3.0,
                'position_size': 10000,
            }
        ]
        
        # Test the synchronous version of the method
        import asyncio
        metrics = asyncio.run(agent._calculate_performance_metrics(
            trades, 'RELIANCE', 'Test_Strategy', '5min'
        ))
        
        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_trades == 3
        assert metrics.winning_trades == 2
        assert metrics.losing_trades == 1
        assert metrics.win_rate == (2/3) * 100
        assert metrics.total_return == 250  # 100 - 50 + 200
        assert metrics.expectancy == 250/3  # Average return per trade
    
    def test_max_drawdown_calculation(self):
        """Test maximum drawdown calculation"""
        agent = EnhancedBacktestingAgent()
        
        # Test with sample returns
        returns = np.array([1.0, -2.0, 1.5, -1.0, 2.0, -3.0, 1.0])
        max_dd = agent._calculate_max_drawdown(returns)
        
        assert max_dd >= 0  # Drawdown should be positive
        assert isinstance(max_dd, float)
    
    def test_sharpe_ratio_calculation(self):
        """Test Sharpe ratio calculation"""
        agent = EnhancedBacktestingAgent()
        
        # Test with sample returns
        returns = np.array([0.01, 0.02, -0.01, 0.015, 0.005, -0.005, 0.02])
        sharpe = agent._calculate_sharpe_ratio(returns)
        
        assert isinstance(sharpe, float)
        # Should be reasonable for positive average returns
        assert -5 <= sharpe <= 5
    
    def test_kelly_criterion_calculation(self):
        """Test Kelly criterion calculation"""
        agent = EnhancedBacktestingAgent()
        
        wins = [100, 150, 200]
        losses = [-50, -75, -100]
        win_rate = 60.0
        
        kelly = agent._calculate_kelly_criterion(wins, losses, win_rate)
        
        assert isinstance(kelly, float)
        assert 0 <= kelly <= 0.25  # Should be capped at 25%
    
    @pytest.mark.asyncio
    async def test_feature_data_loading(self, temp_directories, sample_feature_data):
        """Test feature data loading"""
        agent = EnhancedBacktestingAgent()
        agent.config = BacktestConfig(data_directory=temp_directories['data_dir'])
        
        # Save sample data
        data_file = os.path.join(temp_directories['data_dir'], 'features_5min.parquet')
        sample_feature_data.write_parquet(data_file)
        
        # Test loading
        loaded_data = await agent._load_feature_data('5min')
        
        assert loaded_data is not None
        assert isinstance(loaded_data, pl.DataFrame)
        assert loaded_data.height > 0
        assert 'datetime' in loaded_data.columns
        assert 'close' in loaded_data.columns
    
    @pytest.mark.asyncio
    async def test_symbol_discovery(self, temp_directories, sample_feature_data):
        """Test symbol discovery from data"""
        agent = EnhancedBacktestingAgent()
        agent.config = BacktestConfig(
            data_directory=temp_directories['data_dir'],
            timeframes=['5min']
        )
        
        # Save sample data
        data_file = os.path.join(temp_directories['data_dir'], 'features_5min.parquet')
        sample_feature_data.write_parquet(data_file)
        
        # Test symbol discovery
        symbols = await agent._discover_symbols_from_data()
        
        assert isinstance(symbols, list)
        assert 'RELIANCE' in symbols
    
    def test_config_validation(self):
        """Test configuration validation"""
        agent = EnhancedBacktestingAgent()
        
        # Test with valid config
        agent.config = BacktestConfig(
            data_directory='data/features',  # Assuming this exists
            strategies_config='config/strategies.yaml',  # Assuming this exists
            timeframes=['5min'],
            initial_capital=100000,
            risk_per_trade_pct=1.0
        )
        
        # Mock the file existence checks
        with patch('os.path.exists', return_value=True):
            result = asyncio.run(agent._validate_config())
            assert result is True
        
        # Test with invalid config
        agent.config.initial_capital = -1000
        with patch('os.path.exists', return_value=True):
            result = asyncio.run(agent._validate_config())
            assert result is False
    
    def test_transaction_cost_calculation(self):
        """Test transaction cost calculation"""
        config = {
            'brokerage_pct': 0.03,
            'brokerage_flat': 20.0,
            'stt_pct': 0.025,
            'slippage_pct': 0.02,
        }
        
        trade_executor = TradeExecutor(config)
        
        position_size = 10000
        cost = trade_executor._calculate_transaction_costs(position_size)
        
        assert cost > 0
        assert isinstance(cost, float)
        # Should include brokerage, STT, and slippage
        assert cost > 20  # At least the flat brokerage
    
    @pytest.mark.asyncio
    async def test_result_processing(self, temp_directories):
        """Test result processing and saving"""
        agent = EnhancedBacktestingAgent()
        agent.config = BacktestConfig(
            output_directory=temp_directories['output_dir'],
            output_format='parquet',
            compression='snappy',
            enable_versioning=True
        )
        
        # Create sample results
        sample_metrics = PerformanceMetrics(
            strategy_name='Test_Strategy',
            symbol='RELIANCE',
            timeframe='5min',
            total_trades=10,
            winning_trades=6,
            losing_trades=4,
            win_rate=60.0,
            total_return=1000,
            annualized_return=12.0,
            roi=10.0,
            expectancy=100,
            max_drawdown=5.0,
            sharpe_ratio=1.5,
            sortino_ratio=1.8,
            calmar_ratio=2.0,
            profit_factor=1.5,
            recovery_factor=2.0,
            payoff_ratio=1.2,
            kelly_criterion=0.15,
            avg_win=200,
            avg_loss=-100,
            largest_win=500,
            largest_loss=-200,
            avg_holding_period=2.5,
            capital_utilization=80.0,
            return_on_margin=12.0,
        )
        
        sample_result = BacktestResults(
            backtest_id='test_123',
            config=agent.config,
            trades=[],
            performance_metrics=sample_metrics,
            start_time=datetime.now(),
            end_time=datetime.now(),
            execution_time_seconds=10.0,
            total_data_points=1000,
            data_quality_score=0.95,
        )
        
        # Test result processing
        await agent._process_and_save_results([sample_result])
        
        # Check if files were created
        output_files = list(Path(temp_directories['output_dir']).glob('*.parquet'))
        assert len(output_files) > 0
    
    def test_status_reporting(self):
        """Test status reporting"""
        agent = EnhancedBacktestingAgent()
        agent.config = BacktestConfig()
        agent.is_running = True
        agent.processed_strategies = 5
        agent.total_strategies = 10
        
        status = agent.get_status()
        
        assert isinstance(status, dict)
        assert status['is_running'] is True
        assert status['processed_strategies'] == 5
        assert status['total_strategies'] == 10
        assert status['progress_pct'] == 50.0

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
