#!/usr/bin/env python3
"""
Test Online Learning Implementation
Demonstrates memory-efficient training on large datasets without data duplication
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from agents.ai_training_agent import AITrainingAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_online_learning():
    """Test the new online learning functionality"""
    
    logger.info("🧪 Testing Online Learning Implementation")
    logger.info("=" * 60)
    
    try:
        # Initialize AI Training Agent
        agent = AITrainingAgent()
        
        # Update configuration for online learning
        agent.config.online_learning_enabled = True
        agent.config.date_based_filtering = True
        agent.config.training_cutoff_date = "2025-07-04"
        agent.config.memory_efficient_mode = True
        agent.config.stream_chunk_size = 1000
        
        logger.info("✅ AI Training Agent initialized with online learning config")
        
        # Test with a feature file
        feature_file = "data/features/features_historical_5min.parquet"
        
        if not Path(feature_file).exists():
            logger.error(f"❌ Feature file not found: {feature_file}")
            logger.info("Available files:")
            for f in Path("data/features").glob("*.parquet"):
                logger.info(f"  - {f.name}")
            return
        
        # Check file size
        file_size_gb = Path(feature_file).stat().st_size / (1024**3)
        logger.info(f"📊 File size: {file_size_gb:.2f} GB")
        
        # Test streaming data with date filter
        logger.info("🌊 Testing streaming data with date filter...")
        
        chunk_count = 0
        total_rows = 0
        
        for chunk in agent.stream_data_with_date_filter(feature_file, chunk_size=500):
            chunk_count += 1
            total_rows += len(chunk)
            
            logger.info(f"📦 Chunk {chunk_count}: {len(chunk):,} rows")
            
            # Only process first few chunks for testing
            if chunk_count >= 3:
                logger.info("🛑 Stopping after 3 chunks for testing")
                break
        
        logger.info(f"✅ Streaming test completed: {chunk_count} chunks, {total_rows:,} total rows")
        
        # Test online training (with limited data for testing)
        logger.info("🎯 Testing online training...")
        
        # Temporarily reduce chunk size for faster testing
        agent.config.stream_chunk_size = 100
        
        # Run online training
        results = await agent.train_online_streaming(feature_file)
        
        logger.info("📊 Training Results:")
        for key, value in results.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("🎉 Online learning test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise

async def test_date_filtering():
    """Test date filtering functionality"""
    
    logger.info("📅 Testing Date Filtering")
    logger.info("=" * 40)
    
    try:
        import polars as pl
        
        # Load sample data
        feature_file = "data/features/features_historical_15min.parquet"
        
        if not Path(feature_file).exists():
            logger.warning(f"⚠️ Feature file not found: {feature_file}")
            return
        
        # Load full data
        df_full = pl.read_parquet(feature_file)
        logger.info(f"📊 Full dataset: {len(df_full):,} rows")
        logger.info(f"📅 Date range: {df_full['date'].min()} to {df_full['date'].max()}")
        
        # Initialize agent
        agent = AITrainingAgent()
        agent.config.training_cutoff_date = "2025-07-04"
        agent.config.date_based_filtering = True
        
        # Test streaming with date filter
        filtered_rows = 0
        for chunk in agent.stream_data_with_date_filter(feature_file, chunk_size=1000):
            filtered_rows += len(chunk)
        
        logger.info(f"📊 Filtered dataset: {filtered_rows:,} rows")
        logger.info(f"🔍 Filtered out: {len(df_full) - filtered_rows:,} rows ({((len(df_full) - filtered_rows) / len(df_full) * 100):.1f}%)")
        
        if filtered_rows < len(df_full):
            logger.info("✅ Date filtering is working correctly!")
        else:
            logger.warning("⚠️ No data was filtered - check date format or cutoff date")
        
    except Exception as e:
        logger.error(f"❌ Date filtering test failed: {e}")

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Online Learning Tests")
    logger.info("=" * 80)
    
    # Test 1: Date filtering
    asyncio.run(test_date_filtering())
    
    print("\n" + "=" * 80 + "\n")
    
    # Test 2: Online learning
    asyncio.run(test_online_learning())
    
    logger.info("🎯 All tests completed!")

if __name__ == "__main__":
    main()
