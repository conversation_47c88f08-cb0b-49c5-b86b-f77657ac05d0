#!/usr/bin/env python3
"""
Production Async Strategy Manager
Async wrapper for ProductionStrategyManager for optimized performance
"""

import asyncio
import logging
import time
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor

from models.signal import Signal
from models.order import Order
from strategies.production_strategy_manager import ProductionStrategyManager
from services.market_data_service import MarketDataServiceInterface


class ProductionAsyncStrategyManager:
    """
    Async wrapper for ProductionStrategyManager
    Provides optimized batch processing for production trading
    """

    def __init__(self, strategy_manager: ProductionStrategyManager, logger: logging.Logger):
        self.strategy_manager = strategy_manager
        self.logger = logger
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ProductionAsync")
        
        self.logger.info("🚀 Production Async Strategy Manager initialized")

    async def process_signals_batch_async(self, signals: List[Signal]) -> List[Order]:
        """
        Process signals asynchronously for better performance
        
        Args:
            signals: List of trading signals
            
        Returns:
            List of executed orders
        """
        if not signals:
            return []

        start_time = time.time()
        
        try:
            # Run the synchronous strategy manager in a thread pool
            loop = asyncio.get_event_loop()
            executed_orders = await loop.run_in_executor(
                self.executor,
                self.strategy_manager.process_signals,
                signals
            )
            
            processing_time = time.time() - start_time
            
            if executed_orders:
                self.logger.info(f"⚡ Async batch processing completed: {len(executed_orders)} orders from {len(signals)} signals in {processing_time:.2f}s (avg: {processing_time/len(signals)*1000:.1f}ms per signal)")
            else:
                self.logger.info(f"⚡ Async batch processing completed: 0 orders from {len(signals)} signals in {processing_time:.2f}s (avg: {processing_time/len(signals)*1000:.1f}ms per signal)")
            
            return executed_orders
            
        except Exception as e:
            self.logger.error(f"❌ Error in async signal processing: {e}")
            return []

    def cleanup(self):
        """Clean up async resources"""
        try:
            self.executor.shutdown(wait=True)
            self.logger.info("🧹 Production async strategy manager cleaned up")
        except Exception as e:
            self.logger.error(f"❌ Error during async cleanup: {e}")


class ProductionAsyncMarketDataProcessor:
    """
    Async market data processor for production trading
    Optimizes data fetching and processing
    """

    def __init__(self, market_data_service: MarketDataServiceInterface, logger: logging.Logger):
        self.market_data_service = market_data_service
        self.logger = logger
        self.executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix="DataProcessor")
        
        self.logger.info("📊 Production Async Market Data Processor initialized")

    async def fetch_data_batch_async(self, symbols: List[str], timeframe: str = "5min") -> Dict[str, Any]:
        """
        Fetch market data for multiple symbols asynchronously
        
        Args:
            symbols: List of symbols to fetch data for
            timeframe: Timeframe for data
            
        Returns:
            Dictionary of symbol -> data mappings
        """
        if not symbols:
            return {}

        start_time = time.time()
        
        try:
            # Create tasks for concurrent data fetching
            loop = asyncio.get_event_loop()
            tasks = []
            
            for symbol in symbols:
                task = loop.run_in_executor(
                    self.executor,
                    self.market_data_service.get_historical_data,
                    symbol,
                    timeframe,
                    3  # 3 days of data
                )
                tasks.append((symbol, task))
            
            # Wait for all tasks to complete
            results = {}
            for symbol, task in tasks:
                try:
                    data = await task
                    results[symbol] = data
                except Exception as e:
                    self.logger.error(f"❌ Error fetching data for {symbol}: {e}")
                    results[symbol] = []
            
            processing_time = time.time() - start_time
            successful_fetches = sum(1 for data in results.values() if data)
            
            self.logger.info(f"📊 Async data fetch completed: {successful_fetches}/{len(symbols)} symbols in {processing_time:.2f}s")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Error in async data processing: {e}")
            return {}

    def cleanup(self):
        """Clean up async resources"""
        try:
            self.executor.shutdown(wait=True)
            self.logger.info("🧹 Production async data processor cleaned up")
        except Exception as e:
            self.logger.error(f"❌ Error during data processor cleanup: {e}")
