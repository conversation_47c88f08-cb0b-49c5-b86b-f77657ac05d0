"""
Enhanced Position monitoring service for tracking open positions and managing exits
with optimized stop-loss mechanism for immediate execution
"""
import logging
import time
import threading
from typing import List, Optional, Dict, Tuple
from datetime import datetime, time as dt_time
from dataclasses import dataclass
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor

from models.order import Order, OrderStatus, TransactionType, ExitReason
from services.order_service import OrderServiceInterface
from services.market_data_service import MarketDataServiceInterface
from services.websocket_service import LivePriceWebSocket
from services.trading_logger import TradingLogger
from utils.safe_logging import safe_info, safe_error, safe_warning, safe_debug


@dataclass
class ExecutionTiming:
    """Track execution timing for performance analysis"""
    trigger_time: datetime
    order_placed_time: Optional[datetime] = None
    order_confirmed_time: Optional[datetime] = None
    total_execution_ms: Optional[float] = None
    slippage: Optional[float] = None

    def calculate_execution_time(self):
        """Calculate total execution time in milliseconds"""
        if self.trigger_time and self.order_confirmed_time:
            delta = self.order_confirmed_time - self.trigger_time
            self.total_execution_ms = delta.total_seconds() * 1000
        return self.total_execution_ms


@dataclass
class StopLossConfig:
    """Enhanced stop-loss configuration"""
    max_slippage_percent: float = 0.5  # Maximum allowed slippage
    emergency_exit_threshold: float = 2.0  # Emergency exit if loss exceeds this %
    retry_attempts: int = 3  # Number of retry attempts for failed orders
    retry_delay_ms: int = 100  # Delay between retries in milliseconds
    pre_auth_enabled: bool = True  # Pre-authenticate API sessions
    parallel_execution: bool = True  # Execute multiple exits in parallel


class ExitPriority(Enum):
    """Priority levels for exit orders"""
    EMERGENCY = 1  # Immediate emergency exits
    STOP_LOSS = 2  # Regular stop-loss exits
    TARGET = 3     # Target profit exits
    TIME_EXIT = 4  # Time-based exits


@dataclass
class PositionRisk:
    """Real-time position risk tracking"""
    symbol: str
    current_loss_percent: float
    max_loss_percent: float
    time_in_position: float  # seconds
    price_volatility: float
    risk_level: str  # LOW, MEDIUM, HIGH, CRITICAL


class EnhancedPositionMonitor:
    """Enhanced position monitor with optimized stop-loss mechanism for immediate execution"""

    def __init__(
        self,
        order_service: OrderServiceInterface,
        market_data_service: MarketDataServiceInterface,
        logger: logging.Logger,
        websocket_service: Optional[LivePriceWebSocket] = None,
        trading_logger: Optional[TradingLogger] = None,
        check_interval: int = 5,  # seconds between position checks (fallback only)
        square_off_time: str = "15:15",
        sl_config: Optional[StopLossConfig] = None
    ):
        self.order_service = order_service
        self.market_data_service = market_data_service
        self.websocket_service = websocket_service
        self.trading_logger = trading_logger
        self.logger = logger
        self.check_interval = check_interval

        # Enhanced SL configuration
        self.sl_config = sl_config or StopLossConfig()

        # Parse square off time
        self.square_off_time = dt_time.fromisoformat(square_off_time)

        self.monitoring = False
        self.monitor_thread = None
        self._stop_event = threading.Event()

        # Real-time price tracking
        self.live_prices: Dict[str, float] = {}
        self.position_symbols: Dict[str, Order] = {}  # symbol -> order mapping

        # Enhanced tracking for performance analysis
        self.execution_timings: Dict[str, ExecutionTiming] = {}
        self.position_risks: Dict[str, PositionRisk] = {}
        self.exit_queue: asyncio.Queue = asyncio.Queue()
        self.executor = ThreadPoolExecutor(max_workers=5)  # For parallel execution

        # Pre-calculated trigger prices for faster comparison
        self.sl_trigger_prices: Dict[str, float] = {}
        self.target_trigger_prices: Dict[str, float] = {}

        # Performance metrics
        self.total_exits = 0
        self.successful_exits = 0
        self.failed_exits = 0
        self.average_execution_time = 0.0
        self.total_slippage = 0.0

        # Emergency circuit breaker
        self.emergency_mode = False
        self.emergency_threshold_breached = False

    def start_monitoring(self):
        """Start position monitoring in a separate thread"""
        if self.monitoring:
            self.logger.warning("Position monitoring is already running")
            return

        self.monitoring = True
        self._stop_event.clear()

        # Set up WebSocket price callbacks if available
        if self.websocket_service:
            safe_info(self.logger, "🔗 Using WebSocket for real-time price monitoring")
        else:
            safe_info(self.logger, "📊 Using polling for price monitoring (fallback mode)")

        self.monitor_thread = threading.Thread(target=self._monitor_positions, daemon=True)
        self.monitor_thread.start()
        safe_info(self.logger, "🔍 Position monitoring started")

    def stop_monitoring(self):
        """Stop position monitoring"""
        if not self.monitoring:
            return

        self.monitoring = False
        self._stop_event.set()

        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)

        safe_info(self.logger, "⏹️ Position monitoring stopped")

    def _monitor_positions(self):
        """Main monitoring loop - runs in separate thread"""
        safe_info(self.logger, "📊 Position monitoring loop started")

        while self.monitoring and not self._stop_event.is_set():
            try:
                # Check if it's square off time
                if self._is_square_off_time():
                    safe_info(self.logger, "🕐 Square off time reached (15:15) - closing all positions")
                    self.square_off_all_positions("SQUARE_OFF_TIME")
                    break

                self._check_all_positions()

                # Wait for next check or stop signal
                if self._stop_event.wait(timeout=self.check_interval):
                    break  # Stop event was set

            except Exception as e:
                self.logger.error(f"Error in position monitoring loop: {e}")
                time.sleep(self.check_interval)

        safe_info(self.logger, "📊 Position monitoring loop ended")

    def _check_all_positions(self):
        """Check all open positions for stop-loss and target hits"""
        try:
            # Get open positions from order service
            open_positions = self.order_service.get_open_positions()

            if not open_positions:
                return  # No positions to monitor

            self.logger.debug(f"Monitoring {len(open_positions)} open positions")

            # Update position tracking and WebSocket subscriptions
            self._update_position_tracking(open_positions)

            for position in open_positions:
                try:
                    self._check_position(position)
                except Exception as e:
                    self.logger.error(f"Error checking position {position.order_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error getting open positions: {e}")

    def _update_position_tracking(self, open_positions: List[Order]):
        """Update position tracking and WebSocket subscriptions"""
        current_symbols = {pos.symbol for pos in open_positions}
        tracked_symbols = set(self.position_symbols.keys())

        # Subscribe to new symbols
        new_symbols = current_symbols - tracked_symbols
        for symbol in new_symbols:
            position = next(pos for pos in open_positions if pos.symbol == symbol)
            self.position_symbols[symbol] = position

            if self.websocket_service:
                # Subscribe to WebSocket for this symbol
                self._subscribe_to_symbol(symbol, position)
                # Set up price callback for immediate exit decisions
                self.websocket_service.add_price_callback(symbol, self._on_price_update)

        # Unsubscribe from closed positions
        closed_symbols = tracked_symbols - current_symbols
        for symbol in closed_symbols:
            if self.websocket_service:
                self.websocket_service.unsubscribe_symbol(symbol)
            del self.position_symbols[symbol]
            if symbol in self.live_prices:
                del self.live_prices[symbol]

    def _subscribe_to_symbol(self, symbol: str, position: Order):
        """Subscribe to WebSocket for a symbol"""
        try:
            # Use the symbol token from the order
            token = position.symbol_token
            if not token:
                safe_warning(self.logger, f"⚠️ No symbol token found for {symbol}, cannot subscribe to WebSocket")
                return

            exchange_type = 1  # Default to NSE, could be made configurable

            success = self.websocket_service.subscribe_symbol(symbol, token, exchange_type)
            if success:
                safe_info(self.logger, f"📡 Subscribed to live prices for {symbol} (token: {token})")
            else:
                safe_warning(self.logger, f"⚠️ Failed to subscribe to live prices for {symbol}")

        except Exception as e:
            self.logger.error(f"Error subscribing to WebSocket for {symbol}: {e}")

    def _on_price_update(self, symbol: str, price: float, timestamp: datetime):
        """Enhanced callback for real-time price updates with immediate SL execution"""
        try:
            # Update live price with timestamp
            self.live_prices[symbol] = price

            # Check if we have a position for this symbol
            if symbol not in self.position_symbols:
                return

            position = self.position_symbols[symbol]

            # Check if position is already squared off
            if hasattr(position, 'squared_off') and position.squared_off:
                return  # Position already closed, ignore further updates

            # Update position risk metrics
            self._update_position_risk(symbol, price, position)

            # Fast path: Use pre-calculated trigger prices for immediate comparison
            exit_triggered = False
            exit_reason = None
            priority = None

            # Check emergency exit first (highest priority)
            if self._is_emergency_exit_required(position, price):
                exit_triggered = True
                exit_reason = "EMERGENCY_EXIT"
                priority = ExitPriority.EMERGENCY
                safe_error(self.logger, f"🚨 EMERGENCY EXIT triggered for {symbol} at ₹{price:.2f}")

            # Check stop-loss (high priority)
            elif symbol in self.sl_trigger_prices and self._is_fast_sl_hit(symbol, price):
                exit_triggered = True
                exit_reason = "STOP_LOSS"
                priority = ExitPriority.STOP_LOSS
                safe_info(self.logger, f"🛑 REAL-TIME Stop-loss hit for {symbol} at ₹{price:.2f}")

            # Check target (medium priority)
            elif symbol in self.target_trigger_prices and self._is_fast_target_hit(symbol, price):
                exit_triggered = True
                exit_reason = "TARGET"
                priority = ExitPriority.TARGET
                safe_info(self.logger, f"🎯 REAL-TIME Target hit for {symbol} at ₹{price:.2f}")

            # Execute exit immediately if triggered
            if exit_triggered:
                # Record trigger timing
                self.execution_timings[symbol] = ExecutionTiming(trigger_time=datetime.now())

                # Execute exit based on priority and configuration
                if self.sl_config.parallel_execution and priority in [ExitPriority.EMERGENCY, ExitPriority.STOP_LOSS]:
                    # Execute in parallel for critical exits
                    self.executor.submit(self._execute_immediate_exit, position, price, exit_reason, priority)
                else:
                    # Execute synchronously
                    success = self._execute_immediate_exit(position, price, exit_reason, priority)
                    if success:
                        self._remove_position_tracking(symbol)

        except Exception as e:
            self.logger.error(f"Error processing enhanced price update for {symbol}: {e}")
            # Fallback to basic exit if enhanced logic fails
            try:
                if symbol in self.position_symbols:
                    position = self.position_symbols[symbol]
                    if self._is_stop_loss_hit(position, price):
                        self._exit_position(position, price, "STOP_LOSS_FALLBACK")
            except Exception as fallback_error:
                self.logger.error(f"Fallback exit also failed for {symbol}: {fallback_error}")

    def _remove_position_tracking(self, symbol: str):
        """Remove position from tracking after exit"""
        try:
            if symbol in self.position_symbols:
                del self.position_symbols[symbol]
            if symbol in self.live_prices:
                del self.live_prices[symbol]

            # Unsubscribe from WebSocket
            if self.websocket_service:
                self.websocket_service.unsubscribe_symbol(symbol)

            self.logger.debug(f"Removed position tracking for {symbol}")
        except Exception as e:
            self.logger.error(f"Error removing position tracking for {symbol}: {e}")

    def _check_position(self, order: Order):
        """Check a single position for exit conditions"""
        try:
            # Check if position is already squared off
            if hasattr(order, 'squared_off') and order.squared_off:
                return  # Position already closed, skip checking

            # Get current market price - prefer WebSocket live price if available
            current_price = None

            if order.symbol in self.live_prices:
                current_price = self.live_prices[order.symbol]
                price_source = "WebSocket"
            else:
                # Fallback to market data service
                current_price = self.market_data_service.get_last_price(order.symbol)
                price_source = "API"

            if current_price is None:
                self.logger.warning(f"Could not get current price for {order.symbol}")
                return

            # For WebSocket prices, exit conditions are already checked in _on_price_update
            # This is mainly for fallback polling mode
            if price_source == "API":
                # Check for stop-loss hit
                if self._is_stop_loss_hit(order, current_price):
                    safe_info(self.logger, f"🛑 Stop-loss hit for {order.symbol} at ₹{current_price:.2f}")
                    success = self._exit_position(order, current_price, "STOP_LOSS")
                    if success:
                        self._remove_position_tracking(order.symbol)
                    return

                # Check for target hit
                if self._is_target_hit(order, current_price):
                    safe_info(self.logger, f"🎯 Target hit for {order.symbol} at ₹{current_price:.2f}")
                    success = self._exit_position(order, current_price, "TARGET")
                    if success:
                        self._remove_position_tracking(order.symbol)
                    return

            # Log current position status (less frequently)
            if hasattr(order, '_last_status_log'):
                time_since_last_log = time.time() - order._last_status_log
                if time_since_last_log < 60:  # Log every minute max
                    return

            # Calculate current P&L
            if order.transaction_type == TransactionType.BUY:
                pnl = (current_price - order.entry_price) * order.quantity
            else:
                pnl = (order.entry_price - current_price) * order.quantity

            safe_debug(self.logger, f"📈 {order.symbol}: Current=₹{current_price:.2f} ({price_source}) "
                            f"Entry=₹{order.entry_price:.2f} SL=₹{order.stop_loss:.2f} "
                            f"Target=₹{order.target:.2f} P&L=₹{pnl:.2f}")

            order._last_status_log = time.time()

        except Exception as e:
            self.logger.error(f"Error checking position for {order.symbol}: {e}")

    def _is_stop_loss_hit(self, order: Order, current_price: float) -> bool:
        """Check if stop-loss has been hit"""
        if order.transaction_type == TransactionType.BUY:
            # For BUY orders, stop-loss is hit when price falls below stop-loss
            return current_price <= order.stop_loss
        else:
            # For SELL orders, stop-loss is hit when price rises above stop-loss
            return current_price >= order.stop_loss

    def _is_target_hit(self, order: Order, current_price: float) -> bool:
        """Check if target has been hit"""
        if order.transaction_type == TransactionType.BUY:
            # For BUY orders, target is hit when price rises above target
            return current_price >= order.target
        else:
            # For SELL orders, target is hit when price falls below target
            return current_price <= order.target

    def _execute_immediate_exit(self, order: Order, exit_price: float, exit_reason: str, priority: ExitPriority) -> bool:
        """Execute immediate exit with enhanced error handling and retry logic"""
        symbol = order.symbol

        try:
            # Check if position is already squared off
            if hasattr(order, 'squared_off') and order.squared_off:
                self.logger.debug(f"Position {symbol} already squared off, skipping")
                return False

            # CRITICAL FIX: Prevent immediate exits within seconds to avoid rapid exit/re-entry cycles
            MIN_POSITION_TIME_SECONDS = 30  # Minimum 30 seconds before allowing exit
            if hasattr(order, 'placed_at') and order.placed_at:
                position_age = (datetime.now() - order.placed_at).total_seconds()
                if position_age < MIN_POSITION_TIME_SECONDS and exit_reason not in ["EMERGENCY_EXIT", "SQUARE_OFF_TIME"]:
                    self.logger.warning(f"⏳ Preventing immediate exit for {symbol}: position age {position_age:.1f}s < {MIN_POSITION_TIME_SECONDS}s minimum")
                    self.logger.warning(f"   Exit reason: {exit_reason} (emergency exits and square-off are allowed)")
                    return False

            # Record order placement time
            if symbol in self.execution_timings:
                self.execution_timings[symbol].order_placed_time = datetime.now()

            # Attempt exit with retry logic for critical exits
            max_attempts = self.sl_config.retry_attempts if priority in [ExitPriority.EMERGENCY, ExitPriority.STOP_LOSS] else 1

            for attempt in range(max_attempts):
                try:
                    # Pre-authenticate if enabled and this is first attempt
                    if attempt == 0 and self.sl_config.pre_auth_enabled:
                        self._ensure_api_authentication()

                    # Execute the square-off order
                    success = self.order_service.square_off_position(order, exit_price, exit_reason)

                    if success:
                        # Record successful execution
                        if symbol in self.execution_timings:
                            self.execution_timings[symbol].order_confirmed_time = datetime.now()
                            execution_time = self.execution_timings[symbol].calculate_execution_time()

                            # Calculate slippage
                            expected_price = order.stop_loss if exit_reason == "STOP_LOSS" else order.target
                            slippage = abs(exit_price - expected_price) / expected_price * 100
                            self.execution_timings[symbol].slippage = slippage

                            # Update performance metrics
                            self._update_performance_metrics(execution_time, slippage, True)

                            safe_info(self.logger, f"⚡ Fast exit executed: {symbol} in {execution_time:.1f}ms, "
                                               f"slippage: {slippage:.2f}%")

                        # Calculate final P&L
                        # CRITICAL FIX: Check for zero entry price to prevent division by zero
                        if order.entry_price == 0:
                            self.logger.error(f"Error validating P&L: float division by zero")
                            pnl = 0.0
                        else:
                            if order.transaction_type == TransactionType.BUY:
                                pnl = (exit_price - order.entry_price) * order.quantity
                            else:
                                pnl = (order.entry_price - exit_price) * order.quantity

                        # Log to trading logger if available
                        if self.trading_logger:
                            self._log_position_closure(order, exit_price, exit_reason, pnl)

                        safe_info(self.logger, f"✅ Enhanced exit completed: {symbol} {order.transaction_type.value} "
                                       f"Entry=₹{order.entry_price:.2f} Exit=₹{exit_price:.2f} "
                                       f"P&L=₹{pnl:.2f} Reason={exit_reason} Priority={priority.name}")
                        return True

                    else:
                        # Retry logic for failed attempts
                        if attempt < max_attempts - 1:
                            retry_delay = self.sl_config.retry_delay_ms * (attempt + 1)  # Exponential backoff
                            safe_warning(self.logger, f"⚠️ Exit attempt {attempt + 1} failed for {symbol}, "
                                                f"retrying in {retry_delay}ms...")
                            time.sleep(retry_delay / 1000)
                        else:
                            safe_error(self.logger, f"❌ All {max_attempts} exit attempts failed for {symbol}")
                            self._update_performance_metrics(None, None, False)
                            return False

                except Exception as attempt_error:
                    if attempt < max_attempts - 1:
                        safe_warning(self.logger, f"⚠️ Exit attempt {attempt + 1} error for {symbol}: {attempt_error}")
                        time.sleep(self.sl_config.retry_delay_ms / 1000)
                    else:
                        raise attempt_error

        except Exception as e:
            self.logger.error(f"Error in immediate exit execution for {symbol}: {e}")
            self._update_performance_metrics(None, None, False)
            return False

    def _exit_position(self, order: Order, exit_price: float, exit_reason: str) -> bool:
        """Legacy exit method - maintained for backward compatibility"""
        return self._execute_immediate_exit(order, exit_price, exit_reason, ExitPriority.TARGET)

    def _is_square_off_time(self) -> bool:
        """Check if it's time to square off all positions"""
        current_time = datetime.now().time()
        return current_time >= self.square_off_time

    def get_monitoring_status(self) -> dict:
        """Get current monitoring status"""
        open_positions = []
        try:
            open_positions = self.order_service.get_open_positions()
        except Exception as e:
            self.logger.error(f"Error getting open positions for status: {e}")

        return {
            'monitoring': self.monitoring,
            'open_positions_count': len(open_positions),
            'check_interval': self.check_interval,
            'thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False
        }

    def square_off_all_positions(self, reason: str = "MANUAL"):
        """Manually square off all open positions"""
        try:
            open_positions = self.order_service.get_open_positions()

            if not open_positions:
                self.logger.info("No open positions to square off")
                return

            self.logger.info(f"Squaring off {len(open_positions)} positions - Reason: {reason}")

            for position in open_positions:
                try:
                    # Get current price for exit
                    current_price = self.market_data_service.get_last_price(position.symbol)
                    if current_price is None:
                        current_price = position.entry_price  # Fallback to entry price
                        self.logger.warning(f"Using entry price as exit price for {position.symbol}")

                    self._exit_position(position, current_price, reason)

                except Exception as e:
                    self.logger.error(f"Error squaring off position {position.symbol}: {e}")

        except Exception as e:
            self.logger.error(f"Error in square_off_all_positions: {e}")

    # Enhanced SL mechanism helper methods

    def _update_position_tracking(self, open_positions: List[Order]):
        """Enhanced position tracking with pre-calculated trigger prices"""
        current_symbols = {pos.symbol for pos in open_positions}
        tracked_symbols = set(self.position_symbols.keys())

        # Subscribe to new symbols
        new_symbols = current_symbols - tracked_symbols
        for symbol in new_symbols:
            position = next(pos for pos in open_positions if pos.symbol == symbol)
            self.position_symbols[symbol] = position

            # Pre-calculate trigger prices for faster comparison
            self._calculate_trigger_prices(symbol, position)

            # Initialize position risk tracking
            self._initialize_position_risk(symbol, position)

            if self.websocket_service:
                self._subscribe_to_symbol(symbol, position)
                self.websocket_service.add_price_callback(symbol, self._on_price_update)

        # Unsubscribe from closed positions
        closed_symbols = tracked_symbols - current_symbols
        for symbol in closed_symbols:
            self._cleanup_position_tracking(symbol)

    def _calculate_trigger_prices(self, symbol: str, position: Order):
        """Pre-calculate trigger prices for faster SL/target detection"""
        try:
            # Store exact trigger prices for fast comparison
            if position.transaction_type == TransactionType.BUY:
                self.sl_trigger_prices[symbol] = position.stop_loss
                self.target_trigger_prices[symbol] = position.target
            else:  # SELL
                self.sl_trigger_prices[symbol] = position.stop_loss
                self.target_trigger_prices[symbol] = position.target

            safe_debug(self.logger, f"📊 Trigger prices calculated for {symbol}: "
                              f"SL=₹{self.sl_trigger_prices[symbol]:.2f}, "
                              f"Target=₹{self.target_trigger_prices[symbol]:.2f}")

        except Exception as e:
            self.logger.error(f"Error calculating trigger prices for {symbol}: {e}")

    def _is_fast_sl_hit(self, symbol: str, current_price: float) -> bool:
        """Fast stop-loss check using pre-calculated trigger prices"""
        if symbol not in self.sl_trigger_prices or symbol not in self.position_symbols:
            return False

        position = self.position_symbols[symbol]
        trigger_price = self.sl_trigger_prices[symbol]

        if position.transaction_type == TransactionType.BUY:
            return current_price <= trigger_price
        else:
            return current_price >= trigger_price

    def _is_fast_target_hit(self, symbol: str, current_price: float) -> bool:
        """Fast target check using pre-calculated trigger prices"""
        if symbol not in self.target_trigger_prices or symbol not in self.position_symbols:
            return False

        position = self.position_symbols[symbol]
        trigger_price = self.target_trigger_prices[symbol]

        if position.transaction_type == TransactionType.BUY:
            return current_price >= trigger_price
        else:
            return current_price <= trigger_price

    def _is_emergency_exit_required(self, position: Order, current_price: float) -> bool:
        """Check if emergency exit is required due to excessive loss"""
        try:
            # CRITICAL FIX: Check for zero entry price to prevent division by zero
            if position.entry_price == 0:
                self.logger.error(f"Error checking emergency exit for {position.symbol}: entry price is zero")
                return False
                
            # Calculate current loss percentage
            if position.transaction_type == TransactionType.BUY:
                loss_percent = (position.entry_price - current_price) / position.entry_price * 100
            else:
                loss_percent = (current_price - position.entry_price) / position.entry_price * 100

            # Emergency exit if loss exceeds threshold
            return loss_percent > self.sl_config.emergency_exit_threshold

        except Exception as e:
            self.logger.error(f"Error checking emergency exit for {position.symbol}: {e}")
            return False

    def _update_position_risk(self, symbol: str, current_price: float, position: Order):
        """Update real-time position risk metrics"""
        try:
            # CRITICAL FIX: Check for zero entry price to prevent division by zero
            if position.entry_price == 0:
                self.logger.error(f"Error updating position risk for {symbol}: entry price is zero")
                return
                
            # Calculate current loss percentage
            if position.transaction_type == TransactionType.BUY:
                loss_percent = (position.entry_price - current_price) / position.entry_price * 100
            else:
                loss_percent = (current_price - position.entry_price) / position.entry_price * 100

            # Calculate time in position
            time_in_position = time.time() - getattr(position, 'entry_time', time.time())

            # Simple volatility calculation (could be enhanced)
            price_change = abs(current_price - position.entry_price) / position.entry_price * 100

            # Determine risk level
            if loss_percent > self.sl_config.emergency_exit_threshold:
                risk_level = "CRITICAL"
            elif loss_percent > self.sl_config.emergency_exit_threshold * 0.7:
                risk_level = "HIGH"
            elif loss_percent > self.sl_config.emergency_exit_threshold * 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            # Update position risk
            self.position_risks[symbol] = PositionRisk(
                symbol=symbol,
                current_loss_percent=loss_percent,
                max_loss_percent=max(loss_percent, self.position_risks.get(symbol, PositionRisk(symbol, 0, 0, 0, 0, "LOW")).max_loss_percent),
                time_in_position=time_in_position,
                price_volatility=price_change,
                risk_level=risk_level
            )

        except Exception as e:
            self.logger.error(f"Error updating position risk for {symbol}: {e}")

    def _initialize_position_risk(self, symbol: str, position: Order):
        """Initialize position risk tracking"""
        self.position_risks[symbol] = PositionRisk(
            symbol=symbol,
            current_loss_percent=0.0,
            max_loss_percent=0.0,
            time_in_position=0.0,
            price_volatility=0.0,
            risk_level="LOW"
        )

    def _cleanup_position_tracking(self, symbol: str):
        """Clean up all tracking data for a closed position"""
        try:
            # Remove from all tracking dictionaries
            for tracking_dict in [
                self.position_symbols, self.live_prices, self.sl_trigger_prices,
                self.target_trigger_prices, self.execution_timings, self.position_risks
            ]:
                tracking_dict.pop(symbol, None)

            # Unsubscribe from WebSocket
            if self.websocket_service:
                self.websocket_service.unsubscribe_symbol(symbol)

            self.logger.debug(f"Cleaned up tracking for {symbol}")

        except Exception as e:
            self.logger.error(f"Error cleaning up tracking for {symbol}: {e}")

    def _ensure_api_authentication(self):
        """Ensure API is authenticated for immediate order execution"""
        try:
            if hasattr(self.order_service, '_authenticated') and not self.order_service._authenticated:
                safe_info(self.logger, "🔐 Pre-authenticating API for immediate execution...")
                self.order_service.authenticate()
        except Exception as e:
            self.logger.error(f"Error in API pre-authentication: {e}")

    def _update_performance_metrics(self, execution_time: Optional[float], slippage: Optional[float], success: bool):
        """Update performance metrics for monitoring"""
        self.total_exits += 1

        if success:
            self.successful_exits += 1
            if execution_time:
                # Update average execution time
                self.average_execution_time = (
                    (self.average_execution_time * (self.successful_exits - 1) + execution_time) / self.successful_exits
                )
            if slippage:
                self.total_slippage += slippage
        else:
            self.failed_exits += 1

    def _log_position_closure(self, order: Order, exit_price: float, exit_reason: str, pnl: float):
        """Log position closure to trading logger"""
        try:
            if self.trading_logger:
                from types import SimpleNamespace
                position = SimpleNamespace()
                position.order = order
                position.exit_price = exit_price
                position.exit_time = datetime.now()
                position.pnl = pnl
                position.exit_reason = SimpleNamespace()
                position.exit_reason.value = exit_reason
                position.exit_order_id = f"EXIT_{order.order_id}"

                self.trading_logger.log_position_closed(
                    position=position,
                    strategy="ENHANCED_POSITION_MONITOR",
                    notes=f"Enhanced position closed - {exit_reason}"
                )
        except Exception as e:
            self.logger.error(f"Error logging position closure: {e}")

    def get_performance_metrics(self) -> Dict:
        """Get enhanced performance metrics"""
        success_rate = (self.successful_exits / self.total_exits * 100) if self.total_exits > 0 else 0
        avg_slippage = (self.total_slippage / self.successful_exits) if self.successful_exits > 0 else 0

        return {
            'total_exits': self.total_exits,
            'successful_exits': self.successful_exits,
            'failed_exits': self.failed_exits,
            'success_rate_percent': success_rate,
            'average_execution_time_ms': self.average_execution_time,
            'average_slippage_percent': avg_slippage,
            'emergency_mode': self.emergency_mode,
            'positions_at_risk': len([r for r in self.position_risks.values() if r.risk_level in ['HIGH', 'CRITICAL']])
        }

    def enable_emergency_mode(self):
        """Enable emergency mode for immediate exits"""
        self.emergency_mode = True
        safe_error(self.logger, "🚨 EMERGENCY MODE ENABLED - All exits will be prioritized")

    def disable_emergency_mode(self):
        """Disable emergency mode"""
        self.emergency_mode = False
        safe_info(self.logger, "✅ Emergency mode disabled")


# Maintain backward compatibility
class PositionMonitor(EnhancedPositionMonitor):
    """Backward compatibility wrapper"""
    pass
