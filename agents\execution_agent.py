#!/usr/bin/env python3
"""
Execution Agent - Order Lifecycle Management and Trade Execution

Features:
⚙️ 1. Order Lifecycle Management
- Convert structured signals → Angel One order placement
- Auto-calculation of quantity, type (MIS), price, stop-loss, target
- Monitor order status and confirm success/failure with retry logic
- Cancel orders (SL/TP or unmatched) if signal expires or reverses
- Modify orders to update SL or target based on trailing logic

[SECURE] 2. Execution Safety Checks
- Market hours validation (9:15-15:25) with pre-market setup support
- Quantity & price validation for lot size, price steps, margin rules
- Error handling with retry on API timeout and failure logging
- Execution delay monitoring with warnings for trades >2s

[MESSAGE] 3. Order Type Management (Angel One Specific)
- MIS (Intraday) margin trading support
- LIMIT/MARKET order types with manual or auto price entry
- SL-L/SL-M stop loss trigger orders
- Comprehensive order response logging and tracking

[METRICS] 4. Trade Execution Feedback + Logging
- Order response logging with order_id, status, timestamp
- Retry queue for failed broker API calls (up to 3x)
- Trade dashboard feed for live UI or Telegram notifications
- Daily trade report storage in parquet for PnL analytics

[COMM] 5. Webhook/Async Queue Support
- Accept signal payloads from other agents via HTTP or queue
- Async order dispatch to avoid blocking system
- Order ID return with status to calling agent
- Integration with Risk Agent for pre-execution approval
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Import utility modules
try:
    from utils.angel_api import AngelOneAPIClient, MarginRequirement, RMSLimits
    from utils.paper_trading import VirtualAccount, PaperTrade
except ImportError:
    print("[WARN]  Angel API utilities not found. Some features will be disabled.")
    AngelOneAPIClient = None
    VirtualAccount = None

# Import other agents
try:
    from risk_agent import RiskManagementAgent
    from utils.risk_models import TradeRequest, TradeDirection, ProductType, OrderType
except ImportError:
    print("[WARN]  Risk Management Agent not found. Some features will be disabled.")
    RiskManagementAgent = None

# Notifications
try:
    import telegram
    from telegram import Bot
    TELEGRAM_AVAILABLE = True
except ImportError:
    print("[WARN]  Telegram not installed. Install with: pip install python-telegram-bot")
    TELEGRAM_AVAILABLE = False

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    OPEN = "OPEN"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    TRIGGER_PENDING = "TRIGGER_PENDING"
    PARTIAL = "PARTIAL"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    SL_MARKET = "SL-M"
    SL_LIMIT = "SL-L"

class ProductType(Enum):
    """Product type enumeration"""
    MIS = "MIS"  # Intraday
    CNC = "CNC"  # Cash and Carry
    NRML = "NRML"  # Normal

class TransactionType(Enum):
    """Transaction type enumeration"""
    BUY = "BUY"
    SELL = "SELL"

@dataclass
class SignalPayload:
    """Signal payload from Signal Generation Agent"""
    symbol: str
    exchange: str
    symbol_token: str
    action: str  # BUY/SELL
    entry_price: float
    sl_price: float
    target_price: float
    quantity: int
    order_type: str = "LIMIT"
    product_type: str = "MIS"
    strategy_name: str = ""
    signal_id: str = ""
    timestamp: datetime = None
    risk_reward_ratio: float = 0.0
    confidence_score: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class OrderRequest:
    """Order request structure for Angel One API"""
    variety: str = "NORMAL"
    tradingsymbol: str = ""
    symboltoken: str = ""
    transactiontype: str = ""
    exchange: str = ""
    ordertype: str = ""
    producttype: str = ""
    duration: str = "DAY"
    price: str = "0"
    squareoff: str = "0"
    stoploss: str = "0"
    quantity: str = "0"
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary for API call"""
        return asdict(self)

@dataclass
class OrderResponse:
    """Order response from Angel One API"""
    order_id: str
    status: str
    message: str
    timestamp: datetime
    signal_id: str = ""
    order_request: Optional[OrderRequest] = None
    execution_time_ms: float = 0.0
    slippage_percent: float = 0.0
    
@dataclass
class TradeExecution:
    """Complete trade execution record"""
    signal_payload: SignalPayload
    entry_order: Optional[OrderResponse] = None
    sl_order: Optional[OrderResponse] = None
    target_order: Optional[OrderResponse] = None
    status: str = "PENDING"
    created_at: datetime = None
    updated_at: datetime = None
    execution_summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.execution_summary is None:
            self.execution_summary = {}

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] EXECUTION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class ExecutionAgent:
    """
    Execution Agent for order lifecycle management and trade execution
    
    Handles:
    - Signal processing and order placement
    - Order status monitoring and management
    - Risk validation and safety checks
    - Execution feedback and logging
    - Integration with Angel One SmartAPI
    """
    
    def __init__(self, config_path: str = "config/execution_config.yaml",
                 trading_mode: str = "paper", trading_config: Dict[str, Any] = None):
        """Initialize Execution Agent"""

        # Load configuration
        self.config = self._load_config(config_path)

        # Trading mode configuration
        self.trading_mode = trading_mode
        self.trading_config = trading_config or {}
        self.paper_trading_enabled = trading_mode == "paper"

        # Initialize components
        self.angel_api = None
        self.risk_agent = None
        self.telegram_bot = None
        self.virtual_account = None

        # Order tracking
        self.active_orders: Dict[str, TradeExecution] = {}
        self.order_history: List[TradeExecution] = []
        self.retry_queue: deque = deque()

        # Performance tracking
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'avg_execution_time_ms': 0.0,
            'avg_slippage_percent': 0.0
        }

        # Market hours
        self.market_open_time = time(9, 15)  # 9:15 AM
        self.market_close_time = time(15, 25)  # 3:25 PM
        self.pre_market_start = time(9, 0)  # 9:00 AM for pre-market setup

        # Initialize logging
        self._setup_logging()

        logger.info(f"[INIT] Execution Agent initialized in {trading_mode.upper()} mode")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file with environment variable resolution"""
        try:
            # Use ConfigurationLoader for proper environment variable resolution
            from utils.config_loader import ConfigurationLoader
            config_loader = ConfigurationLoader()
            config = config_loader.load_agent_config(config_path)
            logger.info(f"[SUCCESS] Configuration loaded from {config_path}")
            return config
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config from {config_path}: {e}")
            return {}
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO'))
        
        # Configure logger
        logger.setLevel(log_level)
        
        # File handler
        if log_config.get('file_enabled', True):
            log_file = log_config.get('file_path', 'logs/execution_agent.log')
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize based on trading mode
            if self.paper_trading_enabled:
                # Initialize Virtual Account for paper trading
                if VirtualAccount:
                    self.virtual_account = VirtualAccount(self.trading_config)
                    logger.info("[SUCCESS] Virtual Account initialized for paper trading")
                else:
                    logger.error("[ERROR] VirtualAccount not available for paper trading")
                    return False
            else:
                # Initialize Angel One API client for real trading
                if AngelOneAPIClient and self.config.get('angel_one_api', {}).get('enabled', True):
                    self.angel_api = AngelOneAPIClient(self.config)
                    auth_success = await self.angel_api.authenticate()
                    if not auth_success:
                        logger.error("[ERROR] Failed to authenticate with Angel One API")
                        return False
                    logger.info("[SUCCESS] Angel One API client initialized and authenticated")

            # Initialize Risk Management Agent
            if RiskManagementAgent and self.config.get('risk_management', {}).get('enabled', True):
                risk_config_path = self.config.get('risk_management', {}).get('config_path', 'config/risk_management_config.yaml')
                self.risk_agent = RiskManagementAgent(risk_config_path)
                # Pass trading mode to risk agent
                self.risk_agent.paper_trading_enabled = self.paper_trading_enabled
                await self.risk_agent.setup()
                logger.info("[SUCCESS] Risk Management Agent initialized")

            # Initialize Telegram bot
            if TELEGRAM_AVAILABLE and self.config.get('notifications', {}).get('telegram', {}).get('enabled', False):
                bot_token = self.config['notifications']['telegram']['bot_token']
                self.telegram_bot = Bot(token=bot_token)
                logger.info("[SUCCESS] Telegram bot initialized")

            # Download historical data and prepare signals for live trading
            await self._prepare_for_live_trading()

            # Start background tasks
            asyncio.create_task(self._order_monitoring_loop())
            asyncio.create_task(self._retry_queue_processor())

            logger.info("[INIT] Execution Agent fully initialized")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Execution Agent: {e}")
            return False

    async def _prepare_for_live_trading(self):
        """Prepare for live trading by downloading historical data and generating signals"""
        try:
            logger.info("[PREP] Preparing for live trading session...")

            # Check if we have recent signals
            signal_files = list(Path("data/signals").glob("*.parquet")) if Path("data/signals").exists() else []

            if not signal_files:
                logger.info("[PREP] No recent signals found, generating new signals...")
                # In a real implementation, this would trigger signal generation
                # For now, we'll just log that signals would be generated
                logger.info("[PREP] Signal generation would be triggered here")
            else:
                logger.info(f"[PREP] Found {len(signal_files)} signal files")

            # Prepare trading session
            logger.info("[PREP] Trading session preparation completed")

        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare for live trading: {e}")
            raise

    def _is_market_hours(self) -> bool:
        """Check if current time is within market hours"""
        current_time = datetime.now().time()

        # Allow pre-market setup
        if self.config.get('market_hours', {}).get('allow_pre_market', True):
            return self.pre_market_start <= current_time <= self.market_close_time
        else:
            return self.market_open_time <= current_time <= self.market_close_time

    def _validate_signal(self, signal: SignalPayload) -> Tuple[bool, str]:
        """Validate incoming signal"""
        try:
            # Basic validation
            if not signal.symbol or not signal.exchange:
                return False, "Missing symbol or exchange"

            if signal.quantity <= 0:
                return False, "Invalid quantity"

            if signal.entry_price <= 0:
                return False, "Invalid entry price"

            # Market hours check
            if not self._is_market_hours():
                return False, f"Outside market hours. Current time: {datetime.now().time()}"

            # Risk-reward validation
            min_rr = self.config.get('risk_management', {}).get('min_rr_ratio', 1.5)
            if signal.action == "BUY":
                rr_ratio = (signal.target_price - signal.entry_price) / (signal.entry_price - signal.sl_price)
            else:
                rr_ratio = (signal.entry_price - signal.target_price) / (signal.sl_price - signal.entry_price)

            if rr_ratio < min_rr:
                return False, f"Risk-reward ratio {rr_ratio:.2f} below minimum {min_rr}"

            return True, "Signal validation passed"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    async def process_signal(self, signal: SignalPayload) -> Tuple[bool, str, Optional[TradeExecution]]:
        """
        Process incoming signal and execute trade

        Args:
            signal: Signal payload from Signal Generation Agent

        Returns:
            Tuple of (success, message, trade_execution)
        """
        try:
            logger.info(f"📥 Processing signal: {signal.symbol} {signal.action} @ {signal.entry_price}")

            # Validate signal
            is_valid, validation_msg = self._validate_signal(signal)
            if not is_valid:
                logger.warning(f"[WARN]  Signal validation failed: {validation_msg}")
                return False, validation_msg, None

            # Risk management validation
            if self.risk_agent:
                trade_request = TradeRequest(
                    signal_id=getattr(signal, 'signal_id', f"EXEC_{signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                    symbol=signal.symbol,
                    exchange=signal.exchange,
                    quantity=signal.quantity,
                    entry_price=signal.entry_price,
                    stop_loss=signal.sl_price,
                    take_profit=getattr(signal, 'target_price', signal.sl_price),  # Use target_price if available
                    direction=TradeDirection.LONG if signal.action == "BUY" else TradeDirection.SHORT,
                    product_type=ProductType.MIS,
                    order_type=OrderType.MARKET,  # Add missing order_type
                    strategy_name=signal.strategy_name,
                    risk_amount=getattr(signal, 'risk_amount', abs(signal.entry_price - signal.sl_price) * signal.quantity),
                    capital_allocated=getattr(signal, 'capital_allocated', 50000.0),
                    risk_reward_ratio=getattr(signal, 'risk_reward_ratio', 2.0)
                )

                risk_approved = await self.risk_agent.validate_trade(trade_request)
                if not risk_approved.is_valid:
                    logger.warning(f"[WARN]  Risk validation failed: {risk_approved.rejection_reason}")
                    return False, f"Risk validation failed: {risk_approved.rejection_reason}", None

            # Margin validation for real trading
            if not self.paper_trading_enabled and self.angel_api:
                margin_valid, margin_msg = await self._validate_margin_requirement(signal)
                if not margin_valid:
                    logger.warning(f"[WARN]  Margin validation failed: {margin_msg}")
                    return False, f"Margin validation: {margin_msg}", None

            # Create trade execution record
            trade_execution = TradeExecution(signal_payload=signal)

            # Place entry order (paper or real)
            if self.paper_trading_enabled:
                entry_success, entry_response = await self._place_paper_order(signal)
            else:
                entry_success, entry_response = await self._place_entry_order(signal)

            if not entry_success:
                logger.error(f"[ERROR] Failed to place entry order: {entry_response}")
                return False, f"Entry order failed: {entry_response}", trade_execution

            trade_execution.entry_order = entry_response
            trade_execution.status = "ENTRY_PLACED"

            # Place stop loss order
            if signal.sl_price > 0:
                sl_success, sl_response = await self._place_stop_loss_order(signal, entry_response.order_id)
                if sl_success:
                    trade_execution.sl_order = sl_response
                else:
                    logger.warning(f"[WARN]  Failed to place SL order: {sl_response}")

            # Place target order
            if signal.target_price > 0:
                target_success, target_response = await self._place_target_order(signal, entry_response.order_id)
                if target_success:
                    trade_execution.target_order = target_response
                else:
                    logger.warning(f"[WARN]  Failed to place target order: {target_response}")

            # Track the trade
            self.active_orders[entry_response.order_id] = trade_execution

            # Send notification
            await self._send_notification(f"[SUCCESS] Trade executed: {signal.symbol} {signal.action} @ {signal.entry_price}")

            # Update statistics
            self.execution_stats['total_orders'] += 1
            self.execution_stats['successful_orders'] += 1

            logger.info(f"[SUCCESS] Signal processed successfully: Order ID {entry_response.order_id}")
            return True, f"Trade executed successfully: {entry_response.order_id}", trade_execution

        except Exception as e:
            logger.error(f"[ERROR] Error processing signal: {e}")
            self.execution_stats['total_orders'] += 1
            self.execution_stats['failed_orders'] += 1
            return False, f"Processing error: {str(e)}", None

    async def _place_entry_order(self, signal: SignalPayload) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place entry order"""
        try:
            start_time = datetime.now()

            # Prepare order parameters
            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=signal.action,
                exchange=signal.exchange,
                ordertype=signal.order_type,
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.entry_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            # Place order via Angel One API
            if not self.angel_api:
                return False, "Angel One API not initialized"

            response = self.angel_api.smart_api.placeOrder(order_request.to_dict())

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'Order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request,
                    execution_time_ms=execution_time
                )

                logger.info(f"[SUCCESS] Entry order placed: {order_id} in {execution_time:.2f}ms")

                # Check for execution delay
                if execution_time > 2000:  # 2 seconds
                    logger.warning(f"[WARN]  Slow execution detected: {execution_time:.2f}ms")

                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Entry order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing entry order: {e}")
            return False, str(e)

    async def _place_stop_loss_order(self, signal: SignalPayload, parent_order_id: str) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place stop loss order"""
        try:
            # Determine SL order type and transaction type
            sl_transaction_type = "SELL" if signal.action == "BUY" else "BUY"

            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=sl_transaction_type,
                exchange=signal.exchange,
                ordertype="SL-M",  # Stop Loss Market order
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.sl_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            response = self.angel_api.smart_api.placeOrder(order_request.to_dict())

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'SL order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request
                )

                logger.info(f"[SUCCESS] Stop loss order placed: {order_id}")
                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Stop loss order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing stop loss order: {e}")
            return False, str(e)

    async def _place_target_order(self, signal: SignalPayload, parent_order_id: str) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place target order"""
        try:
            # Determine target transaction type
            target_transaction_type = "SELL" if signal.action == "BUY" else "BUY"

            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=target_transaction_type,
                exchange=signal.exchange,
                ordertype="LIMIT",
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.target_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            response = self.angel_api.smart_api.placeOrder(order_request.to_dict())

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'Target order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request
                )

                logger.info(f"[SUCCESS] Target order placed: {order_id}")
                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Target order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing target order: {e}")
            return False, str(e)

    async def _place_paper_order(self, signal: SignalPayload) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place paper trading order using virtual account"""
        try:
            if not self.virtual_account:
                return False, "Virtual account not initialized"

            start_time = datetime.now()

            # Execute paper trade
            success, message, paper_trade = await self.virtual_account.execute_trade(
                symbol=signal.symbol,
                exchange=signal.exchange,
                quantity=signal.quantity,
                price=signal.entry_price,
                transaction_type=signal.action,
                order_type=signal.order_type,
                product_type=signal.product_type,
                strategy_name=signal.strategy_name,
                signal_id=signal.signal_id
            )

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            if success and paper_trade:
                # Create order response for consistency
                order_response = OrderResponse(
                    order_id=paper_trade.trade_id,
                    status="EXECUTED",
                    message=message,
                    timestamp=paper_trade.timestamp,
                    signal_id=signal.signal_id,
                    execution_time_ms=execution_time,
                    slippage_percent=0.0  # No slippage in paper trading
                )

                logger.info(f"[SUCCESS] Paper trade executed: {paper_trade.trade_id}")
                logger.info(f"   {signal.action} {signal.quantity} {signal.symbol} @ Rs.{signal.entry_price:.2f}")
                logger.info(f"   Charges: Rs.{paper_trade.total_charges:.2f}")
                logger.info(f"   Net Amount: Rs.{paper_trade.net_amount:.2f}")

                # Get account summary for logging
                account_summary = self.virtual_account.get_account_summary()
                logger.info(f"   Account Balance: Rs.{account_summary.get('current_balance', 0):,.2f}")
                logger.info(f"   Available Margin: Rs.{account_summary.get('available_margin', 0):,.2f}")
                logger.info(f"   Trades Today: {account_summary.get('today_trades', 0)}/{account_summary.get('trades_remaining_today', 0) + account_summary.get('today_trades', 0)}")

                return True, order_response
            else:
                logger.error(f"[ERROR] Paper trade failed: {message}")
                return False, message

        except Exception as e:
            logger.error(f"[ERROR] Error placing paper order: {e}")
            return False, str(e)

    async def modify_order(self, order_id: str, new_price: float, new_quantity: int = None) -> Tuple[bool, str]:
        """Modify existing order"""
        try:
            if order_id not in self.active_orders:
                return False, "Order not found in active orders"

            trade_execution = self.active_orders[order_id]
            original_order = trade_execution.entry_order.order_request

            modify_params = {
                "variety": "NORMAL",
                "orderid": order_id,
                "ordertype": original_order.ordertype,
                "producttype": original_order.producttype,
                "duration": "DAY",
                "price": str(new_price),
                "quantity": str(new_quantity or original_order.quantity),
                "tradingsymbol": original_order.tradingsymbol,
                "symboltoken": original_order.symboltoken,
                "exchange": original_order.exchange
            }

            response = self.angel_api.smart_api.modifyOrder(modify_params)

            if response.get('status'):
                logger.info(f"[SUCCESS] Order modified: {order_id} - New price: {new_price}")
                return True, "Order modified successfully"
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Order modification failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error modifying order: {e}")
            return False, str(e)

    async def cancel_order(self, order_id: str, reason: str = "Manual cancellation") -> Tuple[bool, str]:
        """Cancel existing order"""
        try:
            if order_id not in self.active_orders:
                return False, "Order not found in active orders"

            cancel_params = {
                "variety": "NORMAL",
                "orderid": order_id
            }

            response = self.angel_api.smart_api.cancelOrder(cancel_params)

            if response.get('status'):
                # Update trade execution status
                trade_execution = self.active_orders[order_id]
                trade_execution.status = "CANCELLED"
                trade_execution.updated_at = datetime.now()

                # Move to history
                self.order_history.append(trade_execution)
                del self.active_orders[order_id]

                logger.info(f"[SUCCESS] Order cancelled: {order_id} - Reason: {reason}")
                await self._send_notification(f"[ERROR] Order cancelled: {order_id} - {reason}")

                return True, "Order cancelled successfully"
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Order cancellation failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error cancelling order: {e}")
            return False, str(e)

    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get current order status from broker"""
        try:
            response = self.angel_api.smart_api.orderBook()

            if response.get('status'):
                orders = response.get('data', [])
                for order in orders:
                    if order.get('orderid') == order_id:
                        return order

            return None

        except Exception as e:
            logger.error(f"[ERROR] Error fetching order status: {e}")
            return None

    async def _order_monitoring_loop(self):
        """Background task to monitor active orders"""
        while True:
            try:
                if not self.active_orders:
                    await asyncio.sleep(30)  # Check every 30 seconds when no active orders
                    continue

                # Check status of all active orders
                for order_id, trade_execution in list(self.active_orders.items()):
                    order_status = await self.get_order_status(order_id)

                    if order_status:
                        status = order_status.get('orderstatus', '').upper()

                        if status == 'COMPLETE':
                            # Order filled
                            await self._handle_order_filled(order_id, trade_execution, order_status)
                        elif status in ['CANCELLED', 'REJECTED']:
                            # Order cancelled or rejected
                            await self._handle_order_failed(order_id, trade_execution, order_status)

                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"[ERROR] Error in order monitoring loop: {e}")
                await asyncio.sleep(30)

    async def _handle_order_filled(self, order_id: str, trade_execution: TradeExecution, order_status: Dict[str, Any]):
        """Handle filled order"""
        try:
            fill_price = float(order_status.get('averageprice', 0))
            fill_quantity = int(order_status.get('filledshares', 0))

            # Calculate slippage
            expected_price = trade_execution.signal_payload.entry_price
            slippage_percent = abs(fill_price - expected_price) / expected_price * 100

            # Update trade execution
            trade_execution.entry_order.slippage_percent = slippage_percent
            trade_execution.status = "FILLED"
            trade_execution.updated_at = datetime.now()

            # Update statistics
            self._update_execution_stats(trade_execution.entry_order.execution_time_ms, slippage_percent)

            logger.info(f"[SUCCESS] Order filled: {order_id} @ {fill_price} (Slippage: {slippage_percent:.2f}%)")

            # Send notification
            await self._send_notification(
                f"[SUCCESS] Order filled: {trade_execution.signal_payload.symbol} @ {fill_price} "
                f"(Slippage: {slippage_percent:.2f}%)"
            )

            # Check slippage threshold
            max_slippage = self.config.get('execution', {}).get('max_slippage_percent', 0.5)
            if slippage_percent > max_slippage:
                logger.warning(f"[WARN]  High slippage detected: {slippage_percent:.2f}% > {max_slippage}%")

        except Exception as e:
            logger.error(f"[ERROR] Error handling filled order: {e}")

    async def _handle_order_failed(self, order_id: str, trade_execution: TradeExecution, order_status: Dict[str, Any]):
        """Handle failed/cancelled order"""
        try:
            status = order_status.get('orderstatus', '').upper()
            reason = order_status.get('text', 'Unknown reason')

            # Update trade execution
            trade_execution.status = status
            trade_execution.updated_at = datetime.now()

            # Move to history
            self.order_history.append(trade_execution)
            del self.active_orders[order_id]

            logger.warning(f"[WARN]  Order {status.lower()}: {order_id} - {reason}")

            # Send notification
            await self._send_notification(f"[ERROR] Order {status.lower()}: {order_id} - {reason}")

            # Add to retry queue if appropriate
            if status == 'REJECTED' and self.config.get('execution', {}).get('auto_retry', True):
                self.retry_queue.append((trade_execution.signal_payload, datetime.now()))
                logger.info(f"[WORKFLOW] Added to retry queue: {order_id}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling failed order: {e}")

    async def _retry_queue_processor(self):
        """Background task to process retry queue"""
        while True:
            try:
                if not self.retry_queue:
                    await asyncio.sleep(60)  # Check every minute
                    continue

                # Process retry queue
                retry_delay_minutes = self.config.get('execution', {}).get('retry_delay_minutes', 5)
                max_retries = self.config.get('execution', {}).get('max_retries', 3)

                current_time = datetime.now()

                while self.retry_queue:
                    signal, retry_time = self.retry_queue[0]

                    # Check if enough time has passed
                    if (current_time - retry_time).total_seconds() < retry_delay_minutes * 60:
                        break

                    # Remove from queue
                    self.retry_queue.popleft()

                    # Check retry count
                    retry_count = getattr(signal, 'retry_count', 0)
                    if retry_count >= max_retries:
                        logger.warning(f"[WARN]  Max retries exceeded for signal: {signal.signal_id}")
                        continue

                    # Increment retry count
                    signal.retry_count = retry_count + 1

                    # Retry signal processing
                    logger.info(f"[WORKFLOW] Retrying signal: {signal.signal_id} (Attempt {signal.retry_count})")
                    success, message, _ = await self.process_signal(signal)

                    if not success:
                        logger.warning(f"[WARN]  Retry failed: {message}")

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"[ERROR] Error in retry queue processor: {e}")
                await asyncio.sleep(60)

    def _update_execution_stats(self, execution_time_ms: float, slippage_percent: float):
        """Update execution statistics"""
        try:
            # Update average execution time
            total_orders = self.execution_stats['successful_orders']
            current_avg_time = self.execution_stats['avg_execution_time_ms']

            new_avg_time = ((current_avg_time * (total_orders - 1)) + execution_time_ms) / total_orders
            self.execution_stats['avg_execution_time_ms'] = new_avg_time

            # Update average slippage
            current_avg_slippage = self.execution_stats['avg_slippage_percent']
            new_avg_slippage = ((current_avg_slippage * (total_orders - 1)) + slippage_percent) / total_orders
            self.execution_stats['avg_slippage_percent'] = new_avg_slippage

        except Exception as e:
            logger.error(f"[ERROR] Error updating execution stats: {e}")

    async def _send_notification(self, message: str):
        """Send notification via configured channels"""
        try:
            # Telegram notification
            if self.telegram_bot:
                chat_id = self.config['notifications']['telegram']['chat_id']
                await self.telegram_bot.send_message(chat_id=chat_id, text=message)

            # Log notification
            logger.info(f"[NOTIFY] Notification: {message}")

        except Exception as e:
            logger.error(f"[ERROR] Error sending notification: {e}")

    async def get_execution_summary(self) -> Dict[str, Any]:
        """Get execution summary and statistics"""
        try:
            return {
                'statistics': self.execution_stats.copy(),
                'active_orders_count': len(self.active_orders),
                'total_history_count': len(self.order_history),
                'retry_queue_size': len(self.retry_queue),
                'last_updated': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"[ERROR] Error getting execution summary: {e}")
            return {}

    async def save_trade_data(self, file_path: str = None):
        """Save trade execution data to parquet file"""
        try:
            if not file_path:
                file_path = f"data/execution/trades_{datetime.now().strftime('%Y%m%d')}.parquet"

            # Prepare data for saving
            trade_records = []

            # Add active orders
            for trade_execution in self.active_orders.values():
                trade_records.append(self._trade_execution_to_dict(trade_execution))

            # Add historical orders
            for trade_execution in self.order_history:
                trade_records.append(self._trade_execution_to_dict(trade_execution))

            if trade_records:
                # Create DataFrame and save
                df = pl.DataFrame(trade_records)

                # Ensure directory exists
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # Save to parquet
                df.write_parquet(file_path, compression="snappy")

                logger.info(f"[SUCCESS] Trade data saved: {file_path} ({len(trade_records)} records)")
            else:
                logger.info("[INFO] No trade data to save")

        except Exception as e:
            logger.error(f"[ERROR] Error saving trade data: {e}")

    def _trade_execution_to_dict(self, trade_execution: TradeExecution) -> Dict[str, Any]:
        """Convert TradeExecution to dictionary for storage"""
        try:
            signal = trade_execution.signal_payload
            entry_order = trade_execution.entry_order

            return {
                'signal_id': signal.signal_id,
                'symbol': signal.symbol,
                'exchange': signal.exchange,
                'action': signal.action,
                'entry_price': signal.entry_price,
                'sl_price': signal.sl_price,
                'target_price': signal.target_price,
                'quantity': signal.quantity,
                'strategy_name': signal.strategy_name,
                'order_id': entry_order.order_id if entry_order else '',
                'status': trade_execution.status,
                'execution_time_ms': entry_order.execution_time_ms if entry_order else 0.0,
                'slippage_percent': entry_order.slippage_percent if entry_order else 0.0,
                'created_at': trade_execution.created_at.isoformat(),
                'updated_at': trade_execution.updated_at.isoformat()
            }
        except Exception as e:
            logger.error(f"[ERROR] Error converting trade execution to dict: {e}")
            return {}

    async def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        try:
            stats = self.execution_stats.copy()

            # Calculate success rate
            if stats['total_orders'] > 0:
                stats['success_rate'] = (stats['successful_orders'] / stats['total_orders']) * 100
            else:
                stats['success_rate'] = 0.0

            # Add active orders count
            stats['active_orders'] = len(self.active_orders)

            # Add market status
            stats['market_open'] = self._is_market_open()

            # Add trading mode specific stats
            stats['trading_mode'] = self.trading_mode

            if self.paper_trading_enabled and self.virtual_account:
                # Add paper trading stats
                account_summary = self.virtual_account.get_account_summary()
                stats.update({
                    'paper_trading': {
                        'account_balance': account_summary.get('current_balance', 0),
                        'available_margin': account_summary.get('available_margin', 0),
                        'total_pnl': account_summary.get('total_pnl', 0),
                        'return_percent': account_summary.get('return_percent', 0),
                        'total_trades': account_summary.get('total_trades', 0),
                        'active_positions': account_summary.get('active_positions', 0),
                        'today_trades': account_summary.get('today_trades', 0),
                        'trades_remaining_today': account_summary.get('trades_remaining_today', 0)
                    }
                })

            return stats

        except Exception as e:
            logger.error(f"[ERROR] Error getting execution stats: {e}")
            return {}

    async def get_account_status(self) -> Dict[str, Any]:
        """Get account status (paper or real)"""
        try:
            if self.paper_trading_enabled and self.virtual_account:
                # Return paper trading account status
                account_summary = self.virtual_account.get_account_summary()
                positions_summary = self.virtual_account.get_positions_summary()

                return {
                    'trading_mode': 'paper',
                    'account_summary': account_summary,
                    'positions': positions_summary,
                    'timestamp': datetime.now().isoformat()
                }
            elif self.angel_api:
                # Return real trading account status
                rms_limits = await self.angel_api.get_rms_limits()
                positions = await self.angel_api.get_positions()
                funds = await self.angel_api.get_funds()

                return {
                    'trading_mode': 'real',
                    'rms_limits': rms_limits.__dict__ if rms_limits else {},
                    'positions': [pos.__dict__ for pos in positions] if positions else [],
                    'funds': funds.__dict__ if funds else {},
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'trading_mode': self.trading_mode,
                    'status': 'not_initialized',
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"[ERROR] Error getting account status: {e}")
            return {'error': str(e)}

    async def reset_paper_account(self) -> bool:
        """Reset paper trading account (only in paper mode)"""
        try:
            if not self.paper_trading_enabled:
                logger.warning("[WARN] Reset only available in paper trading mode")
                return False

            if not self.virtual_account:
                logger.error("[ERROR] Virtual account not initialized")
                return False

            self.virtual_account.reset_account()

            # Clear active orders and history
            self.active_orders.clear()
            self.order_history.clear()

            # Reset execution stats
            self.execution_stats = {
                'total_orders': 0,
                'successful_orders': 0,
                'failed_orders': 0,
                'avg_execution_time_ms': 0.0,
                'avg_slippage_percent': 0.0
            }

            logger.info("[SUCCESS] Paper trading account reset successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error resetting paper account: {e}")
            return False

    async def _validate_margin_requirement(self, signal: SignalPayload) -> Tuple[bool, str]:
        """
        Validate margin requirement for real trading

        Args:
            signal: Signal payload to validate

        Returns:
            Tuple of (is_valid, message)
        """
        try:
            if not self.angel_api:
                return False, "Angel API not initialized"

            # Get margin requirement
            margin_req = await self.angel_api.get_margin_requirement(
                symbol=signal.symbol,
                exchange=signal.exchange,
                quantity=signal.quantity,
                price=signal.entry_price,
                transaction_type=signal.action,
                product_type=signal.product_type
            )

            if not margin_req:
                return False, "Failed to get margin requirement"

            if margin_req.error_message:
                return False, f"Margin API error: {margin_req.error_message}"

            # Check if trade is allowed
            if not margin_req.is_allowed:
                return False, f"Insufficient margin. Required: Rs.{margin_req.margin_required:,.2f}, Available: Rs.{margin_req.available_margin:,.2f}"

            # Check margin utilization (warn if > 80%)
            if margin_req.limit_used_percent > 80:
                logger.warning(f"[WARN]  High margin utilization: {margin_req.limit_used_percent:.1f}%")

            logger.info(f"[SUCCESS] Margin validation passed for {signal.symbol}")
            logger.info(f"   Margin Required: Rs.{margin_req.margin_required:,.2f}")
            logger.info(f"   Available Margin: Rs.{margin_req.available_margin:,.2f}")
            logger.info(f"   Utilization: {margin_req.limit_used_percent:.1f}%")

            return True, "Margin validation successful"

        except Exception as e:
            logger.error(f"[ERROR] Error validating margin requirement: {e}")
            return False, f"Margin validation error: {str(e)}"

    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Save final trade data
            await self.save_trade_data()

            # Close Angel One API session
            if self.angel_api:
                await self.angel_api.close_session()

            logger.info("[SUCCESS] Execution Agent cleanup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main function for testing"""
    try:
        # Initialize execution agent
        agent = ExecutionAgent()
        await agent.initialize()

        # Example signal
        signal = SignalPayload(
            symbol="RELIANCE-EQ",
            exchange="NSE",
            symbol_token="2885",
            action="BUY",
            entry_price=2780.0,
            sl_price=2765.0,
            target_price=2815.0,
            quantity=1,
            strategy_name="test_strategy",
            signal_id="test_001"
        )

        # Process signal
        success, message, trade_execution = await agent.process_signal(signal)
        print(f"Signal processing result: {success} - {message}")

        # Keep running for monitoring
        await asyncio.sleep(300)  # Run for 5 minutes

        # Cleanup
        await agent.cleanup()

    except Exception as e:
        logger.error(f"[ERROR] Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
