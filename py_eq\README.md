# Python Equity Trading System

## Overview
This system is designed for intraday equity trading using various strategies. It integrates with MongoDB for symbol mappings and uses stocks_to_monitor.csv for the list of stocks to trade.

## Key Features
- Reads stock list from `data/stocks_to_monitor.csv`
- Uses MongoDB for symbol mappings and token numbers
- Integrates with SmartAPI (Angel One) for trading
- Supports multiple trading strategies:
  - MA Crossover
  - Support/Resistance
  - Opening Range Breakout (ORB)
  - Gap and Go

## Configuration
- Environment variables are stored in `.env`
- MongoDB connection settings:
  - `MONGODB_CONNECTION_STRING`: MongoDB connection string (default: mongodb://localhost:27017/)
  - `MONGODB_DATABASE_NAME`: Database name (default: trading_db)

## Stock List Management
Stocks are managed in `data/stocks_to_monitor.csv` with the following format:
```
symbol,strategy,timeframe,enabled,notes
RELIANCE,Support_Resistance,15min,true,Win: 100.0% (4/0) - 4 trades
HDFCBANK,MA_Crossover,15min,true,Win: 100.0% (3/0) - 3 trades
```

## Symbol Mapping
Symbol mappings are stored in MongoDB with the following collections:
- `symbol_mappings_equity`: Equity symbol mappings
- `symbol_mappings_options`: Option symbol mappings
- `symbol_mappings_indices`: Index symbol mappings
- `stock_categories`: Stock categorization (NIFTY50, NIFTY100, etc.)

## Architecture
- `StockSymbolIntegrationService`: Integrates stocks_to_monitor.csv with MongoDB symbol mappings
- `EfficientMarketDataService`: Provides market data with MongoDB caching
- `ProductionStrategyManager`: Manages trading strategies and executes trades

## Usage
1. Ensure MongoDB is running
2. Update stocks_to_monitor.csv with desired stocks
3. Run `python main.py` to start the trading system

## Verification Scripts
The following scripts can be used to verify the integration:

### Download Symbol Mappings
```bash
cd scripts
python download_symbol_mappings.py
```
This script downloads symbol mappings for stocks in stocks_to_monitor.csv and stores them in MongoDB.

### Test Symbol Integration
```bash
cd scripts
python test_symbol_integration.py
```
This script tests the integration between stocks_to_monitor.csv and MongoDB symbol mappings.

### Verify Integration
```bash
cd scripts
python verify_integration.py
```
This script verifies that the system is using stocks_to_monitor.csv and MongoDB correctly.

## Dependencies
- MongoDB
- SmartAPI (Angel One)
- pandas, numpy, etc.