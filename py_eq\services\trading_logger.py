"""
Trading logger service for comprehensive trade logging
Similar to Golang implementation
"""
import logging
import os
import csv
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from models.order import Order, Position
from models.signal import Signal

# Import Telegram notifier for real-time notifications
try:
    from .telegram_notifier import notify_order_placed, notify_position_update, is_telegram_available
except ImportError:
    # Fallback if telegram notifier is not available
    def notify_order_placed(*args, **kwargs):
        pass
    def notify_position_update(*args, **kwargs):
        pass
    def is_telegram_available():
        return False


@dataclass
class TradeLogEntry:
    """Trade log entry structure"""
    timestamp: datetime
    symbol: str
    signal_type: str
    strategy: str
    action: str  # ENTRY, EXIT, SIGNAL_GENERATED, etc.
    price: float
    quantity: int
    stop_loss: float
    target: float
    order_id: str
    status: str
    pnl: float = 0.0
    notes: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for CSV writing"""
        return {
            'timestamp': self.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'strategy': self.strategy,
            'action': self.action,
            'price': self.price,
            'quantity': self.quantity,
            'stop_loss': self.stop_loss,
            'target': self.target,
            'order_id': self.order_id,
            'status': self.status,
            'pnl': self.pnl,
            'notes': self.notes
        }


class TradingLogger:
    """Trading logger for comprehensive trade tracking"""

    def __init__(self, log_directory: str = "logs", logger: Optional[logging.Logger] = None):
        self.log_directory = log_directory
        self.logger = logger or logging.getLogger(__name__)

        # Create logs directory if it doesn't exist
        os.makedirs(log_directory, exist_ok=True)

        # Generate log file names based on current date
        today = datetime.now().strftime('%Y-%m-%d')
        self.trade_log_file = os.path.join(log_directory, f"trades_{today}.csv")
        self.signal_log_file = os.path.join(log_directory, f"signals_{today}.csv")
        self.performance_log_file = os.path.join(log_directory, f"performance_{today}.csv")

        # Initialize CSV files with headers
        self._initialize_csv_files()

        # Track daily statistics
        self.daily_stats = {
            'total_signals': 0,
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'max_profit': 0.0
        }

    def _initialize_csv_files(self):
        """Initialize CSV files with headers if they don't exist"""

        # Trade log headers
        trade_headers = [
            'timestamp', 'symbol', 'signal_type', 'strategy', 'action',
            'price', 'quantity', 'stop_loss', 'target', 'order_id',
            'status', 'pnl', 'notes'
        ]

        # Signal log headers
        signal_headers = [
            'timestamp', 'symbol', 'signal_type', 'timeframe', 'strategy',
            'indicators', 'conditions_met', 'action_taken', 'notes'
        ]

        # Performance log headers
        performance_headers = [
            'timestamp', 'total_signals', 'total_trades', 'successful_trades',
            'success_rate', 'total_pnl', 'avg_pnl_per_trade', 'max_drawdown',
            'max_profit', 'active_positions'
        ]

        # Initialize files if they don't exist
        for file_path, headers in [
            (self.trade_log_file, trade_headers),
            (self.signal_log_file, signal_headers),
            (self.performance_log_file, performance_headers)
        ]:
            if not os.path.exists(file_path):
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(headers)

    def log_signal_generated(self, signal: Signal, strategy: str, indicators: Dict[str, Any],
                           conditions_met: bool, action_taken: str, notes: str = ""):
        """Log when a signal is generated"""
        try:
            self.daily_stats['total_signals'] += 1

            # Log to signal CSV
            signal_data = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': signal.symbol,
                'signal_type': signal.signal_type.value,
                'timeframe': signal.timeframe,
                'strategy': strategy,
                'indicators': str(indicators),
                'conditions_met': conditions_met,
                'action_taken': action_taken,
                'notes': notes
            }

            self._write_to_csv(self.signal_log_file, signal_data)

            # Log to main logger
            self.logger.info(f"SIGNAL: {signal.symbol} {signal.signal_type.value} - {strategy} - {action_taken}")

        except Exception as e:
            self.logger.error(f"Error logging signal: {e}")

    def log_order_placed(self, order: Order, strategy: str, notes: str = ""):
        """Log when an order is placed"""
        try:
            self.daily_stats['total_trades'] += 1

            trade_entry = TradeLogEntry(
                timestamp=datetime.now(),
                symbol=order.symbol,
                signal_type=order.transaction_type.value,
                strategy=strategy,
                action="ORDER_PLACED",
                price=order.price,
                quantity=order.quantity,
                stop_loss=order.stop_loss,
                target=order.target,
                order_id=order.order_id or "",
                status=order.status.value,
                notes=notes
            )

            self._write_to_csv(self.trade_log_file, trade_entry.to_dict())

            # Log to main logger
            self.logger.info(f"ORDER PLACED: {order.symbol} {order.transaction_type.value} "
                           f"Qty: {order.quantity} @ {order.price:.2f} SL: {order.stop_loss:.2f} "
                           f"Target: {order.target:.2f} ID: {order.order_id}")

            # Send Telegram notification for order placement
            if is_telegram_available():
                try:
                    notify_order_placed(order, strategy, notes)
                    self.logger.info(f"📱 Telegram notification sent for order placement: {order.symbol}")
                except Exception as telegram_error:
                    self.logger.warning(f"Failed to send Telegram notification: {telegram_error}")

        except Exception as e:
            self.logger.error(f"Error logging order placement: {e}")

    def log_order_executed(self, order: Order, execution_price: float, strategy: str, notes: str = ""):
        """Log when an order is executed"""
        try:
            trade_entry = TradeLogEntry(
                timestamp=datetime.now(),
                symbol=order.symbol,
                signal_type=order.transaction_type.value,
                strategy=strategy,
                action="ORDER_EXECUTED",
                price=execution_price,
                quantity=order.quantity,
                stop_loss=order.stop_loss,
                target=order.target,
                order_id=order.order_id or "",
                status="EXECUTED",
                notes=notes
            )

            self._write_to_csv(self.trade_log_file, trade_entry.to_dict())

            # Log to main logger
            self.logger.info(f"ORDER EXECUTED: {order.symbol} {order.transaction_type.value} "
                           f"Qty: {order.quantity} @ {execution_price:.2f} ID: {order.order_id}")

        except Exception as e:
            self.logger.error(f"Error logging order execution: {e}")

    def log_position_closed(self, position: Position, strategy: str, notes: str = ""):
        """Log when a position is closed"""
        try:
            # Data validation - check for unrealistic P&L values
            self._validate_pnl(position.order, position.pnl)

            if position.pnl > 0:
                self.daily_stats['successful_trades'] += 1
                if position.pnl > self.daily_stats['max_profit']:
                    self.daily_stats['max_profit'] = position.pnl
            else:
                self.daily_stats['failed_trades'] += 1
                if abs(position.pnl) > self.daily_stats['max_drawdown']:
                    self.daily_stats['max_drawdown'] = abs(position.pnl)

            self.daily_stats['total_pnl'] += position.pnl

            trade_entry = TradeLogEntry(
                timestamp=position.exit_time or datetime.now(),
                symbol=position.order.symbol,
                signal_type=position.order.transaction_type.value,
                strategy=strategy,
                action="POSITION_CLOSED",
                price=position.exit_price or 0.0,
                quantity=position.order.quantity,
                stop_loss=position.order.stop_loss,
                target=position.order.target,
                order_id=position.exit_order_id or "",
                status=position.exit_reason.value,
                pnl=position.pnl,
                notes=notes
            )

            self._write_to_csv(self.trade_log_file, trade_entry.to_dict())

            # Log to main logger
            self.logger.info(f"POSITION CLOSED: {position.order.symbol} "
                           f"PnL: {position.pnl:.2f} Reason: {position.exit_reason.value} "
                           f"Exit: {position.exit_price:.2f}")

            # Send Telegram notification for position closure
            if is_telegram_available():
                try:
                    # Map exit reason to action for Telegram
                    action_map = {
                        "TARGET_HIT": "TARGET_HIT",
                        "STOP_LOSS": "STOP_LOSS",
                        "TIME_EXIT": "TIME_EXIT",
                        "MANUAL_SQUARE_OFF": "MANUAL_SQUARE_OFF",
                        "MARKET_CLOSE": "TIME_EXIT"
                    }
                    action = action_map.get(position.exit_reason.value, "POSITION_CLOSED")

                    notify_position_update(
                        position.order.symbol,
                        action,
                        position.exit_price or 0.0,
                        position.pnl,
                        strategy,
                        notes
                    )
                    self.logger.info(f"📱 Telegram notification sent for position closure: {position.order.symbol}")
                except Exception as telegram_error:
                    self.logger.warning(f"Failed to send Telegram notification: {telegram_error}")

        except Exception as e:
            self.logger.error(f"Error logging position closure: {e}")

    def log_error(self, symbol: str, strategy: str, error_message: str, notes: str = ""):
        """Log trading errors"""
        try:
            trade_entry = TradeLogEntry(
                timestamp=datetime.now(),
                symbol=symbol,
                signal_type="ERROR",
                strategy=strategy,
                action="ERROR",
                price=0.0,
                quantity=0,
                stop_loss=0.0,
                target=0.0,
                order_id="",
                status="ERROR",
                notes=f"{error_message} | {notes}"
            )

            self._write_to_csv(self.trade_log_file, trade_entry.to_dict())

            # Log to main logger
            self.logger.error(f"TRADING ERROR: {symbol} - {strategy} - {error_message}")

        except Exception as e:
            self.logger.error(f"Error logging trading error: {e}")

    def _validate_pnl(self, order, pnl: float):
        """Validate P&L values to detect unrealistic profits/losses"""
        try:
            # Calculate maximum possible P&L based on position size
            position_value = order.price * order.quantity
            max_reasonable_pnl = position_value * 0.5  # 50% of position value

            # Check for unrealistic profits (> 50% of position value)
            if abs(pnl) > max_reasonable_pnl:
                self.logger.warning(
                    f"⚠️ SUSPICIOUS P&L DETECTED: {order.symbol} "
                    f"P&L: ₹{pnl:,.2f}, Position Value: ₹{position_value:,.2f} "
                    f"(P&L is {abs(pnl)/position_value*100:.1f}% of position value)"
                )

                # Log additional details for investigation
                self.logger.warning(
                    f"   Order Details: Entry: ₹{order.price:.2f}, "
                    f"Qty: {order.quantity}, SL: ₹{order.stop_loss:.2f}, "
                    f"Target: ₹{order.target:.2f}"
                )

                # Log to error file for manual review
                self.log_error(
                    order.symbol,
                    "DATA_VALIDATION",
                    f"Suspicious P&L: ₹{pnl:,.2f} on position value ₹{position_value:,.2f}",
                    f"Entry: ₹{order.price:.2f}, Qty: {order.quantity}"
                )

        except Exception as e:
            self.logger.error(f"Error validating P&L: {e}")

    def log_daily_performance(self, active_positions: int = 0):
        """Log daily performance summary"""
        try:
            success_rate = 0.0
            avg_pnl = 0.0

            if self.daily_stats['total_trades'] > 0:
                success_rate = (self.daily_stats['successful_trades'] /
                              self.daily_stats['total_trades']) * 100
                avg_pnl = self.daily_stats['total_pnl'] / self.daily_stats['total_trades']

            performance_data = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_signals': self.daily_stats['total_signals'],
                'total_trades': self.daily_stats['total_trades'],
                'successful_trades': self.daily_stats['successful_trades'],
                'success_rate': round(success_rate, 2),
                'total_pnl': round(self.daily_stats['total_pnl'], 2),
                'avg_pnl_per_trade': round(avg_pnl, 2),
                'max_drawdown': round(self.daily_stats['max_drawdown'], 2),
                'max_profit': round(self.daily_stats['max_profit'], 2),
                'active_positions': active_positions
            }

            self._write_to_csv(self.performance_log_file, performance_data)

            # Log to main logger
            self.logger.info(f"DAILY PERFORMANCE: Signals: {self.daily_stats['total_signals']} "
                           f"Trades: {self.daily_stats['total_trades']} "
                           f"Success Rate: {success_rate:.1f}% "
                           f"Total PnL: {self.daily_stats['total_pnl']:.2f}")

        except Exception as e:
            self.logger.error(f"Error logging daily performance: {e}")

    def _write_to_csv(self, file_path: str, data: Dict[str, Any]):
        """Write data to CSV file"""
        try:
            with open(file_path, 'a', newline='', encoding='utf-8') as f:
                if data:
                    writer = csv.DictWriter(f, fieldnames=data.keys())
                    writer.writerow(data)
        except Exception as e:
            self.logger.error(f"Error writing to CSV {file_path}: {e}")

    def get_daily_stats(self) -> Dict[str, Any]:
        """Get current daily statistics"""
        return self.daily_stats.copy()

    def reset_daily_stats(self):
        """Reset daily statistics (call at start of new trading day)"""
        self.daily_stats = {
            'total_signals': 0,
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'max_profit': 0.0
        }
