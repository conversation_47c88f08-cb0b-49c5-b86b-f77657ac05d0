"""
Stock monitoring service for managing daily stock lists
Similar to Golang implementation with CSV-based stock management
"""
import csv
import os
import logging
from datetime import datetime
from typing import List, Dict, Set, Optional
from dataclasses import dataclass

from models.signal import Signal, SignalType


@dataclass
class MonitoredStock:
    """Monitored stock configuration"""
    symbol: str
    strategy: str = "ALL"  # ALL, MA_CROSSOVER, SUPPORT_RESISTANCE
    timeframe: str = "15min"
    enabled: bool = True
    notes: str = ""

    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary for CSV writing"""
        return {
            'symbol': self.symbol,
            'strategy': self.strategy,
            'timeframe': self.timeframe,
            'enabled': str(self.enabled),
            'notes': self.notes
        }

    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'MonitoredStock':
        """Create from dictionary (CSV row)"""
        return cls(
            symbol=data.get('symbol', '').strip().upper(),
            strategy=data.get('strategy', 'ALL').strip().upper(),
            timeframe=data.get('timeframe', '15min').strip(),
            enabled=data.get('enabled', 'True').strip().lower() == 'true',
            notes=data.get('notes', '').strip()
        )


class StockMonitor:
    """Stock monitoring service for managing daily trading lists"""

    def __init__(self, data_directory: str = "data", logger: Optional[logging.Logger] = None):
        self.data_directory = data_directory
        self.logger = logger or logging.getLogger(__name__)

        # Create data directory if it doesn't exist
        os.makedirs(data_directory, exist_ok=True)

        # File paths - only stocks_to_monitor.csv is used now
        self.stocks_file = os.path.join(data_directory, "stocks_to_monitor.csv")
        self.watchlist_file = os.path.join(data_directory, "watchlist.csv")

        # Initialize files if they don't exist
        self._initialize_files()

        # Cache for monitored stocks
        self._monitored_stocks: List[MonitoredStock] = []
        self._last_reload = datetime.min

        # Load initial data
        self.reload_stocks()

    def _initialize_files(self):
        """Initialize CSV files with headers only - no hardcoded sample data"""

        # Main stocks monitoring file - only create if it doesn't exist
        # The actual stocks_to_monitor.csv should be managed by the user
        if not os.path.exists(self.stocks_file):
            headers = ['symbol', 'strategy', 'timeframe', 'enabled', 'notes']

            with open(self.stocks_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)

            self.logger.info(f"Created empty stocks monitoring file: {self.stocks_file}")
            self.logger.info("Please add stocks to monitor in the stocks_to_monitor.csv file")

        # Note: Buy/sell signal CSV files are no longer used
        # The system now only uses stocks_to_monitor.csv for all stock monitoring

        # Watchlist file (for stocks to monitor but not trade) - optional
        if not os.path.exists(self.watchlist_file):
            headers = ['symbol', 'sector', 'notes', 'added_date']

            with open(self.watchlist_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)

            self.logger.info(f"Created empty watchlist file: {self.watchlist_file}")

    def reload_stocks(self) -> bool:
        """Reload stocks from CSV file"""
        try:
            if not os.path.exists(self.stocks_file):
                self.logger.warning(f"Stocks file not found: {self.stocks_file}")
                return False

            stocks = []
            with open(self.stocks_file, 'r', newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    try:
                        stock = MonitoredStock.from_dict(row)
                        if stock.symbol:  # Only add if symbol is not empty
                            stocks.append(stock)
                    except Exception as e:
                        self.logger.warning(f"Error parsing stock row {row}: {e}")
                        continue

            self._monitored_stocks = stocks
            self._last_reload = datetime.now()

            self.logger.info(f"Loaded {len(stocks)} stocks from {self.stocks_file}")
            return True

        except Exception as e:
            self.logger.error(f"Error reloading stocks: {e}")
            return False

    def get_monitored_stocks(self, strategy: Optional[str] = None, enabled_only: bool = True) -> List[MonitoredStock]:
        """
        Get list of monitored stocks

        Args:
            strategy: Filter by strategy (None for all)
            enabled_only: Only return enabled stocks

        Returns:
            List of MonitoredStock objects
        """
        # Auto-reload if file is newer
        if self._should_reload():
            self.reload_stocks()

        stocks = self._monitored_stocks

        # Filter by enabled status
        if enabled_only:
            stocks = [stock for stock in stocks if stock.enabled]

        # Filter by strategy
        if strategy:
            strategy = strategy.upper()
            stocks = [stock for stock in stocks if stock.strategy == 'ALL' or stock.strategy == strategy]

        return stocks

    def get_symbols_for_strategy(self, strategy: str, enabled_only: bool = True) -> List[str]:
        """Get list of symbols for a specific strategy"""
        stocks = self.get_monitored_stocks(strategy, enabled_only)
        return [stock.symbol for stock in stocks]

    def get_all_enabled_symbols(self) -> List[str]:
        """Get all enabled symbols regardless of strategy"""
        stocks = self.get_monitored_stocks(enabled_only=True)
        return [stock.symbol for stock in stocks]

    def create_signals_from_monitored_stocks(self, signal_type: SignalType) -> List[Signal]:
        """Create signals from monitored stocks"""
        stocks = self.get_monitored_stocks(enabled_only=True)
        signals = []

        for stock in stocks:
            signal = Signal(
                symbol=stock.symbol,
                signal_type=signal_type,
                timeframe=stock.timeframe
            )
            signals.append(signal)

        return signals



    def add_stock_to_monitor(self, symbol: str, strategy: str = "ALL",
                           timeframe: str = "15min", enabled: bool = True,
                           notes: str = "") -> bool:
        """Add a new stock to monitoring list"""
        try:
            # Check if stock already exists
            existing_symbols = [stock.symbol for stock in self._monitored_stocks]
            if symbol.upper() in existing_symbols:
                self.logger.warning(f"Stock {symbol} already in monitoring list")
                return False

            # Add to memory
            new_stock = MonitoredStock(
                symbol=symbol.upper(),
                strategy=strategy.upper(),
                timeframe=timeframe,
                enabled=enabled,
                notes=notes
            )
            self._monitored_stocks.append(new_stock)

            # Save to file
            self._save_stocks_to_file()

            self.logger.info(f"Added {symbol} to monitoring list")
            return True

        except Exception as e:
            self.logger.error(f"Error adding stock {symbol}: {e}")
            return False

    def remove_stock_from_monitor(self, symbol: str) -> bool:
        """Remove a stock from monitoring list"""
        try:
            symbol = symbol.upper()
            original_count = len(self._monitored_stocks)

            self._monitored_stocks = [stock for stock in self._monitored_stocks
                                    if stock.symbol != symbol]

            if len(self._monitored_stocks) < original_count:
                self._save_stocks_to_file()
                self.logger.info(f"Removed {symbol} from monitoring list")
                return True
            else:
                self.logger.warning(f"Stock {symbol} not found in monitoring list")
                return False

        except Exception as e:
            self.logger.error(f"Error removing stock {symbol}: {e}")
            return False

    def enable_stock(self, symbol: str) -> bool:
        """Enable a stock for trading"""
        return self._update_stock_status(symbol, True)

    def disable_stock(self, symbol: str) -> bool:
        """Disable a stock from trading"""
        return self._update_stock_status(symbol, False)

    def _update_stock_status(self, symbol: str, enabled: bool) -> bool:
        """Update stock enabled status"""
        try:
            symbol = symbol.upper()
            updated = False

            for stock in self._monitored_stocks:
                if stock.symbol == symbol:
                    stock.enabled = enabled
                    updated = True
                    break

            if updated:
                self._save_stocks_to_file()
                status = "enabled" if enabled else "disabled"
                self.logger.info(f"Stock {symbol} {status}")
                return True
            else:
                self.logger.warning(f"Stock {symbol} not found in monitoring list")
                return False

        except Exception as e:
            self.logger.error(f"Error updating stock {symbol} status: {e}")
            return False

    def _save_stocks_to_file(self):
        """Save monitored stocks to CSV file"""
        try:
            with open(self.stocks_file, 'w', newline='', encoding='utf-8') as f:
                headers = ['symbol', 'strategy', 'timeframe', 'enabled', 'notes']
                writer = csv.DictWriter(f, fieldnames=headers)
                writer.writeheader()

                for stock in self._monitored_stocks:
                    writer.writerow(stock.to_dict())

            self.logger.debug(f"Saved {len(self._monitored_stocks)} stocks to {self.stocks_file}")

        except Exception as e:
            self.logger.error(f"Error saving stocks to file: {e}")

    def _should_reload(self) -> bool:
        """Check if stocks file should be reloaded"""
        try:
            if not os.path.exists(self.stocks_file):
                return False

            file_mtime = datetime.fromtimestamp(os.path.getmtime(self.stocks_file))
            return file_mtime > self._last_reload

        except Exception:
            return False

    def get_stock_statistics(self) -> Dict[str, int]:
        """Get statistics about monitored stocks"""
        stocks = self._monitored_stocks

        return {
            'total_stocks': len(stocks),
            'enabled_stocks': len([s for s in stocks if s.enabled]),
            'disabled_stocks': len([s for s in stocks if not s.enabled]),
            'ma_crossover_stocks': len([s for s in stocks if s.strategy == 'MA_CROSSOVER' and s.enabled]),
            'support_resistance_stocks': len([s for s in stocks if s.strategy == 'SUPPORT_RESISTANCE' and s.enabled]),
            'all_strategy_stocks': len([s for s in stocks if s.strategy == 'ALL' and s.enabled])
        }
