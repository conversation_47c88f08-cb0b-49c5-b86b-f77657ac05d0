#!/usr/bin/env python3
"""
Get Telegram User ID

This script helps you find your actual Telegram user ID for bot authorization.
"""

import os
import telebot
from datetime import datetime

def get_user_id():
    """Get the actual user ID from Telegram"""
    
    # Load bot token from .env.telegram
    bot_token = None
    try:
        with open('.env.telegram', 'r') as f:
            for line in f:
                if line.startswith('TELEGRAM_BOT_TOKEN='):
                    bot_token = line.split('=', 1)[1].strip()
                    break
    except Exception as e:
        print(f"Error reading .env.telegram: {e}")
        return
    
    if not bot_token:
        print("❌ Bot token not found in .env.telegram")
        return
    
    print("🤖 USER ID DETECTOR BOT")
    print("=" * 50)
    print("This bot will help you find your actual Telegram user ID.")
    print()
    print("📱 INSTRUCTIONS:")
    print("1. Send any message to your bot in Telegram")
    print("2. This script will show your actual user ID")
    print("3. We'll then update the configuration with the correct ID")
    print()
    print("🔍 Waiting for messages...")
    print("(Press Ctrl+C to stop)")
    
    # Create bot instance
    bot = telebot.TeleBot(bot_token)
    
    # Store found user IDs
    found_users = set()
    
    @bot.message_handler(func=lambda message: True)
    def handle_any_message(message):
        user_id = message.from_user.id
        user_name = message.from_user.first_name or "Unknown"
        username = message.from_user.username or "No username"
        chat_id = message.chat.id
        
        if user_id not in found_users:
            found_users.add(user_id)
            
            print(f"\n✅ FOUND USER!")
            print(f"👤 Name: {user_name}")
            print(f"🆔 User ID: {user_id}")
            print(f"📝 Username: @{username}")
            print(f"💬 Chat ID: {chat_id}")
            print(f"📅 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Send confirmation message
            bot.reply_to(message, f"""
🎯 **User ID Found!**

👤 **Your Details:**
• Name: {user_name}
• User ID: `{user_id}`
• Username: @{username}
• Chat ID: `{chat_id}`

✅ **Next Step:**
Your actual User ID is: **{user_id}**

If this is different from what's in your .env.telegram file, we need to update it!
""", parse_mode='Markdown')
            
            print(f"\n🔧 CONFIGURATION UPDATE NEEDED:")
            print(f"Current .env.telegram has: 757988378")
            print(f"Your actual User ID is: {user_id}")
            
            if str(user_id) != "757988378":
                print(f"\n⚠️  USER ID MISMATCH DETECTED!")
                print(f"You need to update .env.telegram with:")
                print(f"TELEGRAM_AUTHORIZED_USERS={user_id}")
                print(f"TELEGRAM_ADMIN_USERS={user_id}")
                
                # Ask if we should update automatically
                try:
                    response = input(f"\n🤔 Update .env.telegram automatically? (y/n): ").lower().strip()
                    if response == 'y':
                        update_env_file(user_id, chat_id)
                except KeyboardInterrupt:
                    print("\n👋 Stopping...")
                    return
            else:
                print(f"✅ User ID matches! Configuration is correct.")
    
    try:
        # Start polling
        bot.infinity_polling(timeout=10, long_polling_timeout=5)
    except KeyboardInterrupt:
        print("\n👋 Stopping user ID detector...")
    except Exception as e:
        print(f"❌ Error: {e}")

def update_env_file(user_id, chat_id):
    """Update .env.telegram with correct user ID"""
    try:
        # Read current file
        with open('.env.telegram', 'r') as f:
            lines = f.readlines()
        
        # Update lines
        updated_lines = []
        for line in lines:
            if line.startswith('TELEGRAM_CHAT_ID='):
                updated_lines.append(f'TELEGRAM_CHAT_ID={chat_id}\n')
            elif line.startswith('TELEGRAM_AUTHORIZED_USERS='):
                updated_lines.append(f'TELEGRAM_AUTHORIZED_USERS={user_id}\n')
            elif line.startswith('TELEGRAM_ADMIN_USERS='):
                updated_lines.append(f'TELEGRAM_ADMIN_USERS={user_id}\n')
            else:
                updated_lines.append(line)
        
        # Write updated file
        with open('.env.telegram', 'w') as f:
            f.writelines(updated_lines)
        
        print(f"✅ Updated .env.telegram successfully!")
        print(f"📝 New User ID: {user_id}")
        print(f"📝 New Chat ID: {chat_id}")
        print(f"\n🔄 Please restart your bot to apply changes:")
        print(f"python telegram_bot_launcher.py")
        
    except Exception as e:
        print(f"❌ Error updating .env.telegram: {e}")
        print(f"\n🔧 Please update manually:")
        print(f"TELEGRAM_CHAT_ID={chat_id}")
        print(f"TELEGRAM_AUTHORIZED_USERS={user_id}")
        print(f"TELEGRAM_ADMIN_USERS={user_id}")

if __name__ == "__main__":
    get_user_id()
