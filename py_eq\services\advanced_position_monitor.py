"""
Advanced Position Monitor with enhanced exit strategies:
1. Immediate square-off on SL hit
2. Square off at 15:12 hrs
3. Square off if price goes above target and comes back to target price
4. Trailing stop loss that activates after price reaches beyond target
"""
import logging
import time
import threading
from typing import List, Optional, Dict, Tuple
from datetime import datetime, time as dt_time
from dataclasses import dataclass
from enum import Enum

from models.order import Order, OrderStatus, TransactionType
from services.order_service import OrderServiceInterface
from services.market_data_service import MarketDataServiceInterface
from services.position_monitor import EnhancedPositionMonitor, StopLossConfig, ExitPriority

@dataclass
class TrailingStopLossConfig:
    """Configuration for trailing stop loss"""
    activation_threshold: float = 0.3  # Percentage beyond target to activate trailing SL (0.3 = 30% of target-entry)
    trail_percentage: float = 0.5  # Percentage of profit to trail (0.5 = 50% of current profit)
    min_trail_distance: float = 0.2  # Minimum trail distance as percentage of entry price
    max_trail_distance: float = 2.0  # Maximum trail distance as percentage of entry price


class AdvancedPositionMonitor(EnhancedPositionMonitor):
    """
Advanced position monitor with enhanced exit strategies:
1. Immediate square-off on SL hit
2. Square off at 15:12 hrs
3. Square off if price goes above target and comes back to target price
"""

    def __init__(
        self,
        order_service: OrderServiceInterface,
        market_data_service: MarketDataServiceInterface,
        logger: logging.Logger,
        websocket_service=None,
        trading_logger=None,
        check_interval: int = 5,  # seconds between position checks (fallback only)
        square_off_time: str = "15:12",  # Changed to 15:12 as required
        sl_config: Optional[StopLossConfig] = None,
        trailing_sl_config: Optional[TrailingStopLossConfig] = None
    ):
        super().__init__(
            order_service=order_service,
            market_data_service=market_data_service,
            logger=logger,
            websocket_service=websocket_service,
            trading_logger=trading_logger,
            check_interval=check_interval,
            square_off_time=square_off_time,
            sl_config=sl_config
        )
        
        # Trailing stop loss configuration - DISABLED by default to prevent issues
        self.trailing_sl_config = trailing_sl_config or TrailingStopLossConfig()
        self.trailing_sl_enabled = False  # Disable trailing SL to prevent rapid exit/re-entry

        # Tracking dictionaries for trailing stop loss
        self.trailing_sl_active: Dict[str, bool] = {}
        self.trailing_sl_levels: Dict[str, float] = {}

        # Additional tracking for advanced features
        self.max_price_reached: Dict[str, float] = {}  # Symbol -> max price reached (for target return exit)
        self.min_price_reached: Dict[str, float] = {}  # Symbol -> min price reached (for target return exit)
        self.target_hit: Dict[str, bool] = {}  # Symbol -> target hit flag (for target return exit)
        
        # Add compatibility wrapper for websocket service
        if self.websocket_service and hasattr(self.websocket_service, 'get_live_price'):
            # Add a get_last_price method that calls get_live_price
            self.websocket_service.get_last_price = self.websocket_service.get_live_price
            
        # Add compatibility method for getting prices
        self._setup_price_compatibility()
            
        self.logger.info(f"🚀 Advanced Position Monitor initialized with square off time: {square_off_time}")
                        
    def _setup_price_compatibility(self):
        """Setup compatibility methods for getting prices from different services"""
        # For market data service
        if not hasattr(self.market_data_service, 'get_last_price') and hasattr(self.market_data_service, 'get_current_price'):
            self.market_data_service.get_last_price = self.market_data_service.get_current_price
            self.logger.info("Added get_last_price compatibility method to market data service")
            
        # For websocket service
        if self.websocket_service:
            if not hasattr(self.websocket_service, 'get_last_price') and hasattr(self.websocket_service, 'get_live_price'):
                self.websocket_service.get_last_price = self.websocket_service.get_live_price
                self.logger.info("Added get_last_price compatibility method to websocket service")
                
    def _get_price(self, symbol: str) -> Optional[float]:
        """Get price from the best available source with compatibility handling"""
        # Try websocket first (real-time)
        if self.websocket_service:
            try:
                if hasattr(self.websocket_service, 'get_last_price'):
                    price = self.websocket_service.get_last_price(symbol)
                    if price is not None:
                        return price
                elif hasattr(self.websocket_service, 'get_live_price'):
                    price = self.websocket_service.get_live_price(symbol)
                    if price is not None:
                        return price
            except Exception as e:
                self.logger.debug(f"Error getting price from websocket for {symbol}: {e}")
        
        # Try market data service
        try:
            if hasattr(self.market_data_service, 'get_last_price'):
                return self.market_data_service.get_last_price(symbol)
            elif hasattr(self.market_data_service, 'get_current_price'):
                return self.market_data_service.get_current_price(symbol)
        except Exception as e:
            self.logger.debug(f"Error getting price from market data service for {symbol}: {e}")
            
        # Check live prices cache
        if symbol in self.live_prices:
            return self.live_prices[symbol]
            
        return None

    def _on_price_update(self, symbol: str, price: float, timestamp: datetime):
        """Enhanced price update handler with advanced exit strategies"""
        try:
            # Update live price
            self.live_prices[symbol] = price

            # Check if we have a position for this symbol
            if symbol not in self.position_symbols:
                return

            position = self.position_symbols[symbol]

            # Check if position is already squared off
            if hasattr(position, 'squared_off') and position.squared_off:
                return  # Position already closed, ignore further updates

            # CRITICAL FIX: Handle positions with invalid entry prices using emergency monitoring
            if position.entry_price is None or position.entry_price <= 0:
                self.logger.warning(f"⚠️ EMERGENCY MONITORING: Invalid entry price for {symbol}: {position.entry_price}")
                self.logger.warning(f"⚠️ Implementing emergency stop-loss based on current price and risk limits")

                # EMERGENCY MONITORING: Use current price-based stop-loss
                self._emergency_position_monitoring(symbol, price, position)
                return
            else:
                # Normal monitoring with valid entry price
                self._update_position_risk(symbol, price, position)

            # 1. Check for immediate stop-loss hit (highest priority)
            if self._is_fast_sl_hit(symbol, price):
                self.logger.info(f"🛑 IMMEDIATE Stop-loss hit for {symbol} at ₹{price:.2f}")
                self._execute_immediate_exit(position, price, "STOP_LOSS", ExitPriority.STOP_LOSS)
                return

            # 2. Check for square off time (done in main monitoring loop)
            
            # 3. Check for target return (price went above target and came back)
            if self._is_target_return_hit(symbol, price, position):
                self.logger.info(f"🎯 Target return exit for {symbol} at ₹{price:.2f} (price returned after hitting target)")
                self._execute_immediate_exit(position, price, "TARGET_RETURN", ExitPriority.TARGET)
                return
                
            # 4. Check for target return (price went above target and came back)
            if self._is_target_return_hit(symbol, price, position):
                self.logger.info(f"🎯 Target return exit for {symbol} at ₹{price:.2f} (price returned after hitting target)")
                self._execute_immediate_exit(position, price, "TARGET_RETURN", ExitPriority.TARGET)
                return
                
            # Update price tracking for trailing stop loss and target return
            self._update_price_tracking(symbol, price, position)
            
            # Update price tracking for target return exit
            self._update_price_tracking(symbol, price, position)

        except Exception as e:
            self.logger.error(f"Error in advanced price update for {symbol}: {e}")

    def _update_price_tracking(self, symbol: str, price: float, position: Order):
        """Update price tracking for trailing stop loss and target return"""
        try:
            # Initialize tracking if needed
            if symbol not in self.max_price_reached:
                self.max_price_reached[symbol] = position.entry_price
                
            if symbol not in self.min_price_reached:
                self.min_price_reached[symbol] = position.entry_price
                
            if symbol not in self.target_hit:
                self.target_hit[symbol] = False
                
            # Update max/min price reached
            if position.transaction_type == TransactionType.BUY:
                # For long positions, track max price
                self.max_price_reached[symbol] = max(self.max_price_reached[symbol], price)
                
                # Check if target was hit
                if price >= position.target and not self.target_hit[symbol]:
                    self.target_hit[symbol] = True
                    self.logger.info(f"🎯 Target hit for {symbol} at ₹{price:.2f} (tracking for return)")
            else:
                # For short positions, track min price
                self.min_price_reached[symbol] = min(self.min_price_reached[symbol], price)
                
                # Check if target was hit
                if price <= position.target and not self.target_hit[symbol]:
                    self.target_hit[symbol] = True
                    self.logger.info(f"🎯 Target hit for {symbol} at ₹{price:.2f} (tracking for return)")
                    
        except Exception as e:
            self.logger.error(f"Error updating price tracking for {symbol}: {e}")

    def _is_target_return_hit(self, symbol: str, price: float, position: Order) -> bool:
        """
        Check if price has hit target and then returned back to target
        This implements requirement: "square off if price go above target and come again back to the target price"
        """
        if symbol not in self.target_hit or not self.target_hit[symbol]:
            return False
            
        # Target was previously hit, check if price returned to target
        if position.transaction_type == TransactionType.BUY:
            # For long positions, check if price came back down to target after going above
            return price <= position.target
        else:
            # For short positions, check if price came back up to target after going below
            return price >= position.target

    def _update_trailing_stop_loss(self, symbol: str, price: float, position: Order):
        """Update trailing stop loss level based on current price"""
        try:
            # CRITICAL FIX: Check for zero entry price to prevent division by zero
            if position.entry_price == 0:
                self.logger.error(f"Error updating trailing stop loss for {symbol}: entry price is zero")
                return
                
            # Initialize trailing SL tracking if needed
            if symbol not in self.trailing_sl_active:
                self.trailing_sl_active[symbol] = False
                
            if symbol not in self.trailing_sl_levels:
                self.trailing_sl_levels[symbol] = position.stop_loss
                
            # Check if trailing SL should be activated
            if not self.trailing_sl_active[symbol]:
                # Calculate activation threshold
                if position.transaction_type == TransactionType.BUY:
                    # For long positions
                    target_distance = position.target - position.entry_price
                    activation_price = position.target + (target_distance * self.trailing_sl_config.activation_threshold)
                    
                    if price >= activation_price:
                        self.trailing_sl_active[symbol] = True
                        self.logger.info(f"🔄 Trailing SL activated for {symbol} at ₹{price:.2f}")
                else:
                    # For short positions
                    target_distance = position.entry_price - position.target
                    activation_price = position.target - (target_distance * self.trailing_sl_config.activation_threshold)
                    
                    if price <= activation_price:
                        self.trailing_sl_active[symbol] = True
                        self.logger.info(f"🔄 Trailing SL activated for {symbol} at ₹{price:.2f}")
            
            # Update trailing SL level if active
            if self.trailing_sl_active[symbol]:
                if position.transaction_type == TransactionType.BUY:
                    # For long positions, trail upward
                    profit = price - position.entry_price
                    trail_distance = profit * self.trailing_sl_config.trail_percentage
                    
                    # Apply min/max constraints
                    min_distance = position.entry_price * self.trailing_sl_config.min_trail_distance
                    max_distance = position.entry_price * self.trailing_sl_config.max_trail_distance
                    trail_distance = max(min(trail_distance, max_distance), min_distance)
                    
                    new_sl = price - trail_distance
                    
                    # Only move SL up, never down
                    if new_sl > self.trailing_sl_levels[symbol]:
                        self.trailing_sl_levels[symbol] = new_sl
                        self.logger.info(f"🔄 Trailing SL updated for {symbol}: ₹{new_sl:.2f}")
                else:
                    # For short positions, trail downward
                    profit = position.entry_price - price
                    trail_distance = profit * self.trailing_sl_config.trail_percentage
                    
                    # Apply min/max constraints
                    min_distance = position.entry_price * self.trailing_sl_config.min_trail_distance
                    max_distance = position.entry_price * self.trailing_sl_config.max_trail_distance
                    trail_distance = max(min(trail_distance, max_distance), min_distance)
                    
                    new_sl = price + trail_distance
                    
                    # Only move SL down, never up
                    if new_sl < self.trailing_sl_levels[symbol]:
                        self.trailing_sl_levels[symbol] = new_sl
                        self.logger.info(f"🔄 Trailing SL updated for {symbol}: ₹{new_sl:.2f}")
        
        except Exception as e:
            self.logger.error(f"Error updating trailing stop loss for {symbol}: {e}")

    def _is_trailing_sl_hit(self, symbol: str, price: float, position: Order) -> bool:
        """Check if trailing stop loss has been hit"""
        # CRITICAL FIX: Check if position is already squared off
        if hasattr(position, 'squared_off') and position.squared_off:
            return False
            
        # CRITICAL FIX: Check if trailing SL is active (must be activated only after target is hit)
        if symbol not in self.trailing_sl_active or not self.trailing_sl_active[symbol]:
            return False
            
        if symbol not in self.trailing_sl_levels:
            return False
            
        trailing_sl = self.trailing_sl_levels[symbol]
        
        if position.transaction_type == TransactionType.BUY:
            # For long positions, trailing SL is hit when price falls below trailing SL level
            return price <= trailing_sl
        else:
            # For short positions, trailing SL is hit when price rises above trailing SL level
            return price >= trailing_sl

    def _monitor_positions(self):
        """Override main monitoring loop to implement square off at 15:12"""
        self.logger.info("📊 Advanced position monitoring loop started")

        while self.monitoring and not self._stop_event.is_set():
            try:
                # Check if it's square off time (15:12)
                current_time = datetime.now().time()
                if current_time >= self.square_off_time:
                    self.logger.info(f"🕐 Square off time reached ({self.square_off_time.strftime('%H:%M')}) - closing all positions")
                    self.square_off_all_positions("SQUARE_OFF_TIME")
                    break

                self._check_all_positions()

                # Wait for next check or stop signal
                if self._stop_event.wait(timeout=self.check_interval):
                    break  # Stop event was set

            except Exception as e:
                self.logger.error(f"Error in position monitoring loop: {e}")
                time.sleep(self.check_interval)

        self.logger.info("📊 Position monitoring loop ended")
        
    def _check_all_positions(self):
        """Override to use our compatible price method"""
        try:
            # Get open positions from order service
            open_positions = self.order_service.get_open_positions()

            if not open_positions:
                return  # No positions to monitor

            self.logger.debug(f"Monitoring {len(open_positions)} open positions")

            # Update position tracking and WebSocket subscriptions
            self._update_position_tracking(open_positions)

            for position in open_positions:
                try:
                    # Check if position is already squared off
                    if hasattr(position, 'squared_off') and position.squared_off:
                        continue  # Position already closed, skip checking

                    # Get current market price using our compatible method
                    current_price = self._get_price(position.symbol)
                    
                    if current_price is None:
                        self.logger.warning(f"Could not get current price for {position.symbol}")
                        continue

                    # Check for stop-loss hit
                    if self._is_stop_loss_hit(position, current_price):
                        self.logger.info(f"🛑 Stop-loss hit for {position.symbol} at ₹{current_price:.2f}")
                        self._execute_immediate_exit(position, current_price, "STOP_LOSS", ExitPriority.STOP_LOSS)
                        continue

                    # Check for trailing stop loss hit (DISABLED to prevent rapid exit/re-entry)
                    if self.trailing_sl_enabled and self._is_trailing_sl_hit(position.symbol, current_price, position):
                        self.logger.info(f"🔄 Trailing stop-loss hit for {position.symbol} at ₹{current_price:.2f}")
                        self._execute_immediate_exit(position, current_price, "TRAILING_STOP_LOSS", ExitPriority.STOP_LOSS)
                        continue
                        
                    # Check for target return (price went above target and came back)
                    if self._is_target_return_hit(position.symbol, current_price, position):
                        self.logger.info(f"🎯 Target return exit for {position.symbol} at ₹{current_price:.2f}")
                        self._execute_immediate_exit(position, current_price, "TARGET_RETURN", ExitPriority.TARGET)
                        continue
                        
                    # Update price tracking for trailing stop loss and target return
                    self._update_price_tracking(position.symbol, current_price, position)
                    
                    # Update trailing stop loss if needed (DISABLED to prevent rapid exit/re-entry)
                    if self.trailing_sl_enabled:
                        self._update_trailing_stop_loss(position.symbol, current_price, position)
                    
                    # Log position status (less frequently)
                    if not hasattr(position, '_last_status_log') or (time.time() - position._last_status_log) > 60:
                        # Calculate P&L
                        # CRITICAL FIX: Check for zero entry price to prevent division by zero
                        if position.entry_price == 0:
                            self.logger.error(f"Error validating P&L: float division by zero")
                            pnl = 0.0
                        else:
                            if position.transaction_type == TransactionType.BUY:
                                pnl = (current_price - position.entry_price) * position.quantity
                            else:
                                pnl = (position.entry_price - current_price) * position.quantity
                            
                        self.logger.debug(f"📈 {position.symbol}: Current=₹{current_price:.2f}, "
                                        f"Entry=₹{position.entry_price:.2f}, SL=₹{position.stop_loss:.2f}, "
                                        f"Target=₹{position.target:.2f}, P&L=₹{pnl:.2f}")
                        position._last_status_log = time.time()
                    
                except Exception as e:
                    self.logger.error(f"Error checking position {position.symbol}: {e}")

        except Exception as e:
            self.logger.error(f"Error getting open positions: {e}")

    def add_positions(self, positions: List[Order]):
        """Add a list of open positions to the monitor"""
        for position in positions:
            if position.symbol not in self.position_symbols:
                self.position_symbols[position.symbol] = position
                self.logger.info(f"Added position {position.symbol} to monitor")

    def _cleanup_position_tracking(self, symbol: str):
        """Clean up all tracking data for a closed position"""
        try:
            # Remove from all tracking dictionaries
            for tracking_dict in [
                self.position_symbols, self.live_prices, self.sl_trigger_prices,
                self.target_trigger_prices, self.execution_timings, self.position_risks,
                self.trailing_sl_active, self.trailing_sl_levels, 
                self.max_price_reached, self.min_price_reached, self.target_hit
            ]:
                if symbol in tracking_dict:
                    tracking_dict.pop(symbol, None)

            # Unsubscribe from WebSocket
            if self.websocket_service:
                self.websocket_service.unsubscribe_symbol(symbol)

            self.logger.debug(f"Cleaned up tracking for {symbol}")
        except Exception as e:
            self.logger.error(f"Error cleaning up tracking for {symbol}: {e}")

    def _emergency_position_monitoring(self, symbol: str, current_price: float, position):
        """
        Emergency monitoring for positions with invalid entry prices
        Uses risk-based stop-loss instead of percentage-based
        """
        try:
            # EMERGENCY STOP-LOSS: Use absolute risk limit
            # If morning balance was ₹4730, 1% risk = ₹47.3
            morning_balance = 4730.0  # You mentioned this in the issue
            max_risk_per_position = morning_balance * 0.01  # ₹47.3

            # Calculate current position value
            position_value = current_price * position.quantity

            # EMERGENCY LOGIC: Conservative approach to prevent unlimited losses
            # Since we don't have reliable entry price, we'll use time-based and value-based rules

            emergency_exit_needed = False
            exit_reason = "EMERGENCY_MONITORING"

            # Rule 1: If position value is very large, it might be a big loss
            # (This assumes the position was entered at a reasonable price)
            max_reasonable_position_value = morning_balance * 0.5  # 50% of balance
            if position_value > max_reasonable_position_value:
                emergency_exit_needed = True
                exit_reason = "EMERGENCY_POSITION_TOO_LARGE"

            # Rule 2: Time-based emergency exit (if position is very old)
            # This is a safety net for positions that have been running too long
            import time
            from datetime import datetime, timedelta

            # If position has been open for more than 2 hours, consider emergency exit
            # (This is conservative but prevents unlimited losses)
            position_age_hours = 2  # Conservative limit

            # Log emergency monitoring status
            self.logger.warning(f"⚠️ EMERGENCY MONITORING {symbol}:")
            self.logger.warning(f"   Current Price: ₹{current_price:.2f}")
            self.logger.warning(f"   Quantity: {position.quantity}")
            self.logger.warning(f"   Position Value: ₹{position_value:.2f}")
            self.logger.warning(f"   Max Risk Allowed: ₹{max_risk_per_position:.2f}")
            self.logger.warning(f"   Max Reasonable Value: ₹{max_reasonable_position_value:.2f}")
            self.logger.warning(f"   Emergency Exit Needed: {emergency_exit_needed}")

            # CRITICAL: Always log that manual intervention is needed
            self.logger.error(f"🚨 MANUAL INTERVENTION REQUIRED for {symbol}")
            self.logger.error(f"🚨 Entry price unknown - cannot calculate accurate stop-loss")
            self.logger.error(f"🚨 Please manually check and close position if needed")

            if emergency_exit_needed:
                self.logger.error(f"🚨 EMERGENCY EXIT TRIGGERED for {symbol}")
                self.logger.error(f"🚨 Reason: {exit_reason}")
                # Note: ExitPriority.EMERGENCY might not exist, using STOP_LOSS
                from models.order import ExitReason
                self._execute_immediate_exit(position, current_price, ExitReason.STOP_LOSS, ExitPriority.STOP_LOSS)

        except Exception as e:
            self.logger.error(f"Error in emergency position monitoring for {symbol}: {e}")