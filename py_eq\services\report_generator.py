"""
Trading Report Generator - Creates comprehensive HTML dashboards and reports

Features:
- Daily trading dashboard with all trades and P&L
- Weekly reports (generated on Friday or next run day)
- Monthly reports (generated on last day of month or next run day)
- Comprehensive trading metrics and KPIs
- Beautiful HTML templates with charts and analytics
"""

import os
import csv
import json
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import calendar
import math


@dataclass
class TradeRecord:
    """Individual trade record for analysis"""
    timestamp: str
    symbol: str
    action: str
    price: float
    quantity: int
    order_value: float
    stop_loss: float
    target: float
    strategy: str
    account: str
    pnl: float = 0.0
    order_id: str = ""
    exit_price: float = 0.0
    exit_timestamp: str = ""
    duration_minutes: int = 0
    is_winner: bool = False


@dataclass
class TradingMetrics:
    """Comprehensive trading performance metrics"""
    # Basic metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    
    # P&L metrics
    total_pnl: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Risk metrics
    profit_factor: float = 0.0
    expectancy: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    max_drawdown_percent: float = 0.0
    
    # Trading behavior
    average_trade_duration: int = 0  # minutes
    total_volume: float = 0.0
    total_commission: float = 0.0
    
    # Balance metrics
    opening_balance: float = 0.0
    closing_balance: float = 0.0
    peak_balance: float = 0.0
    
    # Strategy breakdown
    strategy_performance: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.strategy_performance is None:
            self.strategy_performance = {}


class TradingReportGenerator:
    """
    Generates comprehensive trading reports and dashboards
    """
    
    def __init__(self, log_directory: str = "logs", reports_directory: str = "reports", logger: logging.Logger = None):
        self.log_directory = log_directory
        self.reports_directory = reports_directory
        self.logger = logger or logging.getLogger(__name__)
        
        # Create reports directory if it doesn't exist
        os.makedirs(self.reports_directory, exist_ok=True)
        os.makedirs(os.path.join(self.reports_directory, "daily"), exist_ok=True)
        os.makedirs(os.path.join(self.reports_directory, "weekly"), exist_ok=True)
        os.makedirs(os.path.join(self.reports_directory, "monthly"), exist_ok=True)
    
    def generate_daily_report(self, date: datetime = None) -> str:
        """Generate daily trading report and dashboard"""
        if date is None:
            date = datetime.now()
        
        date_str = date.strftime('%Y-%m-%d')
        self.logger.info(f"📊 Generating daily report for {date_str}")
        
        # Load trade data for the day
        trades = self._load_trades_for_date(date)
        metrics = self._calculate_metrics(trades, date)
        
        # Generate HTML report
        html_content = self._generate_daily_html(trades, metrics, date)
        
        # Save report
        report_path = os.path.join(self.reports_directory, "daily", f"daily_report_{date_str}.html")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"✅ Daily report saved: {report_path}")
        return report_path
    
    def generate_weekly_report(self, date: datetime = None) -> Optional[str]:
        """Generate weekly trading report (Friday or next available day)"""
        if date is None:
            date = datetime.now()
        
        # Find the week's date range
        week_start, week_end = self._get_week_range(date)
        week_str = f"{week_start.strftime('%Y-%m-%d')}_to_{week_end.strftime('%Y-%m-%d')}"
        
        # Check if weekly report already exists
        report_path = os.path.join(self.reports_directory, "weekly", f"weekly_report_{week_str}.html")
        if os.path.exists(report_path):
            self.logger.info(f"📊 Weekly report already exists: {report_path}")
            return report_path
        
        self.logger.info(f"📊 Generating weekly report for {week_str}")
        
        # Load trade data for the week
        trades = self._load_trades_for_period(week_start, week_end)
        metrics = self._calculate_metrics(trades, week_start, week_end)
        
        # Generate HTML report
        html_content = self._generate_weekly_html(trades, metrics, week_start, week_end)
        
        # Save report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"✅ Weekly report saved: {report_path}")
        return report_path
    
    def generate_monthly_report(self, date: datetime = None) -> Optional[str]:
        """Generate monthly trading report (last day of month or next available day)"""
        if date is None:
            date = datetime.now()
        
        # Find the month's date range
        month_start, month_end = self._get_month_range(date)
        month_str = f"{month_start.strftime('%Y-%m')}"
        
        # Check if monthly report already exists
        report_path = os.path.join(self.reports_directory, "monthly", f"monthly_report_{month_str}.html")
        if os.path.exists(report_path):
            self.logger.info(f"📊 Monthly report already exists: {report_path}")
            return report_path
        
        self.logger.info(f"📊 Generating monthly report for {month_str}")
        
        # Load trade data for the month
        trades = self._load_trades_for_period(month_start, month_end)
        metrics = self._calculate_metrics(trades, month_start, month_end)
        
        # Generate HTML report
        html_content = self._generate_monthly_html(trades, metrics, month_start, month_end)
        
        # Save report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"✅ Monthly report saved: {report_path}")
        return report_path
    
    def check_and_generate_pending_reports(self):
        """Check and generate any pending weekly/monthly reports"""
        current_date = datetime.now()
        
        # Check for pending weekly report
        if current_date.weekday() >= 4:  # Friday or later
            self.generate_weekly_report(current_date)
        
        # Check for pending monthly report
        if current_date.day > 25:  # Near end of month
            self.generate_monthly_report(current_date)
    
    def _load_trades_for_date(self, date: datetime) -> List[TradeRecord]:
        """Load all trades for a specific date"""
        date_str = date.strftime('%Y-%m-%d')
        trades_file = os.path.join(self.log_directory, f"trades_{date_str}.csv")
        
        if not os.path.exists(trades_file):
            self.logger.warning(f"No trades file found for {date_str}")
            return []
        
        return self._parse_trades_file(trades_file)
    
    def _load_trades_for_period(self, start_date: datetime, end_date: datetime) -> List[TradeRecord]:
        """Load all trades for a date range"""
        all_trades = []
        current_date = start_date
        
        while current_date <= end_date:
            daily_trades = self._load_trades_for_date(current_date)
            all_trades.extend(daily_trades)
            current_date += timedelta(days=1)
        
        return all_trades

    def _parse_trades_file(self, file_path: str) -> List[TradeRecord]:
        """Parse trades CSV file and return TradeRecord objects"""
        trades = []

        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)

                for row in reader:
                    if row.get('action') == 'ORDER_PLACED':
                        trade = TradeRecord(
                            timestamp=row.get('timestamp', ''),
                            symbol=row.get('symbol', ''),
                            action=row.get('action', ''),
                            price=float(row.get('price', 0)),
                            quantity=int(row.get('quantity', 0)),
                            order_value=float(row.get('price', 0)) * int(row.get('quantity', 0)),
                            stop_loss=float(row.get('stop_loss', 0)),
                            target=float(row.get('target', 0)),
                            strategy=row.get('strategy', ''),
                            account=row.get('account', ''),
                            order_id=row.get('order_id', '')
                        )
                        trades.append(trade)

                    elif row.get('action') == 'POSITION_CLOSED':
                        # Find corresponding open trade and update with exit info
                        order_id = row.get('order_id', '').replace('EXIT_', '')
                        for trade in trades:
                            if trade.order_id == order_id:
                                trade.exit_price = float(row.get('price', 0))
                                trade.exit_timestamp = row.get('timestamp', '')
                                trade.pnl = float(row.get('pnl', 0))
                                trade.is_winner = trade.pnl > 0

                                # Calculate duration
                                if trade.timestamp and trade.exit_timestamp:
                                    try:
                                        start_time = datetime.strptime(trade.timestamp, '%Y-%m-%d %H:%M:%S')
                                        end_time = datetime.strptime(trade.exit_timestamp, '%Y-%m-%d %H:%M:%S')
                                        trade.duration_minutes = int((end_time - start_time).total_seconds() / 60)
                                    except:
                                        trade.duration_minutes = 0
                                break

        except Exception as e:
            self.logger.error(f"Error parsing trades file {file_path}: {e}")

        return trades

    def _calculate_metrics(self, trades: List[TradeRecord], start_date: datetime, end_date: datetime = None) -> TradingMetrics:
        """Calculate comprehensive trading metrics"""
        if end_date is None:
            end_date = start_date

        metrics = TradingMetrics()

        # Filter completed trades only
        completed_trades = [t for t in trades if t.exit_price > 0]

        if not completed_trades:
            return metrics

        # Basic metrics
        metrics.total_trades = len(completed_trades)
        metrics.winning_trades = len([t for t in completed_trades if t.is_winner])
        metrics.losing_trades = metrics.total_trades - metrics.winning_trades
        metrics.win_rate = (metrics.winning_trades / metrics.total_trades * 100) if metrics.total_trades > 0 else 0

        # P&L metrics
        metrics.total_pnl = sum(t.pnl for t in completed_trades)
        winning_trades = [t for t in completed_trades if t.is_winner]
        losing_trades = [t for t in completed_trades if not t.is_winner]

        if winning_trades:
            metrics.gross_profit = sum(t.pnl for t in winning_trades)
            metrics.average_win = metrics.gross_profit / len(winning_trades)
            metrics.largest_win = max(t.pnl for t in winning_trades)

        if losing_trades:
            metrics.gross_loss = abs(sum(t.pnl for t in losing_trades))
            metrics.average_loss = metrics.gross_loss / len(losing_trades)
            metrics.largest_loss = abs(min(t.pnl for t in losing_trades))

        # Risk metrics
        if metrics.gross_loss > 0:
            metrics.profit_factor = metrics.gross_profit / metrics.gross_loss

        if metrics.total_trades > 0:
            metrics.expectancy = metrics.total_pnl / metrics.total_trades

        # Trading behavior
        if completed_trades:
            metrics.average_trade_duration = sum(t.duration_minutes for t in completed_trades) / len(completed_trades)
            metrics.total_volume = sum(t.order_value for t in completed_trades)

        # Calculate drawdown
        running_pnl = 0
        peak_pnl = 0
        max_dd = 0

        for trade in completed_trades:
            running_pnl += trade.pnl
            if running_pnl > peak_pnl:
                peak_pnl = running_pnl

            current_dd = peak_pnl - running_pnl
            if current_dd > max_dd:
                max_dd = current_dd

        metrics.max_drawdown = max_dd

        # Strategy breakdown
        strategy_stats = {}
        for trade in completed_trades:
            strategy = trade.strategy or 'Unknown'
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {
                    'trades': 0,
                    'wins': 0,
                    'pnl': 0.0,
                    'volume': 0.0
                }

            strategy_stats[strategy]['trades'] += 1
            if trade.is_winner:
                strategy_stats[strategy]['wins'] += 1
            strategy_stats[strategy]['pnl'] += trade.pnl
            strategy_stats[strategy]['volume'] += trade.order_value

        # Calculate win rates for strategies
        for strategy, stats in strategy_stats.items():
            stats['win_rate'] = (stats['wins'] / stats['trades'] * 100) if stats['trades'] > 0 else 0

        metrics.strategy_performance = strategy_stats

        return metrics

    def _get_week_range(self, date: datetime) -> Tuple[datetime, datetime]:
        """Get start and end dates for the week containing the given date"""
        # Monday is 0, Sunday is 6
        days_since_monday = date.weekday()
        week_start = date - timedelta(days=days_since_monday)
        week_end = week_start + timedelta(days=6)
        return week_start, week_end

    def _get_month_range(self, date: datetime) -> Tuple[datetime, datetime]:
        """Get start and end dates for the month containing the given date"""
        month_start = date.replace(day=1)
        last_day = calendar.monthrange(date.year, date.month)[1]
        month_end = date.replace(day=last_day)
        return month_start, month_end

    def _generate_daily_html(self, trades: List[TradeRecord], metrics: TradingMetrics, date: datetime) -> str:
        """Generate daily HTML report"""
        date_str = date.strftime('%Y-%m-%d')

        # Calculate opening and closing balance
        opening_balance = 320000.0  # Initial balance
        closing_balance = opening_balance + metrics.total_pnl

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Trading Report - {date_str}</title>
    <style>
        {self._get_css_styles()}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📊 Daily Trading Report</h1>
            <h2>{date.strftime('%A, %B %d, %Y')}</h2>
        </header>

        <div class="summary-cards">
            <div class="card">
                <h3>💰 Balance Summary</h3>
                <div class="metric">
                    <span class="label">Opening Balance:</span>
                    <span class="value">₹{opening_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Closing Balance:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{closing_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Daily P&L:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{metrics.total_pnl:,.2f}</span>
                </div>
            </div>

            <div class="card">
                <h3>📈 Trading Performance</h3>
                <div class="metric">
                    <span class="label">Total Trades:</span>
                    <span class="value">{metrics.total_trades}</span>
                </div>
                <div class="metric">
                    <span class="label">Win Rate:</span>
                    <span class="value">{metrics.win_rate:.1f}%</span>
                </div>
                <div class="metric">
                    <span class="label">Profit Factor:</span>
                    <span class="value">{metrics.profit_factor:.2f}</span>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Risk Metrics</h3>
                <div class="metric">
                    <span class="label">Largest Win:</span>
                    <span class="value positive">₹{metrics.largest_win:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Largest Loss:</span>
                    <span class="value negative">₹{metrics.largest_loss:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Expectancy:</span>
                    <span class="value">₹{metrics.expectancy:,.2f}</span>
                </div>
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-container">
                <canvas id="pnlChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="strategyChart"></canvas>
            </div>
        </div>

        <div class="trades-section">
            <h3>📋 Today's Trades</h3>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Symbol</th>
                            <th>Strategy</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Quantity</th>
                            <th>Duration</th>
                            <th>P&L</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # Add trade rows
        for trade in trades:
            if trade.exit_price > 0:  # Only completed trades
                entry_time = trade.timestamp.split(' ')[1] if ' ' in trade.timestamp else trade.timestamp
                status_class = 'win' if trade.is_winner else 'loss'
                status_text = '✅ Win' if trade.is_winner else '❌ Loss'

                html += f"""
                        <tr class="{status_class}">
                            <td>{entry_time}</td>
                            <td>{trade.symbol}</td>
                            <td>{trade.strategy}</td>
                            <td>₹{trade.price:.2f}</td>
                            <td>₹{trade.exit_price:.2f}</td>
                            <td>{trade.quantity}</td>
                            <td>{trade.duration_minutes}m</td>
                            <td class="{'positive' if trade.pnl >= 0 else 'negative'}">₹{trade.pnl:,.2f}</td>
                            <td>{status_text}</td>
                        </tr>
                """

        # Add JavaScript for charts
        strategy_labels = list(metrics.strategy_performance.keys())
        strategy_pnl = [metrics.strategy_performance[s]['pnl'] for s in strategy_labels]

        html += f"""
                    </tbody>
                </table>
            </div>
        </div>

        <footer>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Trading System v2.0</p>
        </footer>
    </div>

    <script>
        // P&L Chart
        const pnlCtx = document.getElementById('pnlChart').getContext('2d');
        new Chart(pnlCtx, {{
            type: 'bar',
            data: {{
                labels: ['Gross Profit', 'Gross Loss', 'Net P&L'],
                datasets: [{{
                    label: 'Amount (₹)',
                    data: [{metrics.gross_profit}, {-metrics.gross_loss}, {metrics.total_pnl}],
                    backgroundColor: ['#4CAF50', '#f44336', {'#4CAF50' if metrics.total_pnl >= 0 else '#f44336'}],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Daily P&L Breakdown'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});

        // Strategy Performance Chart
        const strategyCtx = document.getElementById('strategyChart').getContext('2d');
        new Chart(strategyCtx, {{
            type: 'doughnut',
            data: {{
                labels: {strategy_labels},
                datasets: [{{
                    label: 'P&L (₹)',
                    data: {strategy_pnl},
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Strategy Performance'
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
        """

        return html

    def _get_css_styles(self) -> str:
        """Get CSS styles for HTML reports"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        header h2 {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #3498db;
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .label {
            font-weight: 600;
            color: #7f8c8d;
        }

        .value {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
        }

        .positive {
            color: #27ae60 !important;
        }

        .negative {
            color: #e74c3c !important;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
            background: white;
        }

        .chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            height: 400px;
        }

        .trades-section {
            padding: 30px;
            background: white;
        }

        .trades-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .trades-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .trades-table th {
            background: #34495e;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .trades-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }

        .trades-table tr:hover {
            background: #f8f9fa;
        }

        .trades-table tr.win {
            border-left: 4px solid #27ae60;
        }

        .trades-table tr.loss {
            border-left: 4px solid #e74c3c;
        }

        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .chart-container {
                height: 300px;
            }
        }
        """

    def _generate_weekly_html(self, trades: List[TradeRecord], metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        """Generate weekly HTML report"""
        week_str = f"{start_date.strftime('%B %d')} - {end_date.strftime('%B %d, %Y')}"

        # Calculate weekly balance changes
        opening_balance = 320000.0
        closing_balance = opening_balance + metrics.total_pnl

        # Daily breakdown
        daily_pnl = {}
        current_date = start_date
        while current_date <= end_date:
            daily_trades = self._load_trades_for_date(current_date)
            daily_metrics = self._calculate_metrics(daily_trades, current_date)
            daily_pnl[current_date.strftime('%Y-%m-%d')] = daily_metrics.total_pnl
            current_date += timedelta(days=1)

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Trading Report - {week_str}</title>
    <style>
        {self._get_css_styles()}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📊 Weekly Trading Report</h1>
            <h2>{week_str}</h2>
        </header>

        <div class="summary-cards">
            <div class="card">
                <h3>💰 Weekly Balance Summary</h3>
                <div class="metric">
                    <span class="label">Week Opening Balance:</span>
                    <span class="value">₹{opening_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Week Closing Balance:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{closing_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Weekly P&L:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{metrics.total_pnl:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Weekly Return:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">{(metrics.total_pnl/opening_balance*100):.2f}%</span>
                </div>
            </div>

            <div class="card">
                <h3>📈 Weekly Performance</h3>
                <div class="metric">
                    <span class="label">Total Trades:</span>
                    <span class="value">{metrics.total_trades}</span>
                </div>
                <div class="metric">
                    <span class="label">Win Rate:</span>
                    <span class="value">{metrics.win_rate:.1f}%</span>
                </div>
                <div class="metric">
                    <span class="label">Profit Factor:</span>
                    <span class="value">{metrics.profit_factor:.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Max Drawdown:</span>
                    <span class="value negative">₹{metrics.max_drawdown:,.2f}</span>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Risk Analysis</h3>
                <div class="metric">
                    <span class="label">Average Win:</span>
                    <span class="value positive">₹{metrics.average_win:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Average Loss:</span>
                    <span class="value negative">₹{metrics.average_loss:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Expectancy:</span>
                    <span class="value">₹{metrics.expectancy:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Avg Trade Duration:</span>
                    <span class="value">{metrics.average_trade_duration:.0f} min</span>
                </div>
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-container">
                <canvas id="dailyPnlChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="strategyChart"></canvas>
            </div>
        </div>

        <footer>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Trading System v2.0</p>
        </footer>
    </div>

    <script>
        // Daily P&L Chart
        const dailyPnlCtx = document.getElementById('dailyPnlChart').getContext('2d');
        new Chart(dailyPnlCtx, {{
            type: 'line',
            data: {{
                labels: {list(daily_pnl.keys())},
                datasets: [{{
                    label: 'Daily P&L (₹)',
                    data: {list(daily_pnl.values())},
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    fill: true
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Daily P&L Trend'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});

        // Strategy Performance Chart
        const strategyCtx = document.getElementById('strategyChart').getContext('2d');
        new Chart(strategyCtx, {{
            type: 'bar',
            data: {{
                labels: {list(metrics.strategy_performance.keys())},
                datasets: [{{
                    label: 'P&L (₹)',
                    data: {[metrics.strategy_performance[s]['pnl'] for s in metrics.strategy_performance.keys()]},
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Strategy Performance Comparison'
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
        """

        return html

    def _generate_monthly_html(self, trades: List[TradeRecord], metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        """Generate monthly HTML report"""
        month_str = start_date.strftime('%B %Y')

        # Calculate monthly balance changes
        opening_balance = 320000.0
        closing_balance = opening_balance + metrics.total_pnl

        # Weekly breakdown
        weekly_pnl = {}
        current_date = start_date
        week_num = 1

        while current_date <= end_date:
            week_start, week_end = self._get_week_range(current_date)
            # Ensure we don't go beyond the month
            week_end = min(week_end, end_date)

            week_trades = self._load_trades_for_period(week_start, week_end)
            week_metrics = self._calculate_metrics(week_trades, week_start, week_end)
            weekly_pnl[f"Week {week_num}"] = week_metrics.total_pnl

            current_date = week_end + timedelta(days=1)
            week_num += 1

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Trading Report - {month_str}</title>
    <style>
        {self._get_css_styles()}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📊 Monthly Trading Report</h1>
            <h2>{month_str}</h2>
        </header>

        <div class="summary-cards">
            <div class="card">
                <h3>💰 Monthly Balance Summary</h3>
                <div class="metric">
                    <span class="label">Month Opening Balance:</span>
                    <span class="value">₹{opening_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Month Closing Balance:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{closing_balance:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Monthly P&L:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">₹{metrics.total_pnl:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Monthly Return:</span>
                    <span class="value {'positive' if metrics.total_pnl >= 0 else 'negative'}">{(metrics.total_pnl/opening_balance*100):.2f}%</span>
                </div>
            </div>

            <div class="card">
                <h3>📈 Monthly Performance</h3>
                <div class="metric">
                    <span class="label">Total Trades:</span>
                    <span class="value">{metrics.total_trades}</span>
                </div>
                <div class="metric">
                    <span class="label">Win Rate:</span>
                    <span class="value">{metrics.win_rate:.1f}%</span>
                </div>
                <div class="metric">
                    <span class="label">Profit Factor:</span>
                    <span class="value">{metrics.profit_factor:.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Max Drawdown:</span>
                    <span class="value negative">₹{metrics.max_drawdown:,.2f}</span>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Advanced Metrics</h3>
                <div class="metric">
                    <span class="label">Sharpe Ratio:</span>
                    <span class="value">{self._calculate_sharpe_ratio(trades):.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Total Volume:</span>
                    <span class="value">₹{metrics.total_volume:,.2f}</span>
                </div>
                <div class="metric">
                    <span class="label">Avg Trade Duration:</span>
                    <span class="value">{metrics.average_trade_duration:.0f} min</span>
                </div>
                <div class="metric">
                    <span class="label">Trading Days:</span>
                    <span class="value">{len([d for d in weekly_pnl.values() if d != 0])}</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 Strategy Breakdown</h3>
        """

        # Add strategy performance metrics
        for strategy, stats in metrics.strategy_performance.items():
            html += f"""
                <div class="metric">
                    <span class="label">{strategy}:</span>
                    <span class="value {'positive' if stats['pnl'] >= 0 else 'negative'}">₹{stats['pnl']:,.2f} ({stats['win_rate']:.1f}%)</span>
                </div>
            """

        html += f"""
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-container">
                <canvas id="weeklyPnlChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="strategyChart"></canvas>
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-container">
                <canvas id="drawdownChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="winLossChart"></canvas>
            </div>
        </div>

        <footer>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Trading System v2.0</p>
        </footer>
    </div>

    <script>
        // Weekly P&L Chart
        const weeklyPnlCtx = document.getElementById('weeklyPnlChart').getContext('2d');
        new Chart(weeklyPnlCtx, {{
            type: 'bar',
            data: {{
                labels: {list(weekly_pnl.keys())},
                datasets: [{{
                    label: 'Weekly P&L (₹)',
                    data: {list(weekly_pnl.values())},
                    backgroundColor: {['"#4CAF50"' if v >= 0 else '"#f44336"' for v in weekly_pnl.values()]},
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Weekly P&L Performance'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});

        // Strategy Performance Chart
        const strategyCtx = document.getElementById('strategyChart').getContext('2d');
        new Chart(strategyCtx, {{
            type: 'doughnut',
            data: {{
                labels: {list(metrics.strategy_performance.keys())},
                datasets: [{{
                    label: 'Trades',
                    data: {[metrics.strategy_performance[s]['trades'] for s in metrics.strategy_performance.keys()]},
                    backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Trade Distribution by Strategy'
                    }}
                }}
            }}
        }});

        // Win/Loss Chart
        const winLossCtx = document.getElementById('winLossChart').getContext('2d');
        new Chart(winLossCtx, {{
            type: 'pie',
            data: {{
                labels: ['Winning Trades', 'Losing Trades'],
                datasets: [{{
                    data: [{metrics.winning_trades}, {metrics.losing_trades}],
                    backgroundColor: ['#4CAF50', '#f44336'],
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Win/Loss Ratio'
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
        """

        return html

    def _calculate_sharpe_ratio(self, trades: List[TradeRecord]) -> float:
        """Calculate Sharpe ratio for the trades"""
        if not trades:
            return 0.0

        # Calculate daily returns
        daily_returns = []
        for trade in trades:
            if trade.exit_price > 0:
                daily_return = trade.pnl / trade.order_value if trade.order_value > 0 else 0
                daily_returns.append(daily_return)

        if len(daily_returns) < 2:
            return 0.0

        # Calculate mean and standard deviation
        mean_return = sum(daily_returns) / len(daily_returns)
        variance = sum((r - mean_return) ** 2 for r in daily_returns) / (len(daily_returns) - 1)
        std_dev = math.sqrt(variance)

        # Sharpe ratio (assuming risk-free rate of 0)
        if std_dev == 0:
            return 0.0

        return mean_return / std_dev * math.sqrt(252)  # Annualized
