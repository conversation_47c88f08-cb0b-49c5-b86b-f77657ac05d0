"""
Real Balance Service for AngelOne Trading Account

This service fetches and manages real account balance from AngelOne broker.
Replaces virtual account system with actual trading balance integration.

Features:
- Real-time balance fetching from AngelOne API
- Dynamic risk calculation based on actual available funds
- Order value validation against real balance
- No virtual account fallbacks
- Real transaction cost tracking
"""

import logging
from typing import Dict, Optional, Tuple, Set
from datetime import datetime
import asyncio


class RealBalanceService:
    """
    Service to manage real trading balance from AngelOne broker
    """

    def __init__(self, order_service, logger: logging.Logger):
        """
        Initialize Real Balance Service
        
        Args:
            order_service: SmartAPI order service for balance fetching
            logger: Logger instance
        """
        self.order_service = order_service
        self.logger = logger
        
        # Balance tracking
        self.current_balance: float = 0.0
        self.available_balance: float = 0.0
        self.used_margin: float = 0.0
        self.available_margin: float = 0.0  # Intraday margin/leverage
        self.last_balance_update: Optional[datetime] = None
        
        # Track traded symbols to prevent duplicates
        self.traded_stocks_today: Set[str] = set()
        self.last_trade_time: Dict[str, float] = {}
        
        # Cooldown period (30 minutes) between trades in the same stock
        self.symbol_cooldown_seconds: int = 1800  # Increased from 5 min to 30 min
        
        # Risk management settings
        self.daily_risk_percentage: float = 0.01  # 1% of total balance
        self.margin_multiplier: float = 3.5  # Intraday margin multiplier
        self.max_trades_per_day: int = 3  # Global trade limit
        
        # Daily tracking
        self.trades_placed_today: int = 0
        self.daily_pnl: float = 0.0
        self.daily_risk_amount: float = 0.0
        
        self.logger.info("🏦 Real Balance Service initialized")
        self.logger.info(f"⏳ Symbol cooldown period: {self.symbol_cooldown_seconds//60} minutes")

    async def fetch_real_balance(self) -> bool:
        """
        Fetch real account balance from AngelOne
        
        Returns:
            bool: True if balance fetched successfully, False otherwise
        """
        try:
            self.logger.info("💰 Fetching real account balance from AngelOne...")
            
            # Get balance from SmartAPI
            balance_response = await asyncio.to_thread(self.order_service.get_balance)
            
            if balance_response and balance_response.get('status'):
                balance_data = balance_response.get('data', {})
                
                # Extract balance information
                self.available_balance = float(balance_data.get('availablecash', 0.0))
                self.used_margin = float(balance_data.get('utilisedmargin', 0.0))
                # Try to fetch available margin (field name may vary by broker)
                self.available_margin = float(balance_data.get('availablemargin', 0.0)) if 'availablemargin' in balance_data else 0.0
                self.current_balance = self.available_balance + self.used_margin
                
                # Calculate daily risk amount
                self.daily_risk_amount = self.current_balance * self.daily_risk_percentage
                
                self.last_balance_update = datetime.now()
                
                self.logger.info(f"✅ Balance fetched successfully:")
                self.logger.info(f"   💵 Total Balance: ₹{self.current_balance:,.2f}")
                self.logger.info(f"   💳 Available Cash: ₹{self.available_balance:,.2f}")
                self.logger.info(f"   📊 Used Margin: ₹{self.used_margin:,.2f}")
                self.logger.info(f"   💹 Available Margin: ₹{self.available_margin:,.2f}")
                self.logger.info(f"   🎯 Daily Risk (1%): ₹{self.daily_risk_amount:,.2f}")
                self.logger.info(f"   💳 Max Order Value: ₹{self.get_max_order_value():,.2f}")
                
                return True
            else:
                error_msg = balance_response.get('message', 'Unknown error') if balance_response else 'No response'
                self.logger.error(f"❌ Failed to fetch balance: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error fetching real balance: {e}")
            return False

    def get_current_balance(self) -> float:
        """Get current total balance"""
        return self.current_balance

    def get_available_balance(self) -> float:
        """Get available cash balance"""
        return self.available_balance

    def get_daily_risk_per_trade(self) -> float:
        """Get daily risk amount per trade (1% of total balance)"""
        return self.daily_risk_amount

    def get_max_order_value(self) -> float:
        """Get maximum order value based on available balance and margin"""
        # Always use available_balance * 3.5 as per user requirement
        return self.available_balance * 3.5

    def can_place_trade(self) -> bool:
        """
        Check if a new trade can be placed based on daily limits
        
        Returns:
            bool: True if trade can be placed, False otherwise
        """
        # CRITICAL SAFETY: Double-check against absolute maximum
        ABSOLUTE_MAX_TRADES = 3
        if self.trades_placed_today >= ABSOLUTE_MAX_TRADES:
            self.logger.warning(f"❌ Hard trade limit reached: {self.trades_placed_today}/{ABSOLUTE_MAX_TRADES}")
            return False

        if self.trades_placed_today >= self.max_trades_per_day:
            self.logger.warning(f"❌ Daily trade limit reached: {self.trades_placed_today}/{self.max_trades_per_day}")
            return False
        
        if self.available_balance <= 0:
            self.logger.warning(f"❌ Insufficient available balance: ₹{self.available_balance:,.2f}")
            return False
            
        return True

    def has_traded_stock(self, symbol: str) -> bool:
        """
        Check if a stock has already been traded today
        
        Args:
            symbol: Stock symbol to check
            
        Returns:
            bool: True if stock has been traded, False otherwise
        """
        import time
        current_time = time.time()
        
        if symbol in self.traded_stocks_today:
            return True
            
        # Also check cooldown period (in case we're resuming from logs)
        if symbol in self.last_trade_time:
            time_since_last_trade = current_time - self.last_trade_time[symbol]
            if time_since_last_trade < self.symbol_cooldown_seconds:
                return True

        return False

    def mark_symbol_as_traded(self, symbol: str):
        """
        Mark a symbol as traded to prevent duplicates
        
        Args:
            symbol: Stock symbol that was traded
        """
        import time
        self.traded_stocks_today.add(symbol)
        self.last_trade_time[symbol] = time.time()

    def validate_order_value(self, order_value: float, estimated_costs: float = 0.0) -> bool:
        """
        Validate if order value is within limits
        
        Args:
            order_value: Total order value
            estimated_costs: Estimated transaction costs
            
        Returns:
            bool: True if order is valid, False otherwise
        """
        total_required = order_value + estimated_costs
        max_allowed = self.get_max_order_value()
        
        if total_required > max_allowed:
            self.logger.warning(f"❌ Order value too high: ₹{total_required:,.2f} > ₹{max_allowed:,.2f}")
            return False
        return True

    def calculate_position_size(self, entry_price: float, stop_loss: float) -> int:
        """
        Calculate position size based on user requirements:
        quantity = available_balance * 3.5 / share_price
        with constraint: quantity * risk <= 1% of morning balance

        Args:
            entry_price: Entry price per share
            stop_loss: Stop loss price per share

        Returns:
            int: Quantity to trade
        """
        if entry_price <= 0 or stop_loss <= 0:
            return 0
            
        risk_per_share = abs(entry_price - stop_loss)
        if risk_per_share <= 0:
            return 0
            
        # USER REQUIREMENT: Calculate quantity as available_balance * 3.5 / share_price
        quantity = int((self.available_balance * 3.5) / entry_price)

        # Ensure minimum quantity of 1
        quantity = max(1, quantity)

        # USER REQUIREMENT: Ensure quantity * risk <= 1% of morning balance
        total_risk = quantity * risk_per_share
        max_allowed_risk = self.daily_risk_amount  # This is 1% of morning balance

        if total_risk > max_allowed_risk:
            # Reduce quantity to meet 1% risk limit
            risk_limited_quantity = int(max_allowed_risk / risk_per_share)
            risk_limited_quantity = max(1, risk_limited_quantity)

            self.logger.warning(f"⚠️ Position size reduced due to 1% risk limit")
            self.logger.warning(f"   Formula-based: {quantity} shares (Risk: ₹{total_risk:,.2f})")
            self.logger.warning(f"   Risk-limited: {risk_limited_quantity} shares (Risk: ₹{risk_limited_quantity * risk_per_share:,.2f})")
            quantity = risk_limited_quantity

        # Log the calculation for transparency
        order_value = entry_price * quantity
        self.logger.info(f"📊 Position Size Calculation for entry price ₹{entry_price:.2f}:")
        self.logger.info(f"   Available Balance: ₹{self.available_balance:,.2f}")
        self.logger.info(f"   Formula: {self.available_balance:,.2f} * 3.5 / {entry_price:.2f} = {quantity} shares")
        self.logger.info(f"   Order Value: ₹{order_value:,.2f}")
        self.logger.info(f"   Risk per Share: ₹{risk_per_share:.2f}")
        self.logger.info(f"   Total Risk: ₹{quantity * risk_per_share:,.2f} (Max: ₹{max_allowed_risk:,.2f})")

        return quantity

    def get_today_trade_count(self) -> int:
        """
        Get the number of trades executed today using SmartAPI getTradeBook
        Returns:
            int: Number of trades executed today
        """
        try:
            trade_book = self.order_service.get_trade_book()  # This should call SmartAPI's getTradeBook
            if trade_book and trade_book.get('status') and 'data' in trade_book:
                return len(trade_book['data'])
            return 0
        except Exception as e:
            self.logger.error(f"Error fetching today's trade count: {e}")
            return 0

    def record_trade_placed(self, order_value: float = 0.0):
        """
        Record that a trade has been placed
        
        Args:
            order_value: Value of the order placed (optional)
        """
        self.trades_placed_today += 1
        self.logger.info(f"📈 Trade recorded: {self.trades_placed_today}/{self.max_trades_per_day} trades used")
        if order_value > 0:
            self.logger.info(f"💰 Order value: ₹{order_value:,.2f}")

    def get_trading_summary(self) -> Dict:
        """
        Get current trading summary
        
        Returns:
            Dict: Trading summary with balance and limits
        """
        return {
            'current_balance': self.current_balance,
            'available_balance': self.available_balance,
            'used_margin': self.used_margin,
            'daily_risk_amount': self.daily_risk_amount,
            'max_order_value': self.get_max_order_value(),
            'trades_placed_today': self.trades_placed_today,
            'max_trades_per_day': self.max_trades_per_day,
            'trades_remaining': self.max_trades_per_day - self.trades_placed_today,
            'can_place_trade': self.can_place_trade(),
            'last_balance_update': self.last_balance_update
        }

    def reset_daily_counters(self):
        """Reset daily counters for new trading day"""
        self.trades_placed_today = 0
        self.daily_pnl = 0.0
        self.logger.info("🔄 Daily counters reset for new trading day")

    def log_balance_summary(self):
        """Log detailed balance summary"""
        self.logger.info("💰 REAL BALANCE SUMMARY")
        self.logger.info("=" * 50)
        self.logger.info(f"💵 Total Balance: ₹{self.current_balance:,.2f}")
        self.logger.info(f"💳 Available Cash: ₹{self.available_balance:,.2f}")
        self.logger.info(f"📊 Used Margin: ₹{self.used_margin:,.2f}")
        self.logger.info(f"💹 Available Margin: ₹{self.available_margin:,.2f}")
        self.logger.info(f"🎯 Daily Risk (1%): ₹{self.daily_risk_amount:,.2f}")
        self.logger.info(f"💳 Max Order Value: ₹{self.get_max_order_value():,.2f}")
        self.logger.info(f"📈 Trades Today: {self.trades_placed_today}/{self.max_trades_per_day}")
        self.logger.info(f"✅ Can Trade: {'Yes' if self.can_place_trade() else 'No'}")
        self.logger.info("=" * 50)
