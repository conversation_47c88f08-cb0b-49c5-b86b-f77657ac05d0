"""
Centralized Symbol Mapping Service for MongoDB
Downloads and manages symbol mappings for Nifty 500 stocks and Bank Nifty options
"""

import os
import logging
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pymongo
from pymongo import MongoClient
import yfinance as yf
from dotenv import load_dotenv
import io

# Load environment variables
load_dotenv()

class SymbolMappingService:
    """Centralized service for managing symbol mappings in MongoDB"""

    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

        # MongoDB connection
        self.mongodb_client = None
        self.db = None
        self.mongodb_connected = False

        # Collections
        self.equity_collection = None
        self.option_collection = None
        self.index_collection = None

        # Connect to MongoDB
        self._connect_mongodb()

    def _connect_mongodb(self):
        """Connect to MongoDB"""
        try:
            connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017/")
            # Use the same database as the trading system
            database_name = os.getenv("MONGODB_DATABASE_NAME", os.getenv("DB_NAME", "intraday_trading"))

            self.mongodb_client = MongoClient(connection_string)
            self.db = self.mongodb_client[database_name]

            # Test connection
            self.mongodb_client.admin.command('ping')
            self.mongodb_connected = True

            # Initialize collections
            self.equity_collection = self.db['symbol_mappings_equity']
            self.option_collection = self.db['symbol_mappings_options']
            self.index_collection = self.db['symbol_mappings_indices']
            self.stock_categories_collection = self.db['stock_categories']

            # Create indexes for better performance
            self._create_indexes()

            self.logger.info("✅ Connected to MongoDB for symbol mapping service")

        except Exception as e:
            self.logger.error(f"❌ MongoDB connection failed: {e}")
            self.mongodb_connected = False

    def _create_indexes(self):
        """Create indexes for better query performance"""
        try:
            # Helper function to create index safely
            def create_index_safe(collection, index_spec, **kwargs):
                try:
                    collection.create_index(index_spec, **kwargs)
                except pymongo.errors.OperationFailure as e:
                    if "already exists" in str(e) or "IndexKeySpecsConflict" in str(e):
                        # Index already exists, skip
                        pass
                    else:
                        raise e

            # Equity collection indexes
            create_index_safe(self.equity_collection, [("symbol", 1)], unique=True, name="equity_symbol_unique")
            create_index_safe(self.equity_collection, [("smartapi_token", 1)], name="equity_smartapi_token")
            create_index_safe(self.equity_collection, [("yahoo_symbol", 1)], name="equity_yahoo_symbol")

            # Option collection indexes
            create_index_safe(self.option_collection, [("symbol", 1)], unique=True, name="option_symbol_unique")
            create_index_safe(self.option_collection, [("smartapi_token", 1)], name="option_smartapi_token")
            create_index_safe(self.option_collection, [("underlying", 1)], name="option_underlying")
            create_index_safe(self.option_collection, [("strike", 1)], name="option_strike")
            create_index_safe(self.option_collection, [("option_type", 1)], name="option_type")
            create_index_safe(self.option_collection, [("expiry", 1)], name="option_expiry")

            # Index collection indexes
            create_index_safe(self.index_collection, [("symbol", 1)], unique=True, name="index_symbol_unique")
            create_index_safe(self.index_collection, [("smartapi_token", 1)], name="index_smartapi_token")

            # Stock categories collection indexes
            create_index_safe(self.stock_categories_collection, [("symbol", 1), ("category", 1)], unique=True, name="stock_category_unique")
            create_index_safe(self.stock_categories_collection, [("category", 1)], name="stock_category")
            create_index_safe(self.stock_categories_collection, [("index_name", 1)], name="stock_index_name")
            create_index_safe(self.stock_categories_collection, [("symbol", 1)], name="stock_symbol")  # Non-unique index for symbol lookup

            self.logger.info("✅ Created MongoDB indexes for symbol mappings")

        except Exception as e:
            self.logger.warning(f"⚠️ Error creating indexes: {e}")

    def download_smartapi_master_data(self) -> Optional[pd.DataFrame]:
        """Download SmartAPI master instrument file with multiple fallback URLs"""
        try:
            self.logger.info("📡 Downloading SmartAPI master instrument file...")

            # Multiple URLs to try for SmartAPI master data
            urls = [
                'https://margincalculator.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json',
                'https://smartapi.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json',
                'https://apiconnect.angelbroking.com/OpenAPI_File/files/OpenAPIScripMaster.json'
            ]

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }

            for url in urls:
                try:
                    self.logger.info(f"🔄 Trying SmartAPI URL: {url}")
                    response = requests.get(url, headers=headers, timeout=60)

                    if response.status_code == 200:
                        data = response.json()
                        df = pd.DataFrame.from_dict(data)

                        # Clean and process data
                        df['expiry'] = pd.to_datetime(df['expiry'], errors='coerce')
                        df['strike'] = pd.to_numeric(df['strike'], errors='coerce') / 100  # Convert from paise

                        self.logger.info(f"✅ Downloaded {len(df)} instruments from SmartAPI ({url})")

                        # Log sample data for verification
                        if not df.empty:
                            equity_count = len(df[df['instrumenttype'] == ''])
                            option_count = len(df[df['instrumenttype'] == 'OPTIDX'])
                            future_count = len(df[df['instrumenttype'] == 'FUTSTK'])
                            self.logger.info(f"📊 Sample breakdown: {equity_count} equities, {option_count} options, {future_count} futures")

                        return df
                    else:
                        self.logger.warning(f"⚠️ Failed to download from {url}: {response.status_code}")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error with SmartAPI URL {url}: {e}")
                    continue

            self.logger.error("❌ All SmartAPI URLs failed")
            return None

        except Exception as e:
            self.logger.error(f"❌ Error downloading SmartAPI master data: {e}")
            return None

    def download_current_nifty_500_list(self) -> Optional[List[str]]:
        """Download current Nifty 500 stocks list from NSE official source"""
        try:
            self.logger.info("📡 Downloading current Nifty 500 list from NSE...")

            # Official NSE Nifty 500 CSV URL
            url = 'https://nsearchives.nseindia.com/content/indices/ind_nifty500list.csv'

            # Set headers to mimic browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code != 200:
                self.logger.warning(f"⚠️ Failed to download from NSE (status: {response.status_code}), using fallback list")
                return self._get_fallback_nifty_500_list()

            # Parse CSV content
            csv_content = response.text
            df = pd.read_csv(io.StringIO(csv_content))

            # Extract symbols from the 'Symbol' column
            if 'Symbol' in df.columns:
                symbols = df['Symbol'].dropna().tolist()
                # Clean symbols (remove any whitespace)
                symbols = [symbol.strip() for symbol in symbols if symbol.strip()]

                self.logger.info(f"✅ Downloaded {len(symbols)} symbols from NSE Nifty 500 list")

                # Log first few symbols for verification
                if symbols:
                    self.logger.info(f"📊 Sample symbols: {symbols[:10]}")

                return symbols
            else:
                self.logger.warning("⚠️ 'Symbol' column not found in NSE CSV, using fallback list")
                return self._get_fallback_nifty_500_list()

        except Exception as e:
            self.logger.warning(f"⚠️ Error downloading Nifty 500 list from NSE: {e}")
            self.logger.info("🔄 Using fallback Nifty 500 list...")
            return self._get_fallback_nifty_500_list()

    def _get_fallback_nifty_500_list(self) -> List[str]:
        """Get fallback Nifty 500 list in case NSE download fails"""
        # This is a comprehensive list based on the latest available data
        # Updated as of May 2025 from NSE official source
        return [
            '360ONE', '3MINDIA', 'ABB', 'ACC', 'ACMESOLAR', 'AIAENG', 'APLAPOLLO', 'AUBANK', 'AWL',
            'AADHARHFC', 'AARTIIND', 'AAVAS', 'ABBOTINDIA', 'ACE', 'ADANIENSOL', 'ADANIENT', 'ADANIGREEN',
            'ADANIPORTS', 'ADANIPOWER', 'ATGL', 'ABCAPITAL', 'ABFRL', 'ABREL', 'ABSLAMC', 'AEGISLOG',
            'AFCONS', 'AFFLE', 'AJANTPHARM', 'AKUMS', 'APLLTD', 'ALIVUS', 'ALKEM', 'ALKYLAMINE',
            'ALOKINDS', 'ARE&M', 'AMBER', 'AMBUJACEM', 'ANANDRATHI', 'ANANTRAJ', 'ANGELONE', 'APARINDS',
            'APOLLOHOSP', 'APOLLOTYRE', 'APTUS', 'ASAHIINDIA', 'ASHOKLEY', 'ASIANPAINT', 'ASTERDM',
            'ASTRAZEN', 'ASTRAL', 'ATUL', 'AUROPHARMA', 'AIIL', 'DMART', 'AXISBANK', 'BASF',
            'BEML', 'BLS', 'BSE', 'BAJAJ-AUTO', 'BAJFINANCE', 'BAJAJFINSV', 'BAJAJHLDNG', 'BAJAJHFL',
            'BALKRISIND', 'BALRAMCHIN', 'BANDHANBNK', 'BANKBARODA', 'BANKINDIA', 'MAHABANK', 'BATAINDIA',
            'BAYERCROP', 'BERGEPAINT', 'BDL', 'BEL', 'BHARATFORG', 'BHEL', 'BPCL', 'BHARTIARTL',
            'BHARTIHEXA', 'BIKAJI', 'BIOCON', 'BSOFT', 'BLUEDART', 'BLUESTARCO', 'BBTC', 'BOSCHLTD',
            'FIRSTCRY', 'BRIGADE', 'BRITANNIA', 'MAPMYINDIA', 'CCL', 'CESC', 'CGPOWER', 'CRISIL',
            'CAMPUS', 'CANFINHOME', 'CANBK', 'CAPLIPOINT', 'CGCL', 'CARBORUNIV', 'CASTROLIND', 'CEATLTD',
            'CENTRALBK', 'CDSL', 'CENTURYPLY', 'CERA', 'CHALET', 'CHAMBLFERT', 'CHENNPETRO', 'CHOLAHLDNG',
            'CHOLAFIN', 'CIPLA', 'CUB', 'CLEAN', 'COALINDIA', 'COCHINSHIP', 'COFORGE', 'COHANCE',
            'COLPAL', 'CAMS', 'CONCORDBIO', 'CONCOR', 'COROMANDEL', 'CRAFTSMAN', 'CREDITACC', 'CROMPTON',
            'CUMMINSIND', 'CYIENT', 'DCMSHRIRAM', 'DLF', 'DOMS', 'DABUR', 'DALBHARAT', 'DATAPATTNS',
            'DEEPAKFERT', 'DEEPAKNTR', 'DELHIVERY', 'DEVYANI', 'DIVISLAB', 'DIXON', 'LALPATHLAB', 'DRREDDY',
            'DUMMYABFRL', 'DUMMYSIEMS', 'DUMMYRAYMN', 'EIDPARRY', 'EIHOTEL', 'EICHERMOT', 'ELECON',
            'ELGIEQUIP', 'EMAMILTD', 'EMCURE', 'ENDURANCE', 'ENGINERSIN', 'ERIS', 'ESCORTS', 'ETERNAL',
            'EXIDEIND', 'NYKAA', 'FEDERALBNK', 'FACT', 'FINCABLES', 'FINPIPE', 'FSL', 'FIVESTAR',
            'FORTIS', 'GAIL', 'GVT&D', 'GMRAIRPORT', 'GRSE', 'GICRE', 'GILLETTE', 'GLAND',
            'GLAXO', 'GLENMARK', 'MEDANTA', 'GODIGIT', 'GPIL', 'GODFRYPHLP', 'GODREJAGRO', 'GODREJCP',
            'GODREJIND', 'GODREJPROP', 'GRANULES', 'GRAPHITE', 'GRASIM', 'GRAVITA', 'GESHIP', 'FLUOROCHEM',
            'GUJGASLTD', 'GMDCLTD', 'GNFC', 'GPPL', 'GSPL', 'HEG', 'HBLENGINE', 'HCLTECH',
            'HDFCAMC', 'HDFCBANK', 'HDFCLIFE', 'HFCL', 'HAPPSTMNDS', 'HAVELLS', 'HEROMOTOCO', 'HSCL',
            'HINDALCO', 'HAL', 'HINDCOPPER', 'HINDPETRO', 'HINDUNILVR', 'HINDZINC', 'POWERINDIA', 'HOMEFIRST',
            'HONASA', 'HONAUT', 'HUDCO', 'HYUNDAI', 'ICICIBANK', 'ICICIGI', 'ICICIPRULI', 'IDBI',
            'IDFCFIRSTB', 'IFCI', 'IIFL', 'INOXINDIA', 'IRB', 'IRCON', 'ITC', 'ITI',
            'INDGN', 'INDIACEM', 'INDIAMART', 'INDIANB', 'IEX', 'INDHOTEL', 'IOC', 'IOB',
            'IRCTC', 'IRFC', 'IREDA', 'IGL', 'INDUSTOWER', 'INDUSINDBK', 'NAUKRI', 'INFY',
            'INOXWIND', 'INTELLECT', 'INDIGO', 'IGIL', 'IKS', 'IPCALAB', 'JBCHEPHARM', 'JKCEMENT',
            'JBMA', 'JKTYRE', 'JMFINANCIL', 'JSWENERGY', 'JSWHL', 'JSWINFRA', 'JSWSTEEL', 'JPPOWER',
            'J&KBANK', 'JINDALSAW', 'JSL', 'JINDALSTEL', 'JIOFIN', 'JUBLFOOD', 'JUBLINGREA', 'JUBLPHARMA',
            'JWL', 'JUSTDIAL', 'JYOTHYLAB', 'JYOTICNC', 'KPRMILL', 'KEI', 'KNRCON', 'KPITTECH',
            'KAJARIACER', 'KPIL', 'KALYANKJIL', 'KANSAINER', 'KARURVYSYA', 'KAYNES', 'KEC', 'KFINTECH',
            'KIRLOSBROS', 'KIRLOSENG', 'KOTAKBANK', 'KIMS', 'LTF', 'LTTS', 'LICHSGFIN', 'LTFOODS',
            'LTIM', 'LT', 'LATENTVIEW', 'LAURUSLABS', 'LEMONTREE', 'LICI', 'LINDEINDIA', 'LLOYDSME',
            'LUPIN', 'MMTC', 'MRF', 'LODHA', 'MGL', 'MAHSEAMLES', 'M&MFIN', 'M&M',
            'MANAPPURAM', 'MRPL', 'MANKIND', 'MARICO', 'MARUTI', 'MASTEK', 'MFSL', 'MAXHEALTH',
            'MAZDOCK', 'METROPOLIS', 'MINDACORP', 'MSUMI', 'MOTILALOFS', 'MPHASIS', 'MCX', 'MUTHOOTFIN',
            'NATCOPHARM', 'NBCC', 'NCC', 'NHPC', 'NLCINDIA', 'NMDC', 'NSLNISP', 'NTPCGREEN',
            'NTPC', 'NH', 'NATIONALUM', 'NAVA', 'NAVINFLUOR', 'NESTLEIND', 'NETWEB', 'NETWORK18',
            'NEULANDLAB', 'NEWGEN', 'NAM-INDIA', 'NIVABUPA', 'NUVAMA', 'OBEROIRLTY', 'ONGC', 'OIL',
            'OLAELEC', 'OLECTRA', 'PAYTM', 'OFSS', 'POLICYBZR', 'PCBL', 'PGEL', 'PIIND',
            'PNBHOUSING', 'PNCINFRA', 'PTCIL', 'PVRINOX', 'PAGEIND', 'PATANJALI', 'PERSISTENT', 'PETRONET',
            'PFIZER', 'PHOENIXLTD', 'PIDILITIND', 'PEL', 'PPLPHARMA', 'POLYMED', 'POLYCAB', 'POONAWALLA',
            'PFC', 'POWERGRID', 'PRAJIND', 'PREMIERENE', 'PRESTIGE', 'PNB', 'RRKABEL', 'RBLBANK',
            'RECLTD', 'RHIM', 'RITES', 'RADICO', 'RVNL', 'RAILTEL', 'RAINBOW', 'RKFORGE',
            'RCF', 'RTNINDIA', 'RAYMONDLSL', 'RAYMOND', 'REDINGTON', 'RELIANCE', 'RPOWER', 'ROUTE',
            'SBFC', 'SBICARD', 'SBILIFE', 'SJVN', 'SKFINDIA', 'SRF', 'SAGILITY', 'SAILIFE',
            'SAMMAANCAP', 'MOTHERSON', 'SAPPHIRE', 'SARDAEN', 'SAREGAMA', 'SCHAEFFLER', 'SCHNEIDER', 'SCI',
            'SHREECEM', 'RENUKA', 'SHRIRAMFIN', 'SHYAMMETL', 'SIEMENS', 'SIGNATURE', 'SOBHA', 'SOLARINDS',
            'SONACOMS', 'SONATSOFTW', 'STARHEALTH', 'SBIN', 'SAIL', 'SWSOLAR', 'SUMICHEM', 'SUNPHARMA',
            'SUNTV', 'SUNDARMFIN', 'SUNDRMFAST', 'SUPREMEIND', 'SUZLON', 'SWANENERGY', 'SWIGGY', 'SYNGENE',
            'SYRMA', 'TBOTEK', 'TVSMOTOR', 'TANLA', 'TATACHEM', 'TATACOMM', 'TCS', 'TATACONSUM',
            'TATAELXSI', 'TATAINVEST', 'TATAMOTORS', 'TATAPOWER', 'TATASTEEL', 'TATATECH', 'TTML', 'TECHM',
            'TECHNOE', 'TEJASNET', 'NIACL', 'RAMCOCEM', 'THERMAX', 'TIMKEN', 'TITAGARH', 'TITAN',
            'TORNTPHARM', 'TORNTPOWER', 'TARIL', 'TRENT', 'TRIDENT', 'TRIVENI', 'TRITURBINE', 'TIINDIA',
            'UCOBANK', 'UNOMINDA', 'UPL', 'UTIAMC', 'ULTRACEMCO', 'UNIONBANK', 'UBL', 'UNITDSPR',
            'USHAMART', 'VGUARD', 'DBREALTY', 'VTL', 'VBL', 'MANYAVAR', 'VEDL', 'VIJAYA',
            'VMM', 'IDEA', 'VOLTAS', 'WAAREEENER', 'WELCORP', 'WELSPUNLIV', 'WESTLIFE', 'WHIRLPOOL',
            'WIPRO', 'WOCKPHARMA', 'YESBANK', 'ZFCVINDIA', 'ZEEL', 'ZENTEC', 'ZENSARTECH', 'ZYDUSLIFE',
            'ECLERX'
        ]

    def _get_fallback_nifty_50_list(self) -> List[str]:
        """Get fallback Nifty 50 list in case NSE download fails"""
        return [
            'RELIANCE', 'TCS', 'HDFCBANK', 'INFY', 'ICICIBANK', 'HINDUNILVR', 'SBIN',
            'BHARTIARTL', 'ITC', 'KOTAKBANK', 'LT', 'ASIANPAINT', 'AXISBANK', 'MARUTI',
            'BAJFINANCE', 'HCLTECH', 'WIPRO', 'ULTRACEMCO', 'SUNPHARMA', 'TITAN',
            'NESTLEIND', 'POWERGRID', 'NTPC', 'COALINDIA', 'ONGC', 'TATAMOTORS',
            'TATASTEEL', 'BAJAJ-AUTO', 'M&M', 'TECHM', 'DRREDDY', 'EICHERMOT',
            'ADANIPORTS', 'JSWSTEEL', 'TATACONSUM', 'GRASIM', 'CIPLA', 'HINDALCO',
            'APOLLOHOSP', 'HEROMOTOCO', 'UPL', 'BAJAJFINSV', 'DIVISLAB', 'BRITANNIA',
            'INDUSINDBK', 'ADANIENT', 'TRENT', 'HDFCLIFE', 'SBILIFE'
        ]

    def _get_fallback_nifty_100_list(self) -> List[str]:
        """Get fallback Nifty 100 list in case NSE download fails"""
        nifty_50 = self._get_fallback_nifty_50_list()
        additional_50 = [
            'DMART', 'GODREJCP', 'DABUR', 'MARICO', 'COLPAL', 'PIDILITIND',
            'HAVELLS', 'VOLTAS', 'MPHASIS', 'BANKBARODA', 'CANBK', 'PNB',
            'FEDERALBNK', 'IDFCFIRSTB', 'BANDHANBNK', 'RBLBANK', 'AUBANK', 'CHOLAFIN',
            'BAJAJHLDNG', 'MFSL', 'SBICARD', 'ICICIGI', 'ICICIPRULI', 'LICI',
            'NAUKRI', 'PAYTM', 'POLICYBZR', 'DELHIVERY', 'NYKAA', 'SWIGGY',
            'ADANIGREEN', 'ADANIPOWER', 'ADANIENSOL', 'ATGL', 'JSWENERGY', 'TATAPOWER',
            'NHPC', 'SJVN', 'IREDA', 'RECLTD', 'PFC', 'IRFC',
            'SAIL', 'HINDZINC', 'VEDL', 'NATIONALUM', 'NMDC', 'HAL',
            'IOC', 'BPCL', 'HINDPETRO'
        ]
        return nifty_50 + additional_50

    def _get_fallback_nifty_200_list(self) -> List[str]:
        """Get fallback Nifty 200 list in case NSE download fails"""
        nifty_100 = self._get_fallback_nifty_100_list()
        additional_100 = [
            'ACC', 'AMBUJACEM', 'SHREECEM', 'RAMCOCEM', 'BHARATFORG', 'ESCORTS',
            'EXIDEIND', 'MOTHERSON', 'BALKRISIND', 'APOLLOTYRE', 'MRF', 'ASHOKLEY',
            'TVSMOTOR', 'BOSCHLTD', 'BHEL', 'SIEMENS', 'ABB', 'CROMPTON',
            'POLYCAB', 'KEI', 'WHIRLPOOL', 'BLUESTARCO', 'DIXON', 'LTIM',
            'LUPIN', 'MANAPPURAM', 'METROPOLIS', 'MINDACORP', 'MOTILALOFS', 'MUTHOOTFIN'
        ]
        return nifty_100 + additional_100

    def _get_fallback_fno_list(self) -> List[str]:
        """Get fallback FNO enabled stocks list in case NSE download fails - Updated 2025"""
        # Comprehensive list of FNO enabled stocks (185+ stocks as per current NSE FNO list)
        fno_stocks = [
            # Major Large Cap FNO Stocks
            'RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'HINDUNILVR', 'INFY', 'ITC',
            'SBIN', 'BHARTIARTL', 'BAJFINANCE', 'KOTAKBANK', 'LT', 'ASIANPAINT',
            'AXISBANK', 'MARUTI', 'SUNPHARMA', 'ULTRACEMCO', 'TITAN', 'NESTLEIND',
            'WIPRO', 'NTPC', 'POWERGRID', 'COALINDIA', 'ONGC', 'IOC', 'GRASIM',
            'JSWSTEEL', 'TATASTEEL', 'HINDALCO', 'ADANIPORTS', 'ADANIENT',

            # Banking & Financial Services
            'HDFCLIFE', 'SBILIFE', 'BAJAJFINSV', 'ICICIPRULI', 'LICI', 'INDUSINDBK',
            'FEDERALBNK', 'BANKBARODA', 'PNB', 'CANBK', 'IDFCFIRSTB', 'AUBANK',
            'BAJAJHLDNG', 'MUTHOOTFIN', 'CHOLAFIN', 'MANAPPURAM', 'PNBHOUSING',
            'BANDHANBNK', 'RBLBANK', 'SBICARD', 'ICICIGI', 'MFSL',

            # Auto & Auto Components
            'BAJAJ-AUTO', 'M&M', 'EICHERMOT', 'HEROMOTOCO', 'TVSMOTOR', 'ASHOKLEY',
            'TATAMOTORS', 'BOSCHLTD', 'MOTHERSON', 'BALKRISIND', 'MRF', 'APOLLOTYRE',
            'BHARATFORG', 'EXIDEIND', 'ESCORTS', 'MAHINDCIE', 'AMARAJABAT',

            # IT & Technology
            'HCLTECH', 'TECHM', 'LTIM', 'PERSISTENT', 'COFORGE', 'MPHASIS',
            'LTTS', 'OFSS', 'KPITTECH', 'CYIENT', 'MINDTREE',

            # Pharma & Healthcare
            'DRREDDY', 'CIPLA', 'DIVISLAB', 'BIOCON', 'LUPIN', 'AUROPHARMA', 'TORNTPHARM',
            'CADILAHC', 'ALKEM', 'GLENMARK', 'ABBOTINDIA', 'APOLLOHOSP', 'FORTIS',
            'MAXHEALTH', 'LALPATHLAB', 'METROPOLIS', 'DRLABS', 'MANKIND',

            # FMCG & Consumer
            'BRITANNIA', 'DABUR', 'GODREJCP', 'MARICO', 'COLPAL', 'EMAMILTD',
            'TATACONSUM', 'VBL', 'RADICO', 'JYOTHYLAB', 'GILLETTE', 'PGHH',
            'TRENT', 'DMART', 'NYKAA', 'JUBLFOOD',

            # Metals & Mining
            'VEDL', 'HINDZINC', 'NATIONALUM', 'SAIL', 'JINDALSTEL', 'JSL',
            'NMDC', 'MOIL', 'RATNAMANI', 'WELCORP', 'WELSPUNIND',

            # Cement & Construction
            'SHREECEM', 'AMBUJACEM', 'ACC', 'RAMCOCEM', 'HEIDELBERG', 'JKCEMENT',
            'ORIENTCEM', 'DALMIACEM', 'PRISMCEM', 'STARCEMENT',

            # Energy & Power
            'ADANIGREEN', 'ADANITRANS', 'TATAPOWER', 'NHPC', 'SJVN', 'THERMAX',
            'BHEL', 'CESC', 'TORNTPOWER', 'RPOWER', 'SUZLON', 'INOXWIND',
            'IREDA', 'RECLTD', 'PFC', 'IRFC',

            # Telecom & Media
            'IDEA', 'ZEEL', 'SUNTV', 'NETWORK18', 'HATHWAY', 'GTLINFRA',

            # Chemicals & Petrochemicals
            'TATACHEM', 'PIDILITIND', 'AARTI', 'DEEPAKNTR', 'GNFC', 'GUJALKALI',
            'NOCIL', 'CHAMBLFERT', 'COROMANDEL', 'KANSAINER', 'NAVINFLUOR',
            'BPCL', 'HINDPETRO', 'GAIL', 'PETRONET',

            # Real Estate & Infrastructure
            'DLF', 'GODREJPROP', 'OBEROIRLTY', 'PRESTIGE', 'SOBHA', 'BRIGADE',
            'PHOENIXLTD', 'MAHLIFE', 'LODHA', 'MACROTECH', 'SUNTECK',
            'IRB', 'GMRAIRPORT', 'CONCOR', 'RVNL',

            # Others & New Age
            'ZOMATO', 'PAYTM', 'POLICYBZR', 'IRCTC', 'RAILTEL', 'MAZAGON',
            'BEL', 'HAL', 'COCHINSHIP', 'GRSE', 'BEML', 'BHARAT', 'NBCC',
            'NLCINDIA', 'HUDCO', 'TITAGARH', 'KAYNES', 'DIXON', 'VOLTAS',
            'CROMPTON', 'HAVELLS', 'POLYCAB', 'KEI', 'CUMMINSIND', 'SIEMENS',
            'ABB', 'SCHAEFFLER', 'TIMKEN', 'THERMAX', 'CAMS', 'CDSL',
            'MCX', 'NAUKRI', 'INDIGO', 'SPICEJET', 'EASEMYTRIP'
        ]

        # Remove duplicates while preserving order
        seen = set()
        unique_fno_stocks = []
        for stock in fno_stocks:
            if stock not in seen:
                seen.add(stock)
                unique_fno_stocks.append(stock)

        return unique_fno_stocks

    def download_current_nifty_50_list(self) -> Optional[List[str]]:
        """Download current Nifty 50 stocks list from NSE official source"""
        try:
            self.logger.info("📡 Downloading current Nifty 50 list from NSE...")
            url = 'https://nsearchives.nseindia.com/content/indices/ind_nifty50list.csv'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                df = pd.read_csv(io.StringIO(response.text))
                if 'Symbol' in df.columns:
                    symbols = [symbol.strip() for symbol in df['Symbol'].dropna().tolist() if symbol.strip()]
                    self.logger.info(f"✅ Downloaded {len(symbols)} Nifty 50 symbols")
                    return symbols
            return None
        except Exception as e:
            self.logger.warning(f"⚠️ Error downloading Nifty 50 list: {e}")
            return None

    def download_current_nifty_100_list(self) -> Optional[List[str]]:
        """Download current Nifty 100 stocks list from NSE official source"""
        try:
            self.logger.info("📡 Downloading current Nifty 100 list from NSE...")
            url = 'https://nsearchives.nseindia.com/content/indices/ind_nifty100list.csv'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                df = pd.read_csv(io.StringIO(response.text))
                if 'Symbol' in df.columns:
                    symbols = [symbol.strip() for symbol in df['Symbol'].dropna().tolist() if symbol.strip()]
                    self.logger.info(f"✅ Downloaded {len(symbols)} Nifty 100 symbols")
                    return symbols
            return None
        except Exception as e:
            self.logger.warning(f"⚠️ Error downloading Nifty 100 list: {e}")
            return None

    def download_current_nifty_200_list(self) -> Optional[List[str]]:
        """Download current Nifty 200 stocks list from NSE official source"""
        try:
            self.logger.info("📡 Downloading current Nifty 200 list from NSE...")
            url = 'https://nsearchives.nseindia.com/content/indices/ind_nifty200list.csv'
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                df = pd.read_csv(io.StringIO(response.text))
                if 'Symbol' in df.columns:
                    symbols = [symbol.strip() for symbol in df['Symbol'].dropna().tolist() if symbol.strip()]
                    self.logger.info(f"✅ Downloaded {len(symbols)} Nifty 200 symbols")
                    return symbols
            return None
        except Exception as e:
            self.logger.warning(f"⚠️ Error downloading Nifty 200 list: {e}")
            return None

    def download_current_fno_list(self) -> Optional[List[str]]:
        """Download current FNO enabled stocks list from NSE official source with multiple fallback URLs"""
        try:
            self.logger.info("📡 Downloading current FNO stocks list from NSE...")

            # Multiple URLs to try for FNO data
            urls = [
                'https://nsearchives.nseindia.com/content/fo/fo_mktlots.csv',
                'https://www.nseindia.com/content/fo/fo_mktlots.csv',
                'https://archives.nseindia.com/content/fo/fo_mktlots.csv'
            ]

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }

            for url in urls:
                try:
                    self.logger.info(f"🔄 Trying URL: {url}")
                    response = requests.get(url, headers=headers, timeout=30)

                    if response.status_code == 200:
                        # Try CSV parsing first
                        try:
                            df = pd.read_csv(io.StringIO(response.text))

                            # Extract symbols from the 'SYMBOL' column (try different column names)
                            symbol_column = None
                            for col in ['SYMBOL', 'Symbol', 'symbol', 'UNDERLYING', 'Underlying']:
                                if col in df.columns:
                                    symbol_column = col
                                    break

                            if symbol_column:
                                fno_symbols = df[symbol_column].dropna().tolist()
                                # Clean symbols (remove any whitespace)
                                fno_symbols = [symbol.strip() for symbol in fno_symbols if symbol.strip()]

                                self.logger.info(f"✅ Downloaded {len(fno_symbols)} FNO enabled symbols from {url}")

                                # Log first few symbols for verification
                                if fno_symbols:
                                    self.logger.info(f"📊 Sample FNO symbols: {fno_symbols[:10]}")

                                return fno_symbols
                        except:
                            # If CSV parsing fails, try line-by-line parsing
                            lines = response.text.strip().split('\n')
                            fno_symbols = []

                            # Skip header lines and process individual securities
                            start_processing = False
                            for line in lines:
                                if 'Derivatives on Individual Securities' in line:
                                    start_processing = True
                                    continue
                                if start_processing and line.strip() and ',' in line:
                                    parts = line.split(',')
                                    if len(parts) >= 2:
                                        symbol = parts[1].strip()
                                        # Skip if it's a header or empty
                                        if symbol and symbol != 'Symbol' and symbol != 'SYMBOL':
                                            fno_symbols.append(symbol)

                            if fno_symbols:
                                self.logger.info(f"✅ Downloaded {len(fno_symbols)} FNO enabled symbols from {url}")
                                return fno_symbols
                    else:
                        self.logger.warning(f"⚠️ Failed to download from {url}: {response.status_code}")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error with URL {url}: {e}")
                    continue

            # If all URLs fail, try to extract from SmartAPI master data
            self.logger.info("🔄 Trying to extract FNO list from SmartAPI master data...")
            return self._extract_fno_from_smartapi()

        except Exception as e:
            self.logger.warning(f"⚠️ Error downloading FNO list: {e}")
            return None

    def _extract_fno_from_smartapi(self) -> Optional[List[str]]:
        """Extract FNO enabled stocks from SmartAPI master data"""
        try:
            master_df = self.download_smartapi_master_data()
            if master_df is None:
                return None

            # Filter for equity futures (instrumenttype = 'FUTSTK')
            fno_df = master_df[
                (master_df['instrumenttype'] == 'FUTSTK') &
                (master_df['exch_seg'] == 'NSE')
            ].copy()

            if not fno_df.empty:
                # Extract unique symbols - try both 'name' and 'symbol' columns
                fno_symbols = set()

                # Try 'name' column first
                if 'name' in fno_df.columns:
                    name_symbols = fno_df['name'].dropna().unique().tolist()
                    fno_symbols.update([symbol.strip() for symbol in name_symbols if symbol.strip()])

                # Also try 'symbol' column and clean it
                if 'symbol' in fno_df.columns:
                    symbol_symbols = fno_df['symbol'].dropna().unique().tolist()
                    # Clean symbols - remove -EQ suffix if present
                    cleaned_symbols = []
                    for symbol in symbol_symbols:
                        if symbol and isinstance(symbol, str):
                            clean_symbol = symbol.strip()
                            if clean_symbol.endswith('-EQ'):
                                clean_symbol = clean_symbol[:-3]
                            if clean_symbol:
                                cleaned_symbols.append(clean_symbol)
                    fno_symbols.update(cleaned_symbols)

                fno_symbols = sorted(list(fno_symbols))

                if fno_symbols:
                    self.logger.info(f"✅ Extracted {len(fno_symbols)} FNO symbols from SmartAPI master data")
                    self.logger.info(f"📊 Sample FNO symbols: {fno_symbols[:10]}")
                    return fno_symbols

            self.logger.warning("⚠️ No FNO symbols found in SmartAPI master data")
            return None

        except Exception as e:
            self.logger.warning(f"⚠️ Error extracting FNO from SmartAPI: {e}")
            return None

    def get_stock_categories_data(self) -> Dict[str, List[str]]:
        """Get comprehensive stock categorization data"""
        try:
            # Download current lists from NSE
            current_nifty_50 = self.download_current_nifty_50_list()
            current_nifty_100 = self.download_current_nifty_100_list()
            current_nifty_200 = self.download_current_nifty_200_list()
            current_nifty_500 = self.download_current_nifty_500_list()
            current_fno_list = self.download_current_fno_list()

            # Use fallback lists if downloads fail
            if not current_nifty_500:
                self.logger.error("❌ Failed to get Nifty 500 list")
                return {}

            # Use downloaded lists or fallback to empty if failed
            stock_categories = {}

            if current_nifty_50:
                stock_categories['NIFTY50'] = current_nifty_50
            else:
                self.logger.warning("⚠️ Using fallback Nifty 50 list")
                stock_categories['NIFTY50'] = self._get_fallback_nifty_50_list()

            if current_nifty_100:
                stock_categories['NIFTY100'] = current_nifty_100
            else:
                self.logger.warning("⚠️ Using fallback Nifty 100 list")
                stock_categories['NIFTY100'] = self._get_fallback_nifty_100_list()

            if current_nifty_200:
                stock_categories['NIFTY200'] = current_nifty_200
            else:
                self.logger.warning("⚠️ Using fallback Nifty 200 list")
                stock_categories['NIFTY200'] = self._get_fallback_nifty_200_list()

            # NIFTY500 uses the current list downloaded from NSE
            stock_categories['NIFTY500'] = current_nifty_500

            if current_fno_list:
                stock_categories['NIFTYFNO'] = current_fno_list
            else:
                self.logger.warning("⚠️ Using fallback FNO list")
                stock_categories['NIFTYFNO'] = self._get_fallback_fno_list()



            return stock_categories

        except Exception as e:
            self.logger.error(f"❌ Error getting stock categories data: {e}")
            return {}

    def process_stock_categories(self) -> int:
        """Process and store stock categorization data"""
        if not self.mongodb_connected:
            self.logger.error("❌ MongoDB not connected")
            return 0

        try:
            self.logger.info("🔄 Processing stock categories...")

            stock_categories = self.get_stock_categories_data()
            if not stock_categories:
                self.logger.error("❌ Failed to get stock categories data")
                return 0

            processed_count = 0

            for category, symbols in stock_categories.items():
                for symbol in symbols:
                    try:
                        # Create category document
                        category_doc = {
                            'symbol': symbol,
                            'category': category,
                            'index_name': category,
                            'is_fno_enabled': category == 'NIFTYFNO',
                            'market_cap_category': self._get_market_cap_category(category),
                            'last_updated': datetime.now(),
                            'data_source': 'nse_indices'
                        }

                        # Upsert to MongoDB (allows multiple categories per symbol)
                        result = self.stock_categories_collection.update_one(
                            {'symbol': symbol, 'category': category},
                            {'$set': category_doc},
                            upsert=True
                        )

                        processed_count += 1

                        if processed_count % 100 == 0:
                            self.logger.info(f"📊 Processed {processed_count} stock categorizations...")

                    except pymongo.errors.DuplicateKeyError:
                        # Document already exists, skip silently
                        processed_count += 1
                        continue
                    except Exception as e:
                        self.logger.warning(f"⚠️ Error processing {symbol} for {category}: {e}")
                        continue

            self.logger.info(f"✅ Processed {processed_count} stock categorizations")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ Error processing stock categories: {e}")
            return 0

    def _get_market_cap_category(self, index_name: str) -> str:
        """Get market cap category based on index"""
        if index_name == 'NIFTY50':
            return 'LARGE_CAP'
        elif index_name in ['NIFTY100', 'NIFTY200']:
            return 'LARGE_MID_CAP'
        elif index_name == 'NIFTY500':
            return 'ALL_CAP'
        elif index_name == 'NIFTYFNO':
            return 'FNO_ENABLED'
        else:
            return 'UNKNOWN'

    def process_equity_mappings(self, master_df: pd.DataFrame, all_category_symbols: List[str]) -> int:
        """Process and store equity symbol mappings for all category symbols"""
        if not self.mongodb_connected:
            self.logger.error("❌ MongoDB not connected")
            return 0

        try:
            self.logger.info("🔄 Processing equity symbol mappings...")

            # Filter for equity instruments (empty instrumenttype indicates equity)
            equity_df = master_df[
                (master_df['instrumenttype'] == '') &
                (master_df['exch_seg'] == 'NSE')
            ].copy()

            self.logger.info(f"📊 Found {len(equity_df)} equity instruments from SmartAPI")
            self.logger.info(f"📊 Processing equity mappings for {len(all_category_symbols)} symbols from all categories")

            processed_count = 0
            not_found_count = 0

            for symbol in all_category_symbols:
                try:
                    # Try multiple matching strategies
                    matches = None

                    # Strategy 1: Exact symbol match
                    matches = equity_df[equity_df['symbol'] == symbol]

                    # Strategy 2: Try with -EQ suffix (common in SmartAPI)
                    if matches.empty:
                        matches = equity_df[equity_df['symbol'] == f"{symbol}-EQ"]

                    # Strategy 3: Try tradingsymbol match (if column exists)
                    if matches.empty and 'tradingsymbol' in equity_df.columns:
                        matches = equity_df[equity_df['tradingsymbol'] == symbol]

                    # Strategy 4: Try name-based matching (partial)
                    if matches.empty:
                        matches = equity_df[equity_df['name'].str.contains(symbol, case=False, na=False)]

                    # Strategy 5: Try symbol without special characters
                    if matches.empty and '&' in symbol:
                        clean_symbol = symbol.replace('&', '')
                        matches = equity_df[equity_df['symbol'].str.contains(clean_symbol, case=False, na=False)]

                    if matches.empty:
                        self.logger.debug(f"⚠️ No SmartAPI match found for {symbol}")
                        not_found_count += 1
                        continue

                    # Use the first match
                    instrument = matches.iloc[0]

                    # Create mapping document
                    mapping_doc = {
                        'symbol': symbol,
                        'smartapi_token': str(instrument.get('token', '')),
                        'smartapi_symbol': instrument.get('symbol', symbol),
                        'smartapi_tradingsymbol': instrument.get('tradingsymbol', instrument.get('symbol', symbol)),
                        'company_name': instrument.get('name', ''),
                        'exchange': 'NSE',
                        'yahoo_symbol': f"{symbol}.NS",
                        'instrument_type': 'EQ',
                        'lot_size': int(instrument.get('lotsize', 1)),
                        'tick_size': float(instrument.get('tick_size', 0.05)),
                        'last_updated': datetime.now(),
                        'data_source': 'smartapi_master'
                    }

                    # Upsert to MongoDB
                    self.equity_collection.update_one(
                        {'symbol': symbol},
                        {'$set': mapping_doc},
                        upsert=True
                    )

                    processed_count += 1

                    if processed_count % 50 == 0:
                        self.logger.info(f"📊 Processed {processed_count} equity mappings...")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error processing {symbol}: {e}")
                    continue

            self.logger.info(f"✅ Processed {processed_count} equity symbol mappings")
            if not_found_count > 0:
                self.logger.warning(f"⚠️ Could not find SmartAPI matches for {not_found_count} symbols")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ Error processing equity mappings: {e}")
            return 0

    def process_banknifty_option_mappings(self, master_df: pd.DataFrame) -> int:
        """Process and store Bank Nifty option symbol mappings"""
        if not self.mongodb_connected:
            self.logger.error("❌ MongoDB not connected")
            return 0

        try:
            self.logger.info("🔄 Processing Bank Nifty option mappings...")

            # Filter for Bank Nifty options
            option_df = master_df[
                (master_df['name'] == 'BANKNIFTY') &
                (master_df['instrumenttype'] == 'OPTIDX') &
                (master_df['exch_seg'] == 'NFO')
            ].copy()

            # Only include options expiring within next 90 days
            current_date = datetime.now().date()
            cutoff_date = current_date + timedelta(days=90)

            option_df = option_df[
                (option_df['expiry'].dt.date >= current_date) &
                (option_df['expiry'].dt.date <= cutoff_date)
            ]

            self.logger.info(f"📊 Found {len(option_df)} Bank Nifty options within 90 days")

            processed_count = 0

            for _, instrument in option_df.iterrows():
                try:
                    symbol = instrument['symbol']
                    strike = float(instrument['strike'])
                    expiry = instrument['expiry']

                    # Determine option type (CE/PE)
                    option_type = 'CE' if 'CE' in symbol else 'PE'

                    # Create mapping document
                    mapping_doc = {
                        'symbol': symbol,
                        'smartapi_token': str(instrument['token']),
                        'smartapi_symbol': symbol,
                        'smartapi_tradingsymbol': instrument.get('tradingsymbol', ''),
                        'underlying': 'BANKNIFTY',
                        'strike': strike,
                        'option_type': option_type,
                        'expiry': expiry,
                        'expiry_string': expiry.strftime('%d%b%Y').upper(),
                        'exchange': 'NFO',
                        'instrument_type': 'OPTIDX',
                        'lot_size': int(instrument.get('lotsize', 15)),
                        'tick_size': float(instrument.get('tick_size', 0.05)),
                        'last_updated': datetime.now(),
                        'data_source': 'smartapi_master',
                        'days_to_expiry': (expiry.date() - current_date).days
                    }

                    # Upsert to MongoDB
                    self.option_collection.update_one(
                        {'symbol': symbol},
                        {'$set': mapping_doc},
                        upsert=True
                    )

                    processed_count += 1

                    if processed_count % 100 == 0:
                        self.logger.info(f"📊 Processed {processed_count} option mappings...")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error processing option {instrument.get('symbol', 'Unknown')}: {e}")
                    continue

            self.logger.info(f"✅ Processed {processed_count} Bank Nifty option mappings")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ Error processing option mappings: {e}")
            return 0

    def process_index_mappings(self, master_df: pd.DataFrame) -> int:
        """Process and store index symbol mappings"""
        if not self.mongodb_connected:
            self.logger.error("❌ MongoDB not connected")
            return 0

        try:
            self.logger.info("🔄 Processing index mappings...")

            # Filter for indices
            index_df = master_df[
                (master_df['instrumenttype'].isin(['AMXIDX', 'INDEX', ''])) &
                (master_df['name'].isin(['BANKNIFTY', 'NIFTY 50', 'NIFTY', 'SENSEX']))
            ].copy()

            processed_count = 0

            for _, instrument in index_df.iterrows():
                try:
                    symbol = instrument['symbol']
                    name = instrument['name']

                    # Create mapping document
                    mapping_doc = {
                        'symbol': symbol,
                        'smartapi_token': str(instrument['token']),
                        'smartapi_symbol': symbol,
                        'smartapi_tradingsymbol': instrument.get('tradingsymbol', ''),
                        'index_name': name,
                        'exchange': instrument.get('exch_seg', 'NSE'),
                        'instrument_type': instrument.get('instrumenttype', 'INDEX'),
                        'last_updated': datetime.now(),
                        'data_source': 'smartapi_master'
                    }

                    # Upsert to MongoDB
                    self.index_collection.update_one(
                        {'symbol': symbol},
                        {'$set': mapping_doc},
                        upsert=True
                    )

                    processed_count += 1
                    self.logger.info(f"✅ Processed index: {name} ({symbol}) - Token: {instrument['token']}")

                except Exception as e:
                    self.logger.warning(f"⚠️ Error processing index {instrument.get('symbol', 'Unknown')}: {e}")
                    continue

            self.logger.info(f"✅ Processed {processed_count} index mappings")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ Error processing index mappings: {e}")
            return 0

    def get_equity_mapping(self, symbol: str) -> Optional[Dict]:
        """Get equity symbol mapping from MongoDB"""
        if not self.mongodb_connected:
            return None

        try:
            result = self.equity_collection.find_one({'symbol': symbol})
            return result
        except Exception as e:
            self.logger.error(f"❌ Error getting equity mapping for {symbol}: {e}")
            return None

    def get_option_mapping(self, symbol: str) -> Optional[Dict]:
        """Get option symbol mapping from MongoDB"""
        if not self.mongodb_connected:
            return None

        try:
            result = self.option_collection.find_one({'symbol': symbol})
            return result
        except Exception as e:
            self.logger.error(f"❌ Error getting option mapping for {symbol}: {e}")
            return None

    def get_banknifty_options_by_strike_expiry(self, strike: float, option_type: str, expiry_string: str = None) -> Optional[Dict]:
        """Get Bank Nifty option by strike, type, and expiry"""
        if not self.mongodb_connected:
            return None

        try:
            query = {
                'underlying': 'BANKNIFTY',
                'strike': strike,
                'option_type': option_type
            }

            if expiry_string:
                query['expiry_string'] = expiry_string

            result = self.option_collection.find_one(query)
            return result
        except Exception as e:
            self.logger.error(f"❌ Error getting option mapping: {e}")
            return None

    def get_index_mapping(self, symbol: str) -> Optional[Dict]:
        """Get index symbol mapping from MongoDB"""
        if not self.mongodb_connected:
            return None

        try:
            result = self.index_collection.find_one({'symbol': symbol})
            return result
        except Exception as e:
            self.logger.error(f"❌ Error getting index mapping for {symbol}: {e}")
            return None

    def get_smartapi_token(self, symbol: str, instrument_type: str = 'EQ') -> Optional[Tuple[str, str]]:
        """Get SmartAPI token and exchange for a symbol"""
        try:
            if instrument_type == 'EQ':
                mapping = self.get_equity_mapping(symbol)
            elif instrument_type == 'INDEX':
                mapping = self.get_index_mapping(symbol)
            else:
                mapping = self.get_option_mapping(symbol)

            if mapping:
                return (mapping['smartapi_token'], mapping['exchange'])

            return None

        except Exception as e:
            self.logger.error(f"❌ Error getting token for {symbol}: {e}")
            return None

    def get_yahoo_symbol(self, symbol: str) -> Optional[str]:
        """Get Yahoo Finance symbol for equity"""
        try:
            mapping = self.get_equity_mapping(symbol)
            if mapping:
                return mapping.get('yahoo_symbol')
            return None

        except Exception as e:
            self.logger.error(f"❌ Error getting Yahoo symbol for {symbol}: {e}")
            return None

    def get_stocks_by_category(self, category: str) -> List[str]:
        """Get list of stocks by category (NIFTY50, NIFTY100, NIFTY200, NIFTY500, NIFTYFNO)"""
        if not self.mongodb_connected:
            return []

        try:
            results = self.stock_categories_collection.find(
                {'category': category},
                {'symbol': 1, '_id': 0}
            )
            symbols = [doc['symbol'] for doc in results]
            return sorted(symbols)

        except Exception as e:
            self.logger.error(f"❌ Error getting stocks for category {category}: {e}")
            return []

    def get_fno_enabled_stocks(self) -> List[str]:
        """Get list of F&O enabled stocks"""
        return self.get_stocks_by_category('NIFTYFNO')

    def get_stock_categories_for_symbol(self, symbol: str) -> List[str]:
        """Get all categories a stock belongs to"""
        if not self.mongodb_connected:
            return []

        try:
            results = self.stock_categories_collection.find(
                {'symbol': symbol},
                {'category': 1, '_id': 0}
            )
            categories = [doc['category'] for doc in results]
            return sorted(categories)

        except Exception as e:
            self.logger.error(f"❌ Error getting categories for {symbol}: {e}")
            return []

    def is_stock_in_category(self, symbol: str, category: str) -> bool:
        """Check if a stock belongs to a specific category"""
        if not self.mongodb_connected:
            return False

        try:
            result = self.stock_categories_collection.find_one({
                'symbol': symbol,
                'category': category
            })
            return result is not None

        except Exception as e:
            self.logger.error(f"❌ Error checking {symbol} in {category}: {e}")
            return False

    def get_category_stats(self) -> Dict:
        """Get statistics about stock categories"""
        if not self.mongodb_connected:
            return {}

        try:
            pipeline = [
                {'$group': {'_id': '$category', 'count': {'$sum': 1}}},
                {'$sort': {'_id': 1}}
            ]
            category_stats = list(self.stock_categories_collection.aggregate(pipeline))

            stats = {
                'category_breakdown': category_stats,
                'total_categorizations': sum(stat['count'] for stat in category_stats),
                'last_updated': datetime.now()
            }

            return stats

        except Exception as e:
            self.logger.error(f"❌ Error getting category stats: {e}")
            return {}

    def download_and_update_all_mappings(self) -> bool:
        """Download and update all symbol mappings"""
        try:
            self.logger.info("🚀 Starting comprehensive symbol mapping download...")

            # Step 1: Download SmartAPI master data
            master_df = self.download_smartapi_master_data()
            if master_df is None:
                self.logger.error("❌ Failed to download SmartAPI master data")
                return False

            # Step 2: Get stock categories data ONCE and use it for both storage and equity processing
            self.logger.info("📡 Downloading stock categories data...")
            stock_categories = self.get_stock_categories_data()

            if not stock_categories:
                self.logger.error("❌ Failed to get stock categories data")
                return False

            # Log what we downloaded
            for category, symbols in stock_categories.items():
                self.logger.info(f"📊 Downloaded {category}: {len(symbols)} symbols")

            # Step 3: Process and store stock categories using the same data
            categories_count = self.process_stock_categories_with_data(stock_categories)

            # Step 4: Get all symbols from all categories for equity processing
            all_category_symbols = set()
            for category, symbols in stock_categories.items():
                all_category_symbols.update(symbols)
            all_category_symbols = list(all_category_symbols)

            if not all_category_symbols:
                self.logger.error("❌ No symbols found in stock categories")
                return False

            self.logger.info(f"📊 Found {len(all_category_symbols)} unique symbols across all categories")

            # Step 5: Process equity mappings for all category symbols
            equity_count = self.process_equity_mappings(master_df, all_category_symbols)

            # Step 6: Process Bank Nifty option mappings
            option_count = self.process_banknifty_option_mappings(master_df)

            # Step 7: Process index mappings
            index_count = self.process_index_mappings(master_df)

            # Summary
            total_processed = equity_count + option_count + index_count + categories_count
            self.logger.info(f"🎉 Symbol mapping download completed!")
            self.logger.info(f"📊 Summary:")
            self.logger.info(f"   - Stock categories: {categories_count}")
            self.logger.info(f"   - Equity mappings: {equity_count}")
            self.logger.info(f"   - Option mappings: {option_count}")
            self.logger.info(f"   - Index mappings: {index_count}")
            self.logger.info(f"   - Total processed: {total_processed}")

            return total_processed > 0

        except Exception as e:
            self.logger.error(f"❌ Error in download_and_update_all_mappings: {e}")
            return False

    def process_stock_categories_with_data(self, stock_categories: Dict[str, List[str]]) -> int:
        """Process and store stock categorization data using provided data"""
        if not self.mongodb_connected:
            self.logger.error("❌ MongoDB not connected")
            return 0

        try:
            self.logger.info("🔄 Processing stock categories...")

            if not stock_categories:
                self.logger.error("❌ No stock categories data provided")
                return 0

            processed_count = 0

            for category, symbols in stock_categories.items():
                self.logger.info(f"🔄 Processing {category} with {len(symbols)} symbols...")

                for symbol in symbols:
                    try:
                        # Create category document
                        category_doc = {
                            'symbol': symbol,
                            'category': category,
                            'index_name': category,
                            'is_fno_enabled': category == 'NIFTYFNO',
                            'market_cap_category': self._get_market_cap_category(category),
                            'last_updated': datetime.now(),
                            'data_source': 'nse_indices'
                        }

                        # Upsert to MongoDB (allows multiple categories per symbol)
                        result = self.stock_categories_collection.update_one(
                            {'symbol': symbol, 'category': category},
                            {'$set': category_doc},
                            upsert=True
                        )

                        processed_count += 1

                        if processed_count % 100 == 0:
                            self.logger.info(f"📊 Processed {processed_count} stock categorizations...")

                    except pymongo.errors.DuplicateKeyError:
                        # Document already exists, skip silently
                        processed_count += 1
                        continue
                    except Exception as e:
                        self.logger.warning(f"⚠️ Error processing {symbol} for {category}: {e}")
                        continue

            self.logger.info(f"✅ Processed {processed_count} stock categorizations")
            return processed_count

        except Exception as e:
            self.logger.error(f"❌ Error processing stock categories: {e}")
            return 0

    def get_mapping_stats(self) -> Dict:
        """Get statistics about stored mappings"""
        if not self.mongodb_connected:
            return {}

        try:
            stats = {
                'equity_count': self.equity_collection.count_documents({}),
                'option_count': self.option_collection.count_documents({}),
                'index_count': self.index_collection.count_documents({}),
                'categories_count': self.stock_categories_collection.count_documents({}),
                'last_updated': datetime.now()
            }

            # Get option expiry breakdown
            pipeline = [
                {'$group': {'_id': '$expiry_string', 'count': {'$sum': 1}}},
                {'$sort': {'_id': 1}}
            ]
            expiry_stats = list(self.option_collection.aggregate(pipeline))
            stats['option_expiry_breakdown'] = expiry_stats

            # Get category breakdown
            category_stats = self.get_category_stats()
            stats['category_breakdown'] = category_stats.get('category_breakdown', [])

            return stats

        except Exception as e:
            self.logger.error(f"❌ Error getting mapping stats: {e}")
            return {}

    def close(self):
        """Close MongoDB connection"""
        if self.mongodb_client:
            self.mongodb_client.close()
            self.logger.info("✅ MongoDB connection closed")
