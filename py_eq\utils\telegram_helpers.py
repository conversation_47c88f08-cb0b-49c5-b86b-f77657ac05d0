"""
Telegram Bot Helper Functions

This module contains utility functions and formatting helpers
for the Telegram trading bot.
"""

import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from telebot import types

from ..config.telegram_config import telegram_config, EMOJIS, MESSAGE_TEMPLATES
from ..services.report_generator import TradeRecord, TradingMetrics


class TelegramHelpers:
    """Helper methods for Telegram bot functionality"""
    
    @staticmethod
    def format_weekly_summary(metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        """Format weekly summary message"""
        week_str = f"{start_date.strftime('%B %d')} - {end_date.strftime('%B %d, %Y')}"
        summary_emoji = EMOJIS["rocket"] if metrics.total_pnl >= 0 else EMOJIS["trend_down"]
        
        message = f"""
{summary_emoji} *Weekly Trading Summary*
📅 {week_str}

💰 *Weekly P&L:* ₹{metrics.total_pnl:,.2f}
📊 *Total Trades:* {metrics.total_trades}
🎯 *Win Rate:* {metrics.win_rate:.1f}%
⚡ *Profit Factor:* {metrics.profit_factor:.2f}

📈 *Performance Metrics:*
• Gross Profit: ₹{metrics.gross_profit:,.2f}
• Gross Loss: ₹{metrics.gross_loss:,.2f}
• Average Win: ₹{metrics.average_win:,.2f}
• Average Loss: ₹{metrics.average_loss:,.2f}
• Max Drawdown: ₹{metrics.max_drawdown:,.2f}

⏱️ *Trading Behavior:*
• Avg Trade Duration: {metrics.average_trade_duration:.0f} min
• Total Volume: ₹{metrics.total_volume:,.2f}
"""
        
        if metrics.strategy_performance:
            message += "\n🎯 *Strategy Performance:*"
            for strategy, stats in metrics.strategy_performance.items():
                pnl_emoji = "💚" if stats['pnl'] >= 0 else "❤️"
                message += f"\n• {strategy}: {pnl_emoji} ₹{stats['pnl']:,.2f} ({stats['win_rate']:.1f}%)"
        
        return message
    
    @staticmethod
    def format_monthly_summary(metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        """Format monthly summary message"""
        month_str = start_date.strftime('%B %Y')
        summary_emoji = EMOJIS["fire"] if metrics.total_pnl >= 0 else EMOJIS["trend_down"]
        
        # Calculate monthly return percentage
        opening_balance = 320000.0
        monthly_return = (metrics.total_pnl / opening_balance) * 100 if opening_balance > 0 else 0
        
        message = f"""
{summary_emoji} *Monthly Trading Summary*
📅 {month_str}

💰 *Monthly P&L:* ₹{metrics.total_pnl:,.2f}
📊 *Monthly Return:* {monthly_return:.2f}%
🎯 *Total Trades:* {metrics.total_trades}
⚡ *Win Rate:* {metrics.win_rate:.1f}%

📈 *Advanced Metrics:*
• Profit Factor: {metrics.profit_factor:.2f}
• Expectancy: ₹{metrics.expectancy:,.2f}
• Sharpe Ratio: {TelegramHelpers._calculate_simple_sharpe(metrics):.2f}
• Max Drawdown: ₹{metrics.max_drawdown:,.2f}

🎯 *Risk Analysis:*
• Largest Win: ₹{metrics.largest_win:,.2f}
• Largest Loss: ₹{metrics.largest_loss:,.2f}
• Risk-Reward Ratio: {(metrics.average_win/metrics.average_loss):.2f if metrics.average_loss > 0 else 0:.2f}

📊 *Trading Volume:*
• Total Volume: ₹{metrics.total_volume:,.2f}
• Avg Trade Size: ₹{(metrics.total_volume/metrics.total_trades):,.2f if metrics.total_trades > 0 else 0}
"""
        
        return message
    
    @staticmethod
    def format_trades_message(trades: List[TradeRecord], limit: int = 10) -> str:
        """Format recent trades message"""
        if not trades:
            return MESSAGE_TEMPLATES["no_data"]
        
        # Sort trades by timestamp (most recent first)
        sorted_trades = sorted(trades, key=lambda t: t.timestamp, reverse=True)
        recent_trades = sorted_trades[:limit]
        
        message = f"""
📋 *Recent Trades* (Last {len(recent_trades)})

"""
        
        for i, trade in enumerate(recent_trades, 1):
            status_emoji = "✅" if trade.is_winner else "❌"
            pnl_emoji = "💚" if trade.pnl >= 0 else "❤️"
            
            # Format timestamp
            try:
                trade_time = datetime.strptime(trade.timestamp, '%Y-%m-%d %H:%M:%S')
                time_str = trade_time.strftime('%m/%d %H:%M')
            except:
                time_str = trade.timestamp
            
            message += f"""
{i}. {status_emoji} *{trade.symbol}* - {trade.strategy}
   📅 {time_str} | 💰 ₹{trade.price:.2f}
   {pnl_emoji} P&L: ₹{trade.pnl:,.2f} | ⏱️ {trade.duration_minutes}m
"""
        
        if len(trades) > limit:
            message += f"\n... and {len(trades) - limit} more trades"
        
        return message
    
    @staticmethod
    def format_balance_message(opening_balance: float, current_balance: float, metrics: TradingMetrics) -> str:
        """Format balance information message"""
        balance_change = current_balance - opening_balance
        balance_change_pct = (balance_change / opening_balance) * 100 if opening_balance > 0 else 0
        
        balance_emoji = EMOJIS["profit"] if balance_change >= 0 else EMOJIS["loss"]
        trend_emoji = EMOJIS["trend_up"] if balance_change >= 0 else EMOJIS["trend_down"]
        
        # Calculate available margin (assuming 3.5x leverage)
        available_margin = current_balance * 3.5
        
        message = f"""
{balance_emoji} *Account Balance Summary*

💰 *Current Balance:* ₹{current_balance:,.2f}
📊 *Opening Balance:* ₹{opening_balance:,.2f}
{trend_emoji} *Change:* ₹{balance_change:,.2f} ({balance_change_pct:+.2f}%)

⚡ *Trading Power:*
• Available Margin: ₹{available_margin:,.2f}
• Used Margin: ₹{metrics.total_volume:,.2f}
• Free Margin: ₹{available_margin - metrics.total_volume:,.2f}

🎯 *Risk Metrics:*
• Daily Risk: {abs(balance_change/opening_balance*100):.2f}% of capital
• Max Drawdown: ₹{metrics.max_drawdown:,.2f}
• Risk Level: {'🟢 Low' if abs(balance_change_pct) < 1 else '🟡 Medium' if abs(balance_change_pct) < 3 else '🔴 High'}
"""
        
        return message
    
    @staticmethod
    def format_strategy_message(metrics: TradingMetrics) -> str:
        """Format strategy performance message"""
        if not metrics.strategy_performance:
            return MESSAGE_TEMPLATES["no_data"]
        
        message = f"""
🎯 *Strategy Performance Analysis*

📊 *Overall Stats:*
• Total Trades: {metrics.total_trades}
• Win Rate: {metrics.win_rate:.1f}%
• Total P&L: ₹{metrics.total_pnl:,.2f}

🏆 *Strategy Breakdown:*
"""
        
        # Sort strategies by P&L
        sorted_strategies = sorted(
            metrics.strategy_performance.items(),
            key=lambda x: x[1]['pnl'],
            reverse=True
        )
        
        for i, (strategy, stats) in enumerate(sorted_strategies, 1):
            pnl_emoji = "💚" if stats['pnl'] >= 0 else "❤️"
            rank_emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            
            message += f"""
{rank_emoji} *{strategy}*
   {pnl_emoji} P&L: ₹{stats['pnl']:,.2f}
   📊 Trades: {stats['trades']} | Win Rate: {stats['win_rate']:.1f}%
   💰 Volume: ₹{stats['volume']:,.2f}
"""
        
        # Add best performing strategy highlight
        if sorted_strategies:
            best_strategy = sorted_strategies[0]
            message += f"\n🔥 *Top Performer:* {best_strategy[0]} with ₹{best_strategy[1]['pnl']:,.2f}"
        
        return message
    
    @staticmethod
    def create_trades_keyboard(total_trades: int) -> types.InlineKeyboardMarkup:
        """Create keyboard for trades pagination"""
        keyboard = types.InlineKeyboardMarkup(row_width=3)
        
        buttons = [
            types.InlineKeyboardButton("📊 Today", callback_data="trades_today"),
            types.InlineKeyboardButton("📅 Week", callback_data="trades_week"),
            types.InlineKeyboardButton("📆 Month", callback_data="trades_month"),
        ]
        
        keyboard.row(*buttons)
        
        if total_trades > 10:
            nav_buttons = [
                types.InlineKeyboardButton("⬅️ Prev", callback_data="trades_prev"),
                types.InlineKeyboardButton("➡️ Next", callback_data="trades_next"),
            ]
            keyboard.row(*nav_buttons)
        
        return keyboard
    
    @staticmethod
    def create_alerts_keyboard() -> types.InlineKeyboardMarkup:
        """Create keyboard for alert settings"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        buttons = [
            types.InlineKeyboardButton("🔔 Toggle Alerts", callback_data="alert_toggle"),
            types.InlineKeyboardButton("💰 Loss Threshold", callback_data="alert_loss"),
            types.InlineKeyboardButton("📉 Drawdown Alert", callback_data="alert_drawdown"),
            types.InlineKeyboardButton("⚖️ Balance Alert", callback_data="alert_balance"),
            types.InlineKeyboardButton("📊 Test Alert", callback_data="alert_test"),
            types.InlineKeyboardButton("🔙 Back", callback_data="main_menu"),
        ]
        
        for i in range(0, len(buttons), 2):
            if i + 1 < len(buttons):
                keyboard.row(buttons[i], buttons[i + 1])
            else:
                keyboard.row(buttons[i])
        
        return keyboard
    
    @staticmethod
    def create_settings_keyboard() -> types.InlineKeyboardMarkup:
        """Create keyboard for bot settings"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        buttons = [
            types.InlineKeyboardButton("📈 Daily Reports", callback_data="setting_daily"),
            types.InlineKeyboardButton("📅 Weekly Reports", callback_data="setting_weekly"),
            types.InlineKeyboardButton("📆 Monthly Reports", callback_data="setting_monthly"),
            types.InlineKeyboardButton("🔔 Trade Alerts", callback_data="setting_alerts"),
            types.InlineKeyboardButton("⏰ Report Times", callback_data="setting_times"),
            types.InlineKeyboardButton("🔙 Back", callback_data="main_menu"),
        ]
        
        for i in range(0, len(buttons), 2):
            if i + 1 < len(buttons):
                keyboard.row(buttons[i], buttons[i + 1])
            else:
                keyboard.row(buttons[i])
        
        return keyboard
    
    @staticmethod
    def _calculate_simple_sharpe(metrics: TradingMetrics) -> float:
        """Calculate a simplified Sharpe ratio"""
        if metrics.total_trades == 0 or metrics.expectancy == 0:
            return 0.0
        
        # Simplified calculation based on expectancy and volatility
        avg_return = metrics.expectancy
        volatility = abs(metrics.largest_win - metrics.largest_loss) / 2 if metrics.largest_win > 0 and metrics.largest_loss > 0 else 1
        
        return avg_return / volatility if volatility > 0 else 0.0
