"""
Opening Range Breakout (ORB) Strategy for Live Trading

This strategy identifies the opening range (first 15 minutes) and trades breakouts
above or below this range with volume confirmation.

Features:
- 15-minute opening range identification
- Volume confirmation for entries
- Risk management with ₹800 per trade
- Stop loss and target management
- Real-time signal detection
"""

import logging
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple
import numpy as np
from dataclasses import dataclass

@dataclass
class ORBSignal:
    """ORB trading signal"""
    symbol: str
    signal_type: str  # 'LONG' or 'SHORT'
    entry_price: float
    stop_loss: float
    target: float
    risk_amount: float
    timestamp: datetime
    opening_range_high: float
    opening_range_low: float
    volume_confirmation: bool
    confidence: float

class ORBStrategy:
    """Opening Range Breakout Strategy"""

    def __init__(self, logger=None, market_data_service=None, order_service=None, config=None, risk_per_trade: float = 800.0):
        self.risk_per_trade = risk_per_trade
        self.logger = logger or logging.getLogger(f"{__class__.__name__}")
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config
        
        # Strategy parameters
        self.opening_range_minutes = 15  # First 15 minutes
        self.volume_multiplier = 1.1  # Volume should be 1.1x average (reduced from 1.5x for more signals)
        self.risk_reward_ratio = 3.0  # 1:3 risk reward
        self.buffer_percentage = 0.1  # 10% buffer for stop loss
        
        # Market hours
        self.market_start = time(9, 15)
        self.market_end = time(15, 30)
        self.opening_range_end = time(9, 30)  # 15 minutes after market open
        
        # Daily tracking
        self.daily_ranges: Dict[str, Dict] = {}  # symbol -> {high, low, volume}
        self.signals_generated: Dict[str, bool] = {}  # symbol -> signal_generated_today
        
    def reset_daily_data(self):
        """Reset daily tracking data"""
        self.daily_ranges.clear()
        self.signals_generated.clear()
        self.logger.info("Reset daily ORB data")
        
    def is_market_hours(self, timestamp: datetime) -> bool:
        """Check if timestamp is within market hours"""
        current_time = timestamp.time()
        return self.market_start <= current_time <= self.market_end
        
    def is_opening_range_period(self, timestamp: datetime) -> bool:
        """Check if timestamp is within opening range period"""
        current_time = timestamp.time()
        return self.market_start <= current_time <= self.opening_range_end
        
    def update_opening_range(self, symbol: str, candle: Dict):
        """Update opening range for a symbol"""
        if symbol not in self.daily_ranges:
            self.daily_ranges[symbol] = {
                'high': candle['high'],
                'low': candle['low'],
                'volume': candle['volume'],
                'candle_count': 1
            }
        else:
            range_data = self.daily_ranges[symbol]
            range_data['high'] = max(range_data['high'], candle['high'])
            range_data['low'] = min(range_data['low'], candle['low'])
            range_data['volume'] += candle['volume']
            range_data['candle_count'] += 1
            
    def get_opening_range(self, symbol: str) -> Optional[Tuple[float, float, float]]:
        """Get opening range high, low, and average volume"""
        if symbol not in self.daily_ranges:
            return None
            
        range_data = self.daily_ranges[symbol]
        avg_volume = range_data['volume'] / range_data['candle_count'] if range_data['candle_count'] > 0 else 0
        
        return range_data['high'], range_data['low'], avg_volume
        
    def _initialize_opening_range_from_history(self, symbol: str, candles: List):
        """Initialize opening range from historical candles if not already done"""
        try:
            # Skip if opening range already exists for this symbol
            if symbol in self.daily_ranges:
                return
                
            # Get today's date for filtering
            from datetime import datetime, date
            today = date.today()
            
            # Find candles from today's opening range period (9:15 to 9:30)
            opening_range_candles = []
            
            for candle in candles:
                # Convert candle to dict format if needed
                if hasattr(candle, '__dict__'):
                    candle_dict = {
                        'timestamp': getattr(candle, 'timestamp', None),
                        'open': getattr(candle, 'open', 0),
                        'high': getattr(candle, 'high', 0),
                        'low': getattr(candle, 'low', 0),
                        'close': getattr(candle, 'close', 0),
                        'volume': getattr(candle, 'volume', 0)
                    }
                elif isinstance(candle, dict):
                    candle_dict = candle
                else:
                    continue
                    
                # Parse timestamp
                timestamp = candle_dict.get('timestamp')
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp)
                elif not isinstance(timestamp, datetime):
                    continue
                    
                # Check if candle is from today and within opening range period
                if (timestamp.date() == today and 
                    self.is_opening_range_period(timestamp)):
                    opening_range_candles.append(candle_dict)
            
            # Initialize opening range if we have candles from the opening period
            if opening_range_candles:
                self.logger.debug(f"Initializing opening range for {symbol} from {len(opening_range_candles)} historical candles")
                
                for candle in opening_range_candles:
                    self.update_opening_range(symbol, candle)
                    
                range_data = self.get_opening_range(symbol)
                if range_data:
                    high, low, avg_vol = range_data
                    self.logger.info(f"ORB opening range initialized for {symbol}: H={high:.2f}, L={low:.2f}, AvgVol={avg_vol:.0f}")
            else:
                self.logger.debug(f"No opening range candles found for {symbol} in historical data")
                
        except Exception as e:
            self.logger.error(f"Error initializing opening range for {symbol}: {e}")
            import traceback
            self.logger.debug(f"Opening range initialization traceback: {traceback.format_exc()}")
        
    def check_volume_confirmation(self, current_volume: float, avg_volume: float) -> bool:
        """Check if current volume confirms the breakout"""
        if avg_volume <= 0:
            return False
        return current_volume >= (avg_volume * self.volume_multiplier)
        
    def calculate_position_size(self, entry_price: float, stop_loss: float) -> int:
        """Calculate position size based on risk per trade with margin limits"""
        if entry_price <= 0 or stop_loss <= 0:
            return 0
            
        risk_per_share = abs(entry_price - stop_loss)
        if risk_per_share <= 0:
            return 0
            
        # Calculate position size based on risk
        position_size = int(self.risk_per_trade / risk_per_share)
        position_size = max(1, position_size)  # Minimum 1 share
        
        # CRITICAL: Apply maximum position size based on 3.5x margin limit
        # Assume available cash of ₹4,762 (from logs) for safety
        max_order_value = 4762 * 3.5  # ₹16,667
        max_position_size = int(max_order_value / entry_price)
        
        if position_size > max_position_size:
            self.logger.warning(f"⚠️ ORB position size reduced due to margin limit")
            self.logger.warning(f"   Risk-based: {position_size} shares (₹{position_size * entry_price:,.2f})")
            self.logger.warning(f"   Margin-limited: {max_position_size} shares (₹{max_position_size * entry_price:,.2f})")
            position_size = max_position_size
        
        return max(1, position_size)

    def analyze_candle(self, symbol: str, candle: Dict, historical_candles: List[Dict]) -> Optional[ORBSignal]:
        """
        Analyze current candle for ORB signals
        
        Args:
            symbol: Stock symbol
            candle: Current candle data
            historical_candles: Historical candles for context
            
        Returns:
            ORBSignal if signal detected, None otherwise
        """
        try:
            timestamp = candle.get('timestamp')
            if isinstance(timestamp, (int, float)):
                timestamp = datetime.fromtimestamp(timestamp)
            elif not isinstance(timestamp, datetime):
                return None
                
            # Skip if not market hours
            if not self.is_market_hours(timestamp):
                return None
                
            # Skip if signal already generated for this symbol today
            if self.signals_generated.get(symbol, False):
                return None
                
            # Update opening range during first 15 minutes
            if self.is_opening_range_period(timestamp):
                self.update_opening_range(symbol, candle)
                return None
                
            # Get opening range data
            range_data = self.get_opening_range(symbol)
            if not range_data:
                self.logger.debug(f"No opening range data available for {symbol}")
                return None
                
            opening_range_high, opening_range_low, avg_volume = range_data
            
            # Check for valid opening range
            if opening_range_high <= opening_range_low:
                self.logger.debug(f"Invalid opening range for {symbol}: High={opening_range_high}, Low={opening_range_low}")
                return None
                
            current_price = candle['close']
            current_volume = candle.get('volume', 0)
            
            self.logger.debug(f"ORB analysis for {symbol}: Range H={opening_range_high:.2f}, L={opening_range_low:.2f}, Current={current_price:.2f}, Volume={current_volume}/{avg_volume:.0f}")
            
            # Check volume confirmation
            volume_confirmation = self.check_volume_confirmation(current_volume, avg_volume)
            
            # Check for breakout signals
            signal = None
            
            # Long signal: Price breaks above opening range high
            if current_price > opening_range_high and volume_confirmation:
                entry_price = opening_range_high
                range_size = opening_range_high - opening_range_low
                stop_loss = opening_range_low - (range_size * self.buffer_percentage)
                risk = entry_price - stop_loss
                target = entry_price + (risk * self.risk_reward_ratio)
                
                # Calculate confidence based on breakout strength and volume
                breakout_strength = (current_price - opening_range_high) / opening_range_high
                volume_strength = current_volume / avg_volume if avg_volume > 0 else 1
                confidence = min(0.95, 0.5 + (breakout_strength * 10) + (volume_strength * 0.1))
                
                signal = ORBSignal(
                    symbol=symbol,
                    signal_type='LONG',
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    target=target,
                    risk_amount=risk,
                    timestamp=timestamp,
                    opening_range_high=opening_range_high,
                    opening_range_low=opening_range_low,
                    volume_confirmation=volume_confirmation,
                    confidence=confidence
                )
                
            # Short signal: Price breaks below opening range low
            elif current_price < opening_range_low and volume_confirmation:
                entry_price = opening_range_low
                range_size = opening_range_high - opening_range_low
                stop_loss = opening_range_high + (range_size * self.buffer_percentage)
                risk = stop_loss - entry_price
                target = entry_price - (risk * self.risk_reward_ratio)
                
                # Calculate confidence based on breakdown strength and volume
                breakdown_strength = (opening_range_low - current_price) / opening_range_low
                volume_strength = current_volume / avg_volume if avg_volume > 0 else 1
                confidence = min(0.95, 0.5 + (breakdown_strength * 10) + (volume_strength * 0.1))
                
                signal = ORBSignal(
                    symbol=symbol,
                    signal_type='SHORT',
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    target=target,
                    risk_amount=risk,
                    timestamp=timestamp,
                    opening_range_high=opening_range_high,
                    opening_range_low=opening_range_low,
                    volume_confirmation=volume_confirmation,
                    confidence=confidence
                )
                
            if signal:
                # Mark signal as generated for this symbol
                self.signals_generated[symbol] = True
                
                self.logger.info(f"ORB {signal.signal_type} signal for {symbol}: "
                               f"Entry: ₹{signal.entry_price:.2f}, SL: ₹{signal.stop_loss:.2f}, "
                               f"Target: ₹{signal.target:.2f}, Risk: ₹{signal.risk_amount:.2f}")
                
            return signal
            
        except Exception as e:
            self.logger.error(f"Error analyzing ORB signal for {symbol}: {e}")
            return None

    def process_signal(self, signal) -> Optional[Dict]:
        """
        Process signal for ORB strategy - unified method for strategy manager
        This method bridges the gap between the strategy manager and analyze_candle method
        """
        try:
            # Get historical candle data for the symbol
            if not self.market_data_service:
                self.logger.error("Market data service not available for ORB strategy")
                return None

            # Get recent candles (last 50 candles should be enough for ORB analysis)
            candles = self.market_data_service.get_historical_data(
                symbol=signal.symbol,
                timeframe='5min',
                days=5
            )

            if not candles or len(candles) < 10:
                self.logger.debug(f"Insufficient candle data for ORB analysis: {signal.symbol}")
                return None

            # Initialize opening range from historical data if not already done
            self._initialize_opening_range_from_history(signal.symbol, candles)

            # Convert to the format expected by analyze_candle
            candle_dicts = []
            for candle in candles:
                if hasattr(candle, '__dict__'):
                    # Convert candle object to dict
                    candle_dict = {
                        'timestamp': getattr(candle, 'timestamp', None),
                        'open': getattr(candle, 'open', 0),
                        'high': getattr(candle, 'high', 0),
                        'low': getattr(candle, 'low', 0),
                        'close': getattr(candle, 'close', 0),
                        'volume': getattr(candle, 'volume', 0)
                    }
                elif isinstance(candle, dict):
                    candle_dict = candle
                else:
                    continue
                candle_dicts.append(candle_dict)

            if len(candle_dicts) < 10:
                self.logger.debug(f"Insufficient valid candle data for ORB analysis: {signal.symbol}")
                return None

            # Use the latest candle as current candle and rest as history
            current_candle = candle_dicts[-1]
            historical_candles = candle_dicts[:-1]

            # Analyze using existing analyze_candle method
            orb_signal = self.analyze_candle(signal.symbol, current_candle, historical_candles)

            if orb_signal and orb_signal.signal_type in ['LONG', 'SHORT']:
                # Convert ORB signal to the format expected by strategy manager
                action = 'BUY' if orb_signal.signal_type == 'LONG' else 'SELL'

                return {
                    'action': action,
                    'entry_price': orb_signal.entry_price,
                    'stop_loss': orb_signal.stop_loss,
                    'target': orb_signal.target,
                    'quantity': self.calculate_position_size(orb_signal.entry_price, orb_signal.stop_loss),
                    'confidence': orb_signal.confidence,
                    'strategy': 'ORB',
                    'timeframe': '5min',
                    'volume_confirmation': orb_signal.volume_confirmation,
                    'opening_range_high': orb_signal.opening_range_high,
                    'opening_range_low': orb_signal.opening_range_low
                }

            return None

        except Exception as e:
            self.logger.error(f"Error in ORB process_signal for {signal.symbol}: {e}")
            import traceback
            self.logger.debug(f"ORB process_signal traceback: {traceback.format_exc()}")
            return None
            
    # Alias for backward compatibility
    analyze_signal = process_signal
            
    def get_strategy_info(self) -> Dict:
        """Get strategy information"""
        return {
            'name': 'Opening Range Breakout (ORB)',
            'description': 'Trades breakouts from 15-minute opening range',
            'risk_per_trade': self.risk_per_trade,
            'risk_reward_ratio': self.risk_reward_ratio,
            'opening_range_minutes': self.opening_range_minutes,
            'volume_multiplier': self.volume_multiplier,
            'signals_generated_today': len([s for s in self.signals_generated.values() if s])
        }
