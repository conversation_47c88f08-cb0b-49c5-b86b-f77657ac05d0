#!/usr/bin/env python3
"""
Production Strategy Manager - Real Trading with Real Balances
Replaces virtual account system with real SmartAPI order execution
"""

import logging
import time
from datetime import datetime, time as dt_time
from enum import Enum
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass

from models.signal import Signal, SignalType
from models.order import Order, TransactionType, OrderType, ProductType, OrderStatus
from services.market_data_service import MarketDataServiceInterface
from services.order_service import OrderServiceInterface
from config.config import TradingConfig
from strategies.ma_crossover_strategy import MACrossoverStrategy
from strategies.support_resistance_strategy import SupportResistanceStrategy
from strategies.gap_and_go_strategy import GapAndGoStrategy
from strategies.orb_strategy import ORBStrategy


class StrategyType(Enum):
    """Strategy type enumeration"""
    MA_CROSSOVER = "MA_CROSSOVER"
    SUPPORT_RESISTANCE = "SUPPORT_RESISTANCE"
    GAP_AND_GO = "GAP_AND_GO"
    ORB = "ORB"


@dataclass
class TradeTracker:
    """Track trades across all strategies"""
    total_trades_today: int = 0
    max_trades_per_day: int = 3
    trades_by_strategy: Dict[str, int] = None
    last_trade_time: Dict[str, float] = None  # Symbol -> timestamp of last trade
    symbol_cooldown_seconds: int = 1800  # 30 minutes cooldown between trades in same stock (increased from 5 min)
    traded_symbols: Set[str] = None  # Set of symbols traded today

    # CRITICAL SAFETY: Absolute maximum trades allowed per day
    ABSOLUTE_MAX_TRADES = 3

    # ORDER TIMING CONTROLS: Prevent multiple orders within seconds
    last_order_time: float = 0.0  # Timestamp of last order placement
    min_order_gap_seconds: int = 5  # Minimum 5 seconds between ANY orders
    order_placement_lock: bool = False  # Lock to prevent concurrent order placement
    
    def __post_init__(self):
        if self.trades_by_strategy is None:
            self.trades_by_strategy = {}
        if self.last_trade_time is None:
            self.last_trade_time = {}
        if self.traded_symbols is None:
            self.traded_symbols = set()
    
    def can_trade(self) -> bool:
        """Check if we can place more trades today"""
        # CRITICAL SAFETY: Double-check against absolute maximum
        if self.total_trades_today >= self.ABSOLUTE_MAX_TRADES:
            return False
        return self.total_trades_today < min(self.max_trades_per_day, self.ABSOLUTE_MAX_TRADES)
    
    def can_trade_symbol(self, symbol: str) -> bool:
        """Check if we can trade a specific symbol (cooldown period)"""
        import time
        current_time = time.time()

        # CRITICAL SAFETY: Never trade a symbol more than once per day
        if symbol in self.traded_symbols:
            return False

        # Check cooldown period
        if symbol in self.last_trade_time:
            time_since_last_trade = current_time - self.last_trade_time[symbol]
            if time_since_last_trade < self.symbol_cooldown_seconds:
                # Still in cooldown period
                return False

        return True

    def can_place_order_now(self) -> bool:
        """Check if we can place an order now (timing control)"""
        import time
        current_time = time.time()

        # Check if order placement is locked
        if self.order_placement_lock:
            return False

        # Check minimum gap between orders
        if self.last_order_time > 0:
            time_since_last_order = current_time - self.last_order_time
            if time_since_last_order < self.min_order_gap_seconds:
                return False

        return True

    def lock_order_placement(self):
        """Lock order placement to prevent concurrent orders"""
        self.order_placement_lock = True

    def unlock_order_placement(self):
        """Unlock order placement"""
        self.order_placement_lock = False

    def record_order_placement(self):
        """Record the timestamp of order placement"""
        import time
        self.last_order_time = time.time()
    
    def record_trade(self, strategy_name: str, symbol: str = None):
        """Record a new trade"""
        import time
        
        # CRITICAL SAFETY: Prevent exceeding absolute maximum
        if self.total_trades_today >= self.ABSOLUTE_MAX_TRADES:
            raise ValueError(f"CRITICAL SAFETY: Cannot exceed {self.ABSOLUTE_MAX_TRADES} trades per day")
        
        self.total_trades_today += 1
        self.trades_by_strategy[strategy_name] = self.trades_by_strategy.get(strategy_name, 0) + 1
        
        # Record timestamp for symbol cooldown and add to traded symbols
        if symbol:
            self.last_trade_time[symbol] = time.time()
            self.traded_symbols.add(symbol)
    
    def get_remaining_trades(self) -> int:
        """Get remaining trades for today"""
        # CRITICAL SAFETY: Use absolute maximum as upper limit
        return max(0, min(self.max_trades_per_day, self.ABSOLUTE_MAX_TRADES) - self.total_trades_today)


class ProductionStrategyManager:
    """
    Production Strategy Manager for Real Trading
    
    Features:
    - Real order execution through SmartAPI
    - Real balance tracking
    - Global 3 trades per day limit across ALL strategies
    - No virtual accounts or paper trading
    """

    def __init__(
        self,
        logger: logging.Logger,
        market_data_service: MarketDataServiceInterface,
        order_service: OrderServiceInterface,
        config: TradingConfig,
        trading_logger=None,
        initial_trade_count: int = 0,
        initial_traded_symbols: Set[str] = None,
        trade_tracker=None,
        order_manager=None
    ):
        self.logger = logger
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config
        self.trading_logger = trading_logger

        # Enhanced tracking services
        self.external_trade_tracker = trade_tracker  # External DailyTradeTracker
        self.external_order_manager = order_manager  # External OrderStateManager

        # Legacy trade tracking (for backward compatibility)
        self.trade_tracker = TradeTracker(
            max_trades_per_day=config.max_trades_per_day,
            total_trades_today=initial_trade_count,
            traded_symbols=initial_traded_symbols or set()
        )
        
        if initial_trade_count > 0 or (initial_traded_symbols and len(initial_traded_symbols) > 0):
            self.logger.info(f"Initialized trade tracker with {initial_trade_count} existing trades and {len(initial_traded_symbols)} traded symbols")
        
        # Parse square_off_time from config
        try:
            # Parse time string like "15:12" into datetime.time object
            time_parts = config.square_off_time.split(':')
            square_off_time = dt_time(int(time_parts[0]), int(time_parts[1]))
        except (ValueError, AttributeError, IndexError):
            square_off_time = dt_time(15, 12)  # Default to 15:12
            self.logger.warning(f"Invalid square_off_time in config: {getattr(config, 'square_off_time', 'None')}. Using default 15:12")

        self.square_off_time = square_off_time

        # Initialize strategies
        self.strategies = {
            StrategyType.MA_CROSSOVER: MACrossoverStrategy(
                logger=logger,
                market_data_service=market_data_service,
                order_service=order_service,
                config=config
            ),
            StrategyType.SUPPORT_RESISTANCE: SupportResistanceStrategy(
                logger=logger,
                market_data_service=market_data_service,
                order_service=order_service,
                config=config
            ),
            StrategyType.GAP_AND_GO: GapAndGoStrategy(
                logger=logger,
                market_data_service=market_data_service,
                order_service=order_service,
                config=config
            ),
            StrategyType.ORB: ORBStrategy(
                logger=logger,
                market_data_service=market_data_service,
                order_service=order_service,
                config=config
            )
        }

        # Active positions tracking
        self.active_positions: Dict[str, Order] = {}

        # Traded symbols tracking to prevent duplicate orders
        self.traded_symbols: Set[str] = set()

        # Smart balance caching to reduce API calls
        self.balance_cache = None
        self.balance_cache_time = 0
        self.balance_cache_duration = 30  # 30 seconds cache

        self.logger.info("🏭 Production Strategy Manager initialized")
        self.logger.info(f"📊 Global trade limit: {config.max_trades_per_day} trades/day across ALL strategies")
        self.logger.info("💰 Using REAL TRADING with SmartAPI order execution")
        self.logger.info(f"⚡ Balance caching enabled: {self.balance_cache_duration}s cache duration")

    def process_signals(self, signals: List[Signal]) -> List[Order]:
        """
        Process trading signals and execute real orders
        
        Args:
            signals: List of trading signals from strategies
            
        Returns:
            List of executed orders
        """
        if not signals:
            return []

        executed_orders = []
        
        # ENHANCED SAFETY CHECK: Use external trade tracker if available
        if self.external_trade_tracker:
            trade_summary = self.external_trade_tracker.get_daily_summary()
            if not trade_summary['can_trade_more']:
                self.logger.error(f"🛑 ENHANCED SAFETY: Daily trade limit reached via external tracker: {trade_summary['executed_trades']}/{trade_summary['max_trades_per_day']}")
                return []

        # CRITICAL SAFETY CHECK: Hard-coded maximum trades limit (fallback)
        MAX_ALLOWED_TRADES = 3

        # CRITICAL SAFETY CHECK: Verify trade count against hard limit
        if self.trade_tracker.total_trades_today >= MAX_ALLOWED_TRADES:
            self.logger.error(f"🛑 CRITICAL SAFETY: Hard trade limit of {MAX_ALLOWED_TRADES} reached or exceeded: {self.trade_tracker.total_trades_today}/{MAX_ALLOWED_TRADES}")
            return []

        # STRICT CHECK: Enforce the trade limit at the beginning
        if not self.trade_tracker.can_trade():
            self.logger.warning(f"🚫 Daily trade limit reached: {self.trade_tracker.total_trades_today}/{self.trade_tracker.max_trades_per_day}")
            return []

        # Check if it's time to square off
        current_time = datetime.now().time()
        if current_time >= self.square_off_time:
            self.logger.info("⏰ Square-off time reached, no new positions")
            return []

        # Process signals with global trade limit
        remaining_trades = min(MAX_ALLOWED_TRADES - self.trade_tracker.total_trades_today, 
                              self.trade_tracker.get_remaining_trades())
        
        # STRICT CHECK: Ensure we don't exceed the trade limit
        if remaining_trades <= 0:
            self.logger.warning(f"🚫 No remaining trades available: {self.trade_tracker.total_trades_today}/{self.trade_tracker.max_trades_per_day}")
            return []
            
        # ENHANCED ASYNC: Allow processing multiple signals concurrently
        # Limit only by remaining trades, not by artificial batch size
        max_signals_to_process = min(remaining_trades, len(signals))  # Process all available signals up to trade limit
        signals_to_process = signals[:max_signals_to_process]

        self.logger.info(f"🚀 ASYNC PROCESSING: Processing {len(signals_to_process)} signals out of {len(signals)} total")
        self.logger.info(f"📊 Remaining trades: {remaining_trades}, Max signals to process: {max_signals_to_process}")

        # Track how many trades we've executed in this batch
        trades_executed_this_batch = 0

        for signal in signals_to_process:
            try:
                # STRICT CHECK: Stop if we've reached the remaining trade limit
                if trades_executed_this_batch >= remaining_trades:
                    self.logger.warning(f"🚫 Daily trade limit reached: {trades_executed_this_batch}/{remaining_trades}")
                    break
                
                # ENHANCED CHECK: Use external trackers if available
                if self.external_trade_tracker:
                    can_trade, reason = self.external_trade_tracker.can_place_trade(signal.symbol)
                    if not can_trade:
                        self.logger.info(f"⏭️ Skipping {signal.symbol} - {reason}")
                        continue

                if self.external_order_manager:
                    can_place, reason = self.external_order_manager.can_place_order(signal.symbol)
                    if not can_place:
                        self.logger.info(f"⏭️ Skipping {signal.symbol} - {reason}")
                        continue

                # LEGACY CHECK: Check if symbol has already been traded today
                if signal.symbol in self.traded_symbols:
                    self.logger.info(f"⏭️ Skipping {signal.symbol} - already traded today (legacy check)")
                    continue

                # Check if symbol already has an active position
                if signal.symbol in self.active_positions:
                    self.logger.info(f"⏭️ Skipping {signal.symbol} - already has active position")
                    continue

                # CRITICAL SAFETY: Double-check symbol cooldown using trade tracker
                if not self.trade_tracker.can_trade_symbol(signal.symbol):
                    import time
                    time_since_last = time.time() - self.trade_tracker.last_trade_time.get(signal.symbol, 0)
                    cooldown_remaining = self.trade_tracker.symbol_cooldown_seconds - time_since_last
                    self.logger.info(f"⏳ Skipping {signal.symbol} - still in cooldown period ({cooldown_remaining:.1f}s remaining)")
                    continue

                # Determine which strategy generated this signal
                strategy_name = signal.preferred_strategy if signal.preferred_strategy else 'UNKNOWN'
                
                # Map strategy name to StrategyType enum
                strategy_type = None
                if strategy_name == 'MA_CROSSOVER':
                    strategy_type = StrategyType.MA_CROSSOVER
                elif strategy_name == 'SUPPORT_RESISTANCE':
                    strategy_type = StrategyType.SUPPORT_RESISTANCE
                elif strategy_name == 'GAP_AND_GO':
                    strategy_type = StrategyType.GAP_AND_GO
                elif strategy_name == 'ORB':
                    strategy_type = StrategyType.ORB
                else:
                    # Default strategy based on signal type
                    strategy_type = StrategyType.ORB  # Use ORB as default

                # Get the actual strategy object
                strategy = self.strategies.get(strategy_type)
                if not strategy:
                    self.logger.error(f"❌ Strategy {strategy_type} not found for {signal.symbol}")
                    continue

                # TIMING CONTROL: Check if we can place an order now
                if not self.trade_tracker.can_place_order_now():
                    import time
                    time_since_last_order = time.time() - self.trade_tracker.last_order_time
                    remaining_wait = self.trade_tracker.min_order_gap_seconds - time_since_last_order
                    self.logger.info(f"⏳ Order timing control: Must wait {remaining_wait:.1f}s before next order")
                    continue

                # CRITICAL FIX: Do NOT record trade attempt before order placement
                # This was causing pending trades that blocked future orders
                trade_id = None  # Initialize to None for each signal
                strategy_name = signal.preferred_strategy if signal.preferred_strategy else 'UNKNOWN'
                self.logger.info(f"📝 Preparing to place order for {signal.symbol} via {strategy_name}")

                # Lock order placement to prevent concurrent orders
                self.trade_tracker.lock_order_placement()

                try:
                    # Execute the strategy's analyze_signal method
                    strategy_result = None
                    try:
                        strategy_result = strategy.analyze_signal(signal)
                        self.logger.info(f"📊 Strategy analysis for {signal.symbol}: {strategy_result}")
                    except Exception as e:
                        self.logger.error(f"❌ Error executing strategy {strategy_type} for {signal.symbol}: {e}")
                        continue
                    
                    # Handle different return types from strategies
                    order = None
                    if strategy_result is None:
                        self.logger.warning(f"❌ Strategy {strategy_name} returned None for {signal.symbol}")
                        continue
                    elif isinstance(strategy_result, dict):
                        # Strategy returned a dictionary - convert to Order object
                        if strategy_result.get('action') in ['BUY', 'SELL']:
                            # Get symbol token
                            token_info = self.market_data_service.get_symbol_token(signal.symbol)
                            if not token_info:
                                self.logger.warning(f"❌ Cannot get token for {signal.symbol}")
                                continue
                            
                            symbol_token, exchange = token_info
                            
                            # Get strategy's suggested quantity but validate against margin limits
                            suggested_quantity = strategy_result.get('quantity', 1)
                            entry_price = strategy_result.get('entry_price', 0)
                            
                            # CRITICAL: Validate quantity against 3.5x margin limit
                            validated_quantity = self._validate_position_against_margin(suggested_quantity, entry_price, signal.symbol)
                            
                            # Create Order object from strategy result with validated quantity
                            order = Order(
                                symbol=signal.symbol,
                                symbol_token=symbol_token,
                                exchange=exchange,
                                transaction_type=TransactionType.BUY if strategy_result['action'] == 'BUY' else TransactionType.SELL,
                                order_type=OrderType.MARKET,
                                product_type=ProductType.INTRADAY,
                                quantity=validated_quantity,
                                price=entry_price,
                                stop_loss=strategy_result.get('stop_loss', 0),
                                target=strategy_result.get('target', 0),
                                strategy=strategy_result.get('strategy', strategy_name)
                            )
                        else:
                            self.logger.warning(f"❌ {strategy_name} analysis for {signal.symbol}: No valid action ({strategy_result.get('action', 'None')})")
                            self.logger.warning(f"❌ Strategy result keys: {list(strategy_result.keys()) if isinstance(strategy_result, dict) else 'Not a dict'}")
                            continue
                    elif hasattr(strategy_result, 'price') and hasattr(strategy_result, 'quantity'):
                        # Strategy returned an Order object - validate quantity
                        order = strategy_result
                        # CRITICAL: Validate quantity against 3.5x margin limit
                        validated_quantity = self._validate_position_against_margin(order.quantity, order.price, order.symbol)
                        order.quantity = validated_quantity
                    else:
                        # No valid signal
                        self.logger.debug(f"📊 {strategy_name} analysis for {signal.symbol}: No signal generated")
                        continue
                    
                    # Validate order
                    if not order or not hasattr(order, 'price') or order.price <= 0 or not hasattr(order, 'quantity') or order.quantity <= 0:
                        self.logger.warning(f"❌ Invalid order received for {signal.symbol}: order={order}")
                        if order:
                            self.logger.warning(f"❌ Order details: price={getattr(order, 'price', 'N/A')}, quantity={getattr(order, 'quantity', 'N/A')}")
                        continue

                    self.logger.info(f"✅ Valid order created for {signal.symbol}: price=₹{order.price:.2f}, quantity={order.quantity}")
                        
                    # Set order properties if not set by strategy
                    if not hasattr(order, 'strategy') or not order.strategy:
                        order.strategy = strategy_name
                        
                    if not hasattr(order, 'order_id'):
                        order.order_id = None
                        
                    if not hasattr(order, 'status'):
                        order.status = OrderStatus.PENDING
                        
                    if not hasattr(order, 'completed_at'):
                        order.completed_at = None

                finally:
                    # Unlock will be done after order placement
                    pass

                # FINAL VALIDATION: Double-check order value against margin limit before placing
                order_value = order.price * order.quantity
                balance_info = self._get_cached_balance()
                if balance_info:
                    available_cash = balance_info.get('available_cash', 0)
                    margin_limit = available_cash * 3.5
                    
                    if order_value > margin_limit:
                        self.logger.error(f"🚨 CRITICAL: Order value ₹{order_value:,.2f} exceeds margin limit ₹{margin_limit:,.2f} for {order.symbol}")
                        self.logger.error(f"🚨 Order blocked to prevent margin violation!")
                        # Unlock before continuing
                        self.trade_tracker.unlock_order_placement()
                        continue
                    else:
                        self.logger.info(f"✅ Final validation passed for {order.symbol}: ₹{order_value:,.2f} ≤ ₹{margin_limit:,.2f}")
                else:
                    self.logger.warning(f"⚠️ Could not get balance info for margin validation - proceeding with order for {order.symbol}")

                # Place the order through the real order service
                self.logger.info(f"🚀 PLACING ORDER: {order.symbol} - {order.transaction_type.value} {order.quantity} @ ₹{order.price:.2f}")
                try:
                    order_response = self.order_service.place_order(
                        symbol=order.symbol,
                        token=order.symbol_token,
                        exchange=order.exchange if hasattr(order, 'exchange') else "NSE",
                        transaction_type=order.transaction_type,
                        entry_price=order.price,
                        stop_loss=order.stop_loss,
                        target=order.target,
                        quantity=order.quantity
                    )
                    self.logger.info(f"📋 ORDER RESPONSE: {order.symbol} - Response: {order_response}")
                except Exception as order_error:
                    self.logger.error(f"❌ ORDER PLACEMENT FAILED: {order.symbol} - Error: {order_error}")
                    order_response = None
                finally:
                    # Always unlock order placement after attempting to place order
                    self.trade_tracker.unlock_order_placement()

                # CRITICAL FIX: Only record trade AFTER successful order placement
                if order_response and self.external_trade_tracker:
                    from services.daily_trade_tracker import TradeStatus

                    # Record trade attempt ONLY after successful order placement
                    trade_id = self.external_trade_tracker.record_trade_attempt(signal.symbol, strategy_name)
                    self.logger.info(f"📝 Trade recorded with ID: {trade_id} after successful order placement")

                    # Order was placed successfully
                    order.order_id = getattr(order_response, 'order_id', None)
                    order.status = getattr(order_response, 'status', OrderStatus.PENDING)
                    order.completed_at = getattr(order_response, 'completed_at', None)

                    if order_response.status == OrderStatus.COMPLETED:
                        # Order was immediately executed
                        self.external_trade_tracker.update_trade_status(
                            trade_id=trade_id,
                            status=TradeStatus.EXECUTED,
                            order_id=order.order_id,
                            quantity=order.quantity,
                            price=order.price,
                            notes=f"Order immediately executed via {strategy_name}"
                        )
                    else:
                        # Order was placed but not yet executed (PENDING/OPEN)
                        self.external_trade_tracker.update_trade_status(
                            trade_id=trade_id,
                            status=TradeStatus.EXECUTED,  # Consider placed orders as executed for tracking
                            order_id=order.order_id,
                            quantity=order.quantity,
                            price=order.price,
                            notes=f"Order placed via {strategy_name} (Status: {order_response.status})"
                        )
                elif not order_response:
                    # Order placement failed - do not record any trade
                    self.logger.warning(f"❌ Order placement failed for {signal.symbol} - no trade recorded")

                # CRITICAL FIX: Only process successful orders
                if order_response:
                    # ENHANCED TRACKING: Record order in external order manager
                    if self.external_order_manager:
                        self.external_order_manager.record_order_placement(order)

                    # Add to executed orders
                    executed_orders.append(order)
                    trades_executed_this_batch += 1

                    # Log to trading logger
                    if self.trading_logger:
                        self.trading_logger.log_order_placed(
                            order=order,
                            strategy=strategy_name,
                            notes=f"Real order placed via {strategy_name} strategy"
                        )

                    # Log success
                    self.logger.info(f"✅ Trade executed: {signal.symbol} via {strategy_name}")
                    self.logger.info(f"📊 Daily progress: {trades_executed_this_batch}/{remaining_trades}")
                    self.logger.info(f"⏱️ Order placement timing recorded - next order allowed in {self.trade_tracker.min_order_gap_seconds}s")

                    # CRITICAL SAFETY: Break immediately after successful trade to prevent multiple trades
                    self.logger.info("🛑 SAFETY: Breaking after successful trade to prevent multiple executions")
                    break
                else:
                    # ENHANCED TRACKING: Update trade status as rejected if order failed
                    # Only update if trade_id was actually assigned (trade was recorded)
                    if self.external_trade_tracker and trade_id is not None:
                        from services.daily_trade_tracker import TradeStatus
                        self.external_trade_tracker.update_trade_status(
                            trade_id=trade_id,
                            status=TradeStatus.REJECTED,
                            notes="Order placement failed"
                        )

            except Exception as e:
                self.logger.error(f"❌ Error processing signal for {signal.symbol}: {e}")

                # ENHANCED TRACKING: Update trade status as rejected if there was an exception
                # Only update if trade_id was actually assigned (trade was recorded)
                if self.external_trade_tracker and trade_id is not None:
                    from services.daily_trade_tracker import TradeStatus
                    self.external_trade_tracker.update_trade_status(
                        trade_id=trade_id,
                        status=TradeStatus.REJECTED,
                        notes=f"Exception during order processing: {str(e)}"
                    )

                continue

        # Final check to ensure we didn't exceed the limit
        if self.trade_tracker.total_trades_today > self.trade_tracker.max_trades_per_day:
            self.logger.error(f"⚠️ TRADE LIMIT EXCEEDED: {self.trade_tracker.total_trades_today}/{self.trade_tracker.max_trades_per_day}")
            # Force correct the count to prevent further trades
            self.trade_tracker.total_trades_today = self.trade_tracker.max_trades_per_day

        # CRITICAL SAFETY: If we've reached the absolute maximum, return empty list to prevent further orders
        ABSOLUTE_MAX_TRADES = 3
        if self.trade_tracker.total_trades_today >= ABSOLUTE_MAX_TRADES:
            self.logger.error(f"🛑 CRITICAL SAFETY: Absolute trade limit reached in strategy manager!")
            self.logger.error(f"🛑 Count: {self.trade_tracker.total_trades_today}/{ABSOLUTE_MAX_TRADES}")
            self.logger.error("🛑 Returning empty order list to prevent further trading")
            return []

        return executed_orders

    def _execute_signal(self, signal: Signal) -> Optional[Order]:
        """Execute a trading signal using real order service"""
        try:
            # CRITICAL SAFETY: Hard-coded maximum trades limit
            MAX_ALLOWED_TRADES = 3
            
            # CRITICAL SAFETY: Verify trade count before proceeding
            if self.trade_tracker.total_trades_today >= MAX_ALLOWED_TRADES:
                self.logger.error(f"🛑 CRITICAL SAFETY: Cannot execute signal - already at maximum trades ({self.trade_tracker.total_trades_today}/{MAX_ALLOWED_TRADES})")
                return None
                
            # CRITICAL SAFETY: Check if symbol has already been traded today
            if signal.symbol in self.trade_tracker.traded_symbols:
                self.logger.error(f"🛑 CRITICAL SAFETY: Cannot trade {signal.symbol} - already traded today")
                return None
                
            # Get current market price
            current_price = self.market_data_service.get_current_price(signal.symbol)
            if not current_price:
                self.logger.error(f"❌ Cannot get current price for {signal.symbol}")
                return None

            # Analyze market conditions using the appropriate strategy to determine actual signal
            actual_signal = self._analyze_market_conditions(signal, current_price)
            if not actual_signal:
                # No valid signal found after analysis
                return None

            # Use the analyzed signal instead of the original
            signal = actual_signal

            # Ensure signal has trading parameters set
            self._ensure_signal_trading_parameters(signal, current_price)

            # Calculate position size based on risk
            position_size = self._calculate_position_size(signal, current_price)
            if position_size <= 0:
                self.logger.warning(f"⚠️ Invalid position size for {signal.symbol}")
                return None

            # Validate order value against 3.5x margin limit
            order_value = current_price * position_size
            if not self._validate_order_against_margin_limit(order_value, signal.symbol):
                return None

            # Get symbol token and exchange
            token_info = self.market_data_service.get_symbol_token(signal.symbol)
            if not token_info:
                self.logger.error(f"❌ Cannot get token for {signal.symbol}")
                return None

            symbol_token, exchange = token_info

            # Create order
            order = Order(
                symbol=signal.symbol,
                symbol_token=symbol_token,
                exchange=exchange,
                transaction_type=TransactionType.BUY if signal.signal_type.name.startswith('BUY') else TransactionType.SELL,
                order_type=OrderType.MARKET,
                product_type=ProductType.INTRADAY,
                quantity=position_size,
                price=current_price,
                stop_loss=signal.stop_loss,
                target=signal.target,
                strategy=signal.strategy
            )

            # Execute order through SmartAPI
            executed_order = self.order_service.place_order(
                symbol=order.symbol,
                token=order.symbol_token,
                exchange=order.exchange,
                transaction_type=order.transaction_type,
                entry_price=order.price,
                stop_loss=order.stop_loss,
                target=order.target,
                quantity=order.quantity
            )

            if executed_order:
                # Order was placed successfully (even if status is PENDING/OPEN)
                # SmartAPI orders typically start as PENDING/OPEN, not COMPLETED
                order_id = getattr(executed_order, 'order_id', 'Unknown')
                self.logger.info(f"✅ Order placed successfully: {signal.symbol} @ ₹{current_price:.2f} (Status: {executed_order.status}, ID: {order_id})")

                # CRITICAL: Record trade immediately when order is placed (not when completed)
                # This prevents exceeding trade limits
                strategy_name = getattr(signal, 'preferred_strategy', 'UNKNOWN')
                self.trade_tracker.record_trade(strategy_name, signal.symbol)
                self.traded_symbols.add(signal.symbol)
                
                self.logger.info(f"📊 Trade recorded: {self.trade_tracker.total_trades_today}/{self.trade_tracker.max_trades_per_day} trades used")

                # Invalidate balance cache since balance changed
                self._invalidate_balance_cache()

                # Track active position
                self.active_positions[signal.symbol] = executed_order
                self.logger.info(f"🎯 Position opened: {signal.symbol} @ ₹{current_price:.2f}")

                return executed_order
            else:
                self.logger.error(f"❌ Order execution failed for {signal.symbol}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error in _execute_signal for {signal.symbol}: {e}")
            return None

    def _analyze_market_conditions(self, signal: Signal, current_price: float) -> Optional[Signal]:
        """Analyze market conditions to determine if there's a valid trading signal"""
        try:
            # Determine which strategy to use based on signal's preferred strategy
            strategy_name = getattr(signal, 'preferred_strategy', 'ORB')

            # Map strategy names to strategy types
            strategy_mapping = {
                'MA_CROSSOVER': StrategyType.MA_CROSSOVER,
                'SUPPORT_RESISTANCE': StrategyType.SUPPORT_RESISTANCE,
                'GAP_AND_GO': StrategyType.GAP_AND_GO,
                'ORB': StrategyType.ORB,
                'GAPANDGO': StrategyType.GAP_AND_GO  # Alternative name
            }

            strategy_type = strategy_mapping.get(strategy_name, StrategyType.ORB)
            strategy = self.strategies.get(strategy_type)

            if not strategy:
                self.logger.warning(f"⚠️ Strategy {strategy_name} not found, skipping {signal.symbol}")
                return None

            self.logger.debug(f"🔍 Analyzing {signal.symbol} using {strategy_name} strategy (current price: ₹{current_price:.2f})")

            # Use the strategy to analyze the signal
            # Most strategies have a process_signal method that returns analysis results
            if hasattr(strategy, 'process_signal'):
                try:
                    analysis_result = strategy.process_signal(signal)

                    if analysis_result and isinstance(analysis_result, dict):
                        # Convert analysis result to signal
                        if analysis_result.get('action') in ['BUY', 'SELL', 'LONG', 'SHORT']:
                            # Create new signal with analyzed parameters
                            analyzed_signal = Signal(
                                symbol=signal.symbol,
                                signal_type=SignalType.BUY if analysis_result['action'] in ['BUY', 'LONG'] else SignalType.SELL,
                                timeframe=signal.timeframe
                            )

                            # Set analyzed parameters
                            analyzed_signal.price = analysis_result.get('entry_price', current_price)
                            analyzed_signal.entry_price = analysis_result.get('entry_price', current_price)
                            analyzed_signal.stop_loss = analysis_result.get('stop_loss')
                            analyzed_signal.target = analysis_result.get('target')
                            analyzed_signal.strategy = analysis_result.get('strategy', strategy_name)
                            analyzed_signal.preferred_strategy = strategy_name

                            self.logger.info(f"✅ {strategy_name} signal for {signal.symbol}: {analysis_result['action']} @ ₹{analyzed_signal.price:.2f} (SL: ₹{analyzed_signal.stop_loss:.2f}, Target: ₹{analyzed_signal.target:.2f})")
                            return analyzed_signal
                        else:
                            self.logger.debug(f"📊 {strategy_name} analysis for {signal.symbol}: No valid action ({analysis_result.get('action', 'None')})")
                            return None
                    else:
                        self.logger.debug(f"📊 {strategy_name} analysis for {signal.symbol}: No signal generated (result: {type(analysis_result)})")
                        return None

                except Exception as e:
                    self.logger.error(f"❌ Error in {strategy_name} analysis for {signal.symbol}: {e}")
                    import traceback
                    self.logger.debug(f"Strategy analysis traceback: {traceback.format_exc()}")
                    return None
            else:
                self.logger.warning(f"⚠️ Strategy {strategy_name} does not support signal analysis")
                return None

        except Exception as e:
            self.logger.error(f"❌ Error analyzing market conditions for {signal.symbol}: {e}")
            return None

    def _get_cached_balance(self):
        """Get balance with smart caching and retry logic to reduce API calls"""
        import time
        current_time = time.time()

        # Check if cache is still valid
        if (self.balance_cache is not None and
            current_time - self.balance_cache_time < self.balance_cache_duration):
            self.logger.debug(f"💾 Using cached balance (age: {current_time - self.balance_cache_time:.1f}s)")
            return self.balance_cache

        # Cache expired or doesn't exist, fetch new balance with retry logic
        self.logger.debug("🔄 Fetching fresh balance from API")
        balance_info = self._get_balance_with_retry()

        if balance_info:
            self.balance_cache = balance_info
            self.balance_cache_time = current_time
            self.logger.debug(f"💾 Balance cached for {self.balance_cache_duration}s")
        else:
            # If fresh fetch fails, extend cache validity if we have old data
            if self.balance_cache is not None:
                self.logger.warning("⚠️ Balance fetch failed, extending cache validity by 60s")
                self.balance_cache_time = current_time - self.balance_cache_duration + 60
                return self.balance_cache

        return balance_info

    def _get_balance_with_retry(self, max_retries: int = 3, retry_delay: float = 1.0):
        """Get balance with retry logic and exponential backoff"""
        for attempt in range(max_retries):
            try:
                balance_info = self.order_service.get_real_balance()
                if balance_info:
                    if attempt > 0:
                        self.logger.info(f"✅ Balance fetch succeeded on attempt {attempt + 1}")
                    return balance_info
                else:
                    self.logger.warning(f"⚠️ Balance fetch returned None on attempt {attempt + 1}")

            except Exception as e:
                self.logger.warning(f"⚠️ Balance fetch attempt {attempt + 1} failed: {e}")

            # Wait before retry (exponential backoff)
            if attempt < max_retries - 1:
                wait_time = retry_delay * (2 ** attempt)
                self.logger.info(f"⏳ Retrying balance fetch in {wait_time:.1f}s...")
                time.sleep(wait_time)

        self.logger.error(f"❌ All {max_retries} balance fetch attempts failed")
        return None

    def _invalidate_balance_cache(self):
        """Invalidate balance cache (call after order execution)"""
        self.balance_cache = None
        self.balance_cache_time = 0
        self.logger.debug("💾 Balance cache invalidated")

    def _calculate_position_size(self, signal: Signal, current_price: float) -> int:
        """
        Calculate position size based on risk management
        
        The system uses 1% of capital for risk in the morning and maintains the same risk throughout the day.
        This ensures consistent risk management regardless of P&L fluctuations during the day.
        """
        try:
            # Get available balance (with caching)
            balance_info = self._get_cached_balance()
            if not balance_info:
                self.logger.error("❌ Cannot get real balance")
                return 0

            # Use available cash - Angel One will automatically provide intraday margin
            available_cash = balance_info.get('available_cash', 0)
            available_margin = balance_info.get('available_margin', 0)

            # Use margin if available, otherwise use cash (Angel One provides auto margin)
            trading_balance = available_margin if available_margin > 0 else available_cash

            if trading_balance <= 0:
                self.logger.error("❌ No available cash or margin")
                return 0

            self.logger.info(f"💰 Using trading balance: ₹{trading_balance:.2f} (Cash: ₹{available_cash:.2f}, Margin: ₹{available_margin:.2f})")

            # Use fixed risk amount from config (which is set to 1% of initial balance)
            # This ensures consistent risk throughout the day regardless of P&L fluctuations
            risk_amount = self.config.max_risk_per_trade
            
            self.logger.info(f"🎯 Using fixed risk amount: ₹{risk_amount:.2f} (1% of initial balance)")

            # Calculate stop loss distance if stop_loss is available
            if hasattr(signal, 'stop_loss') and signal.stop_loss and signal.stop_loss > 0:
                stop_loss_distance = abs(current_price - signal.stop_loss)
                if stop_loss_distance > 0:
                    position_size = int(risk_amount / stop_loss_distance)
                    position_size = max(1, position_size)  # Minimum 1 share
                    
                    # Validate against 3.5x margin limit
                    position_size = self._validate_position_against_margin(position_size, current_price, signal.symbol)
                    return position_size

            # Fallback: Use intraday-appropriate stop loss for position sizing
            default_stop_loss_percent = self.config.intraday_stop_loss_percent  # 0.8% for intraday
            stop_loss_distance = current_price * default_stop_loss_percent
            position_size = int(risk_amount / stop_loss_distance)
            position_size = max(1, position_size)
            
            # Validate against 3.5x margin limit
            position_size = self._validate_position_against_margin(position_size, current_price, signal.symbol)
            return position_size

        except Exception as e:
            self.logger.error(f"❌ Error calculating position size: {e}")
            return 0

    def _validate_position_against_margin(self, position_size: int, current_price: float, symbol: str) -> int:
        """
        Validate and adjust position size to ensure it doesn't exceed 3.5x margin limit
        
        Args:
            position_size: Calculated position size
            current_price: Current stock price
            symbol: Stock symbol
            
        Returns:
            int: Adjusted position size that fits within margin limits
        """
        try:
            # Calculate order value
            order_value = position_size * current_price
            
            # Get current available balance
            balance_info = self._get_cached_balance()
            if not balance_info:
                self.logger.error(f"❌ Cannot get balance for margin validation for {symbol}")
                return 1  # Return minimum position size
            
            # Use available cash for margin calculation
            available_cash = balance_info.get('available_cash', 0)
            
            if available_cash <= 0:
                self.logger.error(f"❌ No available cash for {symbol}")
                return 1
            
            # Calculate 3.5x margin limit
            margin_limit = available_cash * 3.5
            
            if order_value <= margin_limit:
                self.logger.info(f"✅ Position size {position_size} within margin limit for {symbol}")
                self.logger.info(f"   Order Value: ₹{order_value:,.2f} / Limit: ₹{margin_limit:,.2f}")
                return position_size
            
            # Adjust position size to fit within margin limit
            adjusted_position_size = int(margin_limit / current_price)
            adjusted_position_size = max(1, adjusted_position_size)  # Minimum 1 share
            
            self.logger.warning(f"⚠️ Position size adjusted for {symbol} due to margin limit")
            self.logger.warning(f"   Original: {position_size} shares (₹{order_value:,.2f})")
            self.logger.warning(f"   Adjusted: {adjusted_position_size} shares (₹{adjusted_position_size * current_price:,.2f})")
            self.logger.warning(f"   Margin Limit: ₹{margin_limit:,.2f} (Available Cash: ₹{available_cash:,.2f} × 3.5)")
            
            return adjusted_position_size
            
        except Exception as e:
            self.logger.error(f"❌ Error validating position against margin for {symbol}: {e}")
            return 1  # Return minimum position size as fallback

    def _validate_order_against_margin_limit(self, order_value: float, symbol: str) -> bool:
        """Validate order value against 3.5x margin limit as per user requirements"""
        try:
            # Get current available balance
            balance_info = self._get_cached_balance()
            if not balance_info:
                self.logger.error("❌ Cannot get balance for margin validation")
                return False

            # Use available cash for margin calculation
            available_cash = balance_info.get('available_cash', 0)
            available_margin = balance_info.get('available_margin', 0)

            # Use the higher of cash or margin for validation
            current_balance = max(available_cash, available_margin)

            if current_balance <= 0:
                self.logger.error(f"❌ No available balance for {symbol}")
                return False

            # Calculate 3.5x margin limit
            margin_limit = current_balance * 3.5

            if order_value > margin_limit:
                self.logger.error(f"❌ Order value exceeds 3.5x margin limit for {symbol}")
                self.logger.error(f"   Order Value: ₹{order_value:,.2f}")
                self.logger.error(f"   Available Balance: ₹{current_balance:,.2f}")
                self.logger.error(f"   3.5x Margin Limit: ₹{margin_limit:,.2f}")
                self.logger.error(f"   Excess: ₹{order_value - margin_limit:,.2f}")
                return False

            self.logger.info(f"✅ Order value within 3.5x margin limit for {symbol}")
            self.logger.info(f"   Order Value: ₹{order_value:,.2f} / Limit: ₹{margin_limit:,.2f}")
            return True

        except Exception as e:
            self.logger.error(f"❌ Error validating margin limit for {symbol}: {e}")
            return False

    def _ensure_signal_trading_parameters(self, signal: Signal, current_price: float):
        """Ensure signal has all required trading parameters (price, stop_loss, target, strategy)"""
        try:
            # Set price if not already set
            if not signal.price:
                signal.price = current_price
                signal.entry_price = current_price

            # Set default stop_loss if not set (2% stop loss)
            if not signal.stop_loss:
                stop_loss_percent = 0.02  # 2% stop loss
                if signal.signal_type.name == 'BUY':
                    signal.stop_loss = current_price * (1 - stop_loss_percent)
                else:  # SELL
                    signal.stop_loss = current_price * (1 + stop_loss_percent)

            # Set default target if not set (1:2 risk-reward ratio)
            if not signal.target:
                risk_distance = abs(current_price - signal.stop_loss)
                reward_distance = risk_distance * 2.0  # 1:2 risk-reward ratio

                if signal.signal_type.name == 'BUY':
                    signal.target = current_price + reward_distance
                else:  # SELL
                    signal.target = current_price - reward_distance

            # Set default strategy if not set
            if not signal.strategy:
                # Use preferred_strategy or default to a generic strategy name
                signal.strategy = signal.preferred_strategy if signal.preferred_strategy != "ALL" else "GENERIC_STRATEGY"

            self.logger.debug(f"📊 Signal parameters for {signal.symbol}: "
                            f"Price: ₹{signal.price:.2f}, SL: ₹{signal.stop_loss:.2f}, "
                            f"Target: ₹{signal.target:.2f}, Strategy: {signal.strategy}")

        except Exception as e:
            self.logger.error(f"❌ Error setting signal trading parameters: {e}")
            # Set minimal defaults to prevent crashes
            signal.price = current_price
            signal.entry_price = current_price
            signal.stop_loss = current_price * 0.98 if signal.signal_type.name == 'BUY' else current_price * 1.02
            signal.target = current_price * 1.04 if signal.signal_type.name == 'BUY' else current_price * 0.96
            signal.strategy = "DEFAULT_STRATEGY"

    def get_strategy_summary(self) -> Dict:
        """Get summary of all strategies and trades"""
        return {
            'total_trades_today': self.trade_tracker.total_trades_today,
            'max_trades_per_day': self.trade_tracker.max_trades_per_day,
            'remaining_trades': self.trade_tracker.get_remaining_trades(),
            'trades_by_strategy': self.trade_tracker.trades_by_strategy.copy(),
            'active_positions': len(self.active_positions),
            'strategies': list(self.strategies.keys())
        }

    def square_off_all_positions(self):
        """Square off all active positions"""
        if not self.active_positions:
            self.logger.info("📭 No active positions to square off")
            return

        self.logger.info(f"🔄 Squaring off {len(self.active_positions)} active positions")
        
        for symbol, position in list(self.active_positions.items()):
            try:
                current_price = self.market_data_service.get_current_price(symbol)
                if current_price:
                    # Get symbol token for exit order
                    token_info = self.market_data_service.get_symbol_token(symbol)
                    if not token_info:
                        self.logger.error(f"❌ Cannot get token for exit order {symbol}")
                        continue

                    symbol_token, exchange = token_info

                    # Create exit order
                    exit_order = Order(
                        symbol=symbol,
                        symbol_token=symbol_token,
                        exchange=exchange,
                        transaction_type=TransactionType.SELL if position.transaction_type == TransactionType.BUY else TransactionType.BUY,
                        order_type=OrderType.MARKET,
                        product_type=ProductType.INTRADAY,
                        quantity=position.quantity,
                        price=current_price,
                        stop_loss=0.0,  # Not needed for exit orders
                        target=0.0,  # Not needed for exit orders
                        strategy=position.strategy
                    )
                    
                    executed_exit = self.order_service.place_order(
                        symbol=exit_order.symbol,
                        token=exit_order.symbol_token,
                        exchange=exit_order.exchange,
                        transaction_type=exit_order.transaction_type,
                        entry_price=exit_order.price,
                        stop_loss=exit_order.stop_loss,
                        target=exit_order.target,
                        quantity=exit_order.quantity
                    )
                    if executed_exit:
                        # Invalidate balance cache since balance changed
                        self._invalidate_balance_cache()

                        # Remove from active positions and traded symbols
                        del self.active_positions[symbol]
                        self.traded_symbols.discard(symbol)  # Allow symbol to be traded again

                        self.logger.info(f"✅ Squared off {symbol} @ ₹{current_price:.2f}")
                    else:
                        self.logger.error(f"❌ Failed to square off {symbol}")
                        
            except Exception as e:
                self.logger.error(f"❌ Error squaring off {symbol}: {e}")
