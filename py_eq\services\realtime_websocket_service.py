"""
Real-time WebSocket Market Data Service with Candle Formation

This service connects to SmartAPI WebSocket for real-time market data,
forms candles from tick data, and provides strategy-specific timeframes.

Features:
- WebSocket connection to SmartAPI
- Real-time tick data processing
- 5-minute and 15-minute candle formation
- Strategy-specific timeframe routing
- Automatic reconnection
- CSV-based symbol subscription
"""

import logging
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict, deque
import pandas as pd
import pytz
from dataclasses import dataclass, field

try:
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
    SMARTAPI_AVAILABLE = True
except ImportError:
    SMARTAPI_AVAILABLE = False
    SmartWebSocketV2 = None

from models.candle import Candle
from services.symbol_mapping_service import SymbolMappingService


@dataclass
class TickData:
    """Real-time tick data"""
    symbol: str
    price: float
    volume: int
    timestamp: datetime
    high: float = 0.0
    low: float = 0.0
    open: float = 0.0


@dataclass
class CandleBuilder:
    """Builds candles from tick data"""
    symbol: str
    timeframe: str  # '5min' or '15min'
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: int = 0
    start_time: Optional[datetime] = None
    tick_count: int = 0
    
    def add_tick(self, tick: TickData):
        """Add a tick to the current candle"""
        if self.start_time is None:
            self.start_time = self._get_candle_start_time(tick.timestamp)
            self.open_price = tick.price
            self.high_price = tick.price
            self.low_price = tick.price
        
        self.close_price = tick.price
        self.high_price = max(self.high_price, tick.price)
        self.low_price = min(self.low_price, tick.price)
        self.volume += tick.volume
        self.tick_count += 1
    
    def _get_candle_start_time(self, timestamp: datetime) -> datetime:
        """Get the start time for the current candle based on timeframe"""
        if self.timeframe == '5min':
            # Round down to nearest 5-minute interval
            minutes = (timestamp.minute // 5) * 5
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        elif self.timeframe == '15min':
            # Round down to nearest 15-minute interval
            minutes = (timestamp.minute // 15) * 15
            return timestamp.replace(minute=minutes, second=0, microsecond=0)
        else:
            return timestamp.replace(second=0, microsecond=0)
    
    def is_candle_complete(self, current_time: datetime) -> bool:
        """Check if the current candle is complete"""
        if self.start_time is None:
            return False
        
        if self.timeframe == '5min':
            next_candle_time = self.start_time + timedelta(minutes=5)
        elif self.timeframe == '15min':
            next_candle_time = self.start_time + timedelta(minutes=15)
        else:
            next_candle_time = self.start_time + timedelta(minutes=1)
        
        return current_time >= next_candle_time
    
    def to_candle(self) -> Candle:
        """Convert to Candle object"""
        return Candle(
            timestamp=self.start_time,
            open=self.open_price,
            high=self.high_price,
            low=self.low_price,
            close=self.close_price,
            volume=self.volume
        )


class RealtimeWebSocketService:
    """Real-time WebSocket market data service with candle formation"""
    
    def __init__(self, config, symbol_mapping_service: SymbolMappingService, csv_reader_service):
        self.config = config
        self.symbol_mapping_service = symbol_mapping_service
        self.csv_reader_service = csv_reader_service
        self.logger = logging.getLogger(__name__)
        
        # WebSocket connection
        self.websocket = None
        self.is_connected = False
        self.is_running = False
        
        # Authentication
        self.auth_token = None
        self.feed_token = None
        
        # Symbol management
        self.subscribed_symbols: Set[str] = set()
        self.symbol_tokens: Dict[str, str] = {}
        
        # Candle builders for different timeframes
        self.candle_builders_5min: Dict[str, CandleBuilder] = {}
        self.candle_builders_15min: Dict[str, CandleBuilder] = {}
        
        # Completed candles storage (last 100 candles per symbol per timeframe)
        self.candles_5min: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.candles_15min: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Strategy timeframe mapping
        self.strategy_timeframes = {
            'MA_CROSSOVER': '15min',
            'SUPPORT_RESISTANCE': '15min',
            'GAPANDGO': '5min',
            'ORB': '5min'
        }
        
        # Callbacks
        self.candle_callbacks: List[Callable] = []
        self.tick_callbacks: List[Callable] = []
        
        # Statistics
        self.stats = {
            'ticks_received': 0,
            'candles_formed_5min': 0,
            'candles_formed_15min': 0,
            'last_tick_time': None,
            'connection_time': None
        }
        
        # IST timezone
        self.ist_timezone = pytz.timezone('Asia/Kolkata')
    
    def set_authentication(self, auth_token: str, feed_token: str):
        """Set authentication tokens"""
        self.auth_token = auth_token
        self.feed_token = feed_token
        self.logger.info("Authentication tokens set for WebSocket")
    
    def add_candle_callback(self, callback: Callable):
        """Add callback for new candles"""
        self.candle_callbacks.append(callback)
    
    def add_tick_callback(self, callback: Callable):
        """Add callback for new ticks"""
        self.tick_callbacks.append(callback)
    
    async def initialize(self) -> bool:
        """Initialize the WebSocket service"""
        try:
            if not SMARTAPI_AVAILABLE:
                self.logger.error("SmartAPI WebSocket not available - install smartapi-python")
                return False
            
            if not self.auth_token or not self.feed_token:
                self.logger.error("Authentication tokens required")
                return False
            
            # Load symbols from CSV
            await self._load_symbols_from_csv()
            
            # Initialize WebSocket
            self._initialize_websocket()
            
            self.logger.info(f"✅ Real-time WebSocket service initialized with {len(self.subscribed_symbols)} symbols")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize WebSocket service: {e}")
            return False
    
    async def _load_symbols_from_csv(self):
        """Load symbols from CSV and get their tokens"""
        try:
            # Get all enabled stocks from CSV
            stocks = self.csv_reader_service.get_enabled_stocks()
            
            for stock in stocks:
                symbol = stock['symbol']
                strategy = stock.get('strategy', 'ALL')
                
                # Get symbol token
                token_info = self.symbol_mapping_service.get_symbol_token(symbol)
                if token_info:
                    token, exchange = token_info
                    self.symbol_tokens[symbol] = token
                    self.subscribed_symbols.add(symbol)
                    
                    # Initialize candle builders based on strategy timeframe
                    timeframe = self.strategy_timeframes.get(strategy, '15min')
                    if timeframe == '5min' or strategy in ['GAPANDGO', 'ORB']:
                        self.candle_builders_5min[symbol] = CandleBuilder(symbol, '5min')
                    if timeframe == '15min' or strategy in ['MA_CROSSOVER', 'SUPPORT_RESISTANCE']:
                        self.candle_builders_15min[symbol] = CandleBuilder(symbol, '15min')
                else:
                    self.logger.warning(f"Could not get token for symbol: {symbol}")
            
            self.logger.info(f"Loaded {len(self.subscribed_symbols)} symbols for WebSocket subscription")
            
        except Exception as e:
            self.logger.error(f"Error loading symbols from CSV: {e}")
    
    def _initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.websocket = SmartWebSocketV2(
                self.auth_token,
                self.config.smartapi.api_key,
                self.config.smartapi.client_id,
                self.feed_token,
                max_retry_attempt=5
            )
            
            # Set up callbacks
            self.websocket.on_open = self._on_open
            self.websocket.on_data = self._on_data
            self.websocket.on_error = self._on_error
            self.websocket.on_close = self._on_close
            
            self.logger.info("WebSocket initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket: {e}")
            raise
    
    async def start(self):
        """Start the WebSocket service"""
        try:
            if not self.websocket:
                raise Exception("WebSocket not initialized")
            
            self.is_running = True
            
            # Start WebSocket in a separate thread
            websocket_thread = threading.Thread(target=self._run_websocket, daemon=True)
            websocket_thread.start()
            
            # Start candle formation loop
            asyncio.create_task(self._candle_formation_loop())
            
            self.logger.info("✅ Real-time WebSocket service started")
            
        except Exception as e:
            self.logger.error(f"Failed to start WebSocket service: {e}")
            raise
    
    def _run_websocket(self):
        """Run WebSocket in separate thread"""
        try:
            self.websocket.connect()
        except Exception as e:
            self.logger.error(f"WebSocket connection error: {e}")
    
    async def _candle_formation_loop(self):
        """Main loop for candle formation"""
        while self.is_running:
            try:
                current_time = datetime.now(self.ist_timezone)
                
                # Check and complete 5-minute candles
                await self._check_and_complete_candles(self.candle_builders_5min, self.candles_5min, '5min', current_time)
                
                # Check and complete 15-minute candles
                await self._check_and_complete_candles(self.candle_builders_15min, self.candles_15min, '15min', current_time)
                
                # Sleep for 1 second
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in candle formation loop: {e}")
                await asyncio.sleep(5)

    async def _check_and_complete_candles(self, builders: Dict[str, CandleBuilder],
                                        candles_storage: Dict[str, deque],
                                        timeframe: str, current_time: datetime):
        """Check and complete candles for a specific timeframe"""
        completed_candles = []

        for symbol, builder in list(builders.items()):
            if builder.start_time and builder.is_candle_complete(current_time):
                # Complete the candle
                candle = builder.to_candle()
                candles_storage[symbol].append(candle)
                completed_candles.append(candle)

                # Reset builder for next candle
                builders[symbol] = CandleBuilder(symbol, timeframe)

                # Update statistics
                if timeframe == '5min':
                    self.stats['candles_formed_5min'] += 1
                else:
                    self.stats['candles_formed_15min'] += 1

        # Notify callbacks about new candles
        for candle in completed_candles:
            for callback in self.candle_callbacks:
                try:
                    await callback(candle, timeframe)
                except Exception as e:
                    self.logger.error(f"Error in candle callback: {e}")

    def _on_open(self, ws):
        """WebSocket opened callback"""
        self.is_connected = True
        self.stats['connection_time'] = datetime.now(self.ist_timezone)
        self.logger.info("✅ WebSocket connection opened")

        # Subscribe to symbols
        self._subscribe_to_symbols()

    def _on_data(self, ws, message):
        """WebSocket data received callback"""
        try:
            # Parse the message and create tick data
            tick = self._parse_tick_data(message)
            if tick:
                self._process_tick(tick)

        except Exception as e:
            self.logger.error(f"Error processing WebSocket data: {e}")

    def _on_error(self, ws, error):
        """WebSocket error callback"""
        self.logger.error(f"WebSocket error: {error}")
        self.is_connected = False

    def _on_close(self, ws):
        """WebSocket closed callback"""
        self.logger.warning("WebSocket connection closed")
        self.is_connected = False

        # Attempt reconnection if still running
        if self.is_running:
            self.logger.info("Attempting to reconnect WebSocket...")
            threading.Timer(5.0, self._reconnect).start()

    def _reconnect(self):
        """Reconnect WebSocket"""
        try:
            if self.is_running and not self.is_connected:
                self.websocket.connect()
        except Exception as e:
            self.logger.error(f"Reconnection failed: {e}")

    def _subscribe_to_symbols(self):
        """Subscribe to all symbols"""
        try:
            if not self.symbol_tokens:
                self.logger.warning("No symbol tokens available for subscription")
                return

            # Prepare subscription data
            token_list = list(self.symbol_tokens.values())

            # Subscribe to symbols (SmartAPI WebSocket format)
            subscription_data = {
                "action": 1,  # Subscribe
                "mode": 1,    # LTP mode
                "tokenList": [
                    {
                        "exchangeType": 1,  # NSE
                        "tokens": token_list
                    }
                ]
            }

            self.websocket.send_request(subscription_data)
            self.logger.info(f"Subscribed to {len(token_list)} symbols")

        except Exception as e:
            self.logger.error(f"Error subscribing to symbols: {e}")

    def _parse_tick_data(self, message) -> Optional[TickData]:
        """Parse WebSocket message to TickData"""
        try:
            # SmartAPI WebSocket message format parsing
            if isinstance(message, dict):
                token = message.get('token')
                if not token:
                    return None

                # Find symbol by token
                symbol = None
                for sym, tok in self.symbol_tokens.items():
                    if tok == token:
                        symbol = sym
                        break

                if not symbol:
                    return None

                # Extract price and volume data
                price = float(message.get('last_traded_price', 0)) / 100  # Convert from paise
                volume = int(message.get('volume_traded', 0))

                if price <= 0:
                    return None

                tick = TickData(
                    symbol=symbol,
                    price=price,
                    volume=volume,
                    timestamp=datetime.now(self.ist_timezone),
                    high=float(message.get('high_price_of_day', 0)) / 100,
                    low=float(message.get('low_price_of_day', 0)) / 100,
                    open=float(message.get('open_price_of_day', 0)) / 100
                )

                return tick

        except Exception as e:
            self.logger.error(f"Error parsing tick data: {e}")

        return None

    def _process_tick(self, tick: TickData):
        """Process incoming tick data"""
        try:
            # Update statistics
            self.stats['ticks_received'] += 1
            self.stats['last_tick_time'] = tick.timestamp

            # Add tick to 5-minute candle builder if exists
            if tick.symbol in self.candle_builders_5min:
                self.candle_builders_5min[tick.symbol].add_tick(tick)

            # Add tick to 15-minute candle builder if exists
            if tick.symbol in self.candle_builders_15min:
                self.candle_builders_15min[tick.symbol].add_tick(tick)

            # Notify tick callbacks
            for callback in self.tick_callbacks:
                try:
                    callback(tick)
                except Exception as e:
                    self.logger.error(f"Error in tick callback: {e}")

        except Exception as e:
            self.logger.error(f"Error processing tick: {e}")

    def get_latest_candles(self, symbol: str, timeframe: str, count: int = 10) -> List[Candle]:
        """Get latest candles for a symbol and timeframe"""
        try:
            if timeframe == '5min':
                candles = list(self.candles_5min.get(symbol, []))
            elif timeframe == '15min':
                candles = list(self.candles_15min.get(symbol, []))
            else:
                return []

            return candles[-count:] if candles else []

        except Exception as e:
            self.logger.error(f"Error getting latest candles: {e}")
            return []

    def get_current_candle(self, symbol: str, timeframe: str) -> Optional[Candle]:
        """Get current forming candle"""
        try:
            if timeframe == '5min' and symbol in self.candle_builders_5min:
                builder = self.candle_builders_5min[symbol]
            elif timeframe == '15min' and symbol in self.candle_builders_15min:
                builder = self.candle_builders_15min[symbol]
            else:
                return None

            if builder.start_time:
                return builder.to_candle()

        except Exception as e:
            self.logger.error(f"Error getting current candle: {e}")

        return None

    def get_statistics(self) -> Dict:
        """Get service statistics"""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'is_running': self.is_running,
            'subscribed_symbols': len(self.subscribed_symbols),
            'active_5min_builders': len(self.candle_builders_5min),
            'active_15min_builders': len(self.candle_builders_15min)
        }

    async def stop(self):
        """Stop the WebSocket service"""
        try:
            self.is_running = False

            if self.websocket and self.is_connected:
                self.websocket.close()

            self.logger.info("Real-time WebSocket service stopped")

        except Exception as e:
            self.logger.error(f"Error stopping WebSocket service: {e}")
