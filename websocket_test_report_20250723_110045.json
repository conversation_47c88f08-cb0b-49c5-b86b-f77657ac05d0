{"basic_diagnostics": {"status": "completed", "results": {"environment": {"smartapi_available": true, "api_key_set": true, "username_set": true, "password_set": true, "totp_token_set": true, "api_key_length": 8, "username_format": true, "totp_token_length": 26, "totp_generation": true}, "network": {"internet_connectivity": true, "smartapi_server_apiconnect.angelbroking.com": true, "smartapi_server_smartapi.angelbroking.com": true, "websocket_port_443": true}, "authentication": {"smartconnect_init": true, "totp_generated": true, "login_successful": true, "jwt_token_received": true, "feed_token_received": true}, "websocket": {"websocket_init": true, "connection_opened": true, "connection_successful": true}, "recommendations": []}, "recommendations_count": 0, "critical_issues": false}, "enhanced_websocket": {"status": "partial_success", "authentication": true, "connection": false}, "dynamic_workflow": {"status": "completed", "stocks_selected": false, "market_data_received": false, "final_status": {"current_phase": "active_trading", "monitoring_active": false, "selected_stocks_count": 0, "selected_stocks": [], "market_condition": null, "websocket_status": {"state": "failed", "metrics": {"connection_attempts": 1, "successful_connections": 0, "failed_connections": 1, "last_connection_time": null, "last_error": null, "total_messages_received": 0, "subscribed_symbols_count": 0}, "is_connected": false}}}}