"""
Order and Position models for trading operations
"""
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from typing import Optional


class OrderStatus(Enum):
    """Order status enumeration"""
    OPEN = "OPEN"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    SQUARED_OFF = "SQUARED_OFF"


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"


class TransactionType(Enum):
    """Transaction type enumeration"""
    BUY = "BUY"
    SELL = "SELL"


class ProductType(Enum):
    """Product type enumeration"""
    INTRADAY = "INTRADAY"
    DELIVERY = "DELIVERY"


class ExitReason(Enum):
    """Exit reason enumeration"""
    STOP_LOSS = "STOP_LOSS"
    TARGET = "TARGET"
    END_OF_DAY = "END_OF_DAY"
    MANUAL = "MANUAL"
    ERROR = "ERROR"
    UNSPECIFIED = "UNSPECIFIED"


@dataclass
class Order:
    """Represents an order placed in the market"""
    symbol: str
    symbol_token: str
    exchange: str
    transaction_type: TransactionType
    order_type: OrderType
    product_type: ProductType
    quantity: int
    price: float
    stop_loss: float
    target: float

    # Order tracking
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.OPEN
    entry_price: Optional[float] = None
    placed_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Strategy tracking
    strategy: Optional[str] = None

    def __post_init__(self):
        """Initialize order with current timestamp and validate critical fields"""
        if self.placed_at is None:
            self.placed_at = datetime.now()

        # CRITICAL VALIDATION: Prevent zero or negative entry prices
        if self.entry_price is not None and self.entry_price <= 0:
            raise ValueError(f"Invalid entry_price: {self.entry_price}. Entry price must be positive.")

        # CRITICAL VALIDATION: Prevent zero or negative prices
        if self.price <= 0:
            raise ValueError(f"Invalid price: {self.price}. Price must be positive.")

        # CRITICAL VALIDATION: Prevent zero or negative quantity
        if self.quantity <= 0:
            raise ValueError(f"Invalid quantity: {self.quantity}. Quantity must be positive.")

        # Set entry_price to price if not provided (for immediate execution orders)
        if self.entry_price is None:
            self.entry_price = self.price

    @property
    def is_buy_order(self) -> bool:
        """Returns True if this is a buy order"""
        return self.transaction_type == TransactionType.BUY

    @property
    def is_sell_order(self) -> bool:
        """Returns True if this is a sell order"""
        return self.transaction_type == TransactionType.SELL

    @property
    def risk_amount(self) -> float:
        """Calculate the risk amount for this order"""
        if self.entry_price:
            return abs(self.entry_price - self.stop_loss) * self.quantity
        return abs(self.price - self.stop_loss) * self.quantity

    @property
    def potential_profit(self) -> float:
        """Calculate the potential profit for this order"""
        if self.entry_price:
            return abs(self.target - self.entry_price) * self.quantity
        return abs(self.target - self.price) * self.quantity

    @property
    def risk_reward_ratio(self) -> float:
        """Calculate the risk-reward ratio"""
        risk = self.risk_amount
        if risk == 0:
            return 0
        return self.potential_profit / risk

    def to_dict(self) -> dict:
        """Convert order to dictionary"""
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'symbol_token': self.symbol_token,
            'exchange': self.exchange,
            'transaction_type': self.transaction_type.value,
            'order_type': self.order_type.value,
            'product_type': self.product_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_loss': self.stop_loss,
            'target': self.target,
            'status': self.status.value,
            'entry_price': self.entry_price,
            'placed_at': self.placed_at.isoformat() if self.placed_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'strategy': self.strategy
        }


@dataclass
class Position:
    """Represents an open position in the market"""
    order: Order
    entry_time: datetime
    is_open: bool = True

    # Exit details
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_order_id: Optional[str] = None
    exit_reason: ExitReason = ExitReason.UNSPECIFIED
    pnl: float = 0.0

    @property
    def current_pnl(self) -> float:
        """Calculate current P&L if position is still open"""
        if not self.is_open or not self.order.entry_price:
            return self.pnl

        if self.order.is_buy_order:
            # For buy orders, profit when current price > entry price
            current_price = self.exit_price or self.order.price
            return (current_price - self.order.entry_price) * self.order.quantity
        else:
            # For sell orders, profit when current price < entry price
            current_price = self.exit_price or self.order.price
            return (self.order.entry_price - current_price) * self.order.quantity

    def close_position(self, exit_price: float, exit_reason: ExitReason, exit_order_id: str = None):
        """Close the position"""
        self.is_open = False
        self.exit_time = datetime.now()
        self.exit_price = exit_price
        self.exit_reason = exit_reason
        self.exit_order_id = exit_order_id
        self.pnl = self.current_pnl

    def to_dict(self) -> dict:
        """Convert position to dictionary"""
        return {
            'order': self.order.to_dict(),
            'entry_time': self.entry_time.isoformat(),
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'exit_price': self.exit_price,
            'exit_order_id': self.exit_order_id,
            'is_open': self.is_open,
            'pnl': self.pnl,
            'exit_reason': self.exit_reason.value
        }
