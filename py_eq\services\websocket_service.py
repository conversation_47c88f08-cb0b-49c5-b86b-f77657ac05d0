"""
Real-time WebSocket service for live price monitoring using SmartAPI
"""
import logging
import threading
import time
from typing import Dict, Callable, Optional, List
from datetime import datetime
import pytz

from SmartApi.smartWebSocketV2 import SmartWebSocketV2


class LivePriceWebSocket:
    """WebSocket service for real-time price monitoring"""

    def __init__(
        self,
        auth_token: str,
        api_key: str,
        username: str,
        feed_token: str,
        logger: logging.Logger,
        max_retry_attempt: int = 5
    ):
        self.auth_token = auth_token
        self.api_key = api_key
        self.username = username
        self.feed_token = feed_token
        self.logger = logger
        self.max_retry_attempt = max_retry_attempt
        
        # WebSocket instance
        self.sws: Optional[SmartWebSocketV2] = None
        
        # Price data storage
        self.live_prices: Dict[str, float] = {}  # symbol -> price
        self.price_callbacks: Dict[str, List[Callable]] = {}  # symbol -> list of callbacks
        
        # Subscription management
        self.subscribed_symbols: Dict[str, dict] = {}  # symbol -> token info
        self.is_connected = False
        self.connection_lock = threading.Lock()

        # Connection health monitoring
        self.last_heartbeat = None
        self.connection_failures = 0
        self.max_connection_failures = 3
        self.heartbeat_interval = 30  # seconds
        self.last_data_received = None
        self.auto_reconnect = True
        self.reconnect_delay = 5  # seconds

        # Timezone for IST
        self.ist_timezone = pytz.timezone('Asia/Kolkata')

    def initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.sws = SmartWebSocketV2(
                self.auth_token,
                self.api_key,
                self.username,
                self.feed_token,
                max_retry_attempt=self.max_retry_attempt
            )
            
            # Set up callbacks
            self.sws.on_open = self._on_open
            self.sws.on_data = self._on_data
            self.sws.on_error = self._on_error
            self.sws.on_close = self._on_close
            
            self.logger.info("WebSocket initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket: {e}")
            return False

    def connect(self):
        """Connect to WebSocket in a separate thread"""
        if not self.sws:
            if not self.initialize_websocket():
                return False
        
        try:
            # Start WebSocket connection in a separate thread
            self.connection_thread = threading.Thread(target=self.sws.connect, daemon=True)
            self.connection_thread.start()
            
            # Wait a bit for connection to establish
            time.sleep(2)
            
            return self.is_connected
            
        except Exception as e:
            self.logger.error(f"Error connecting to WebSocket: {e}")
            return False

    def disconnect(self):
        """Disconnect from WebSocket"""
        try:
            if self.sws:
                self.sws.close_connection()
            self.is_connected = False
            self.logger.info("WebSocket disconnected")
        except Exception as e:
            self.logger.error(f"Error disconnecting WebSocket: {e}")

    def subscribe_symbol(self, symbol: str, token: str, exchange_type: int = 1):
        """
        Subscribe to live price updates for a symbol
        
        Args:
            symbol: Stock symbol (e.g., 'HDFCBANK')
            token: Symbol token from SmartAPI
            exchange_type: 1 for NSE, 2 for NFO, etc.
        """
        if not self.is_connected:
            self.logger.warning(f"WebSocket not connected. Cannot subscribe to {symbol}")
            return False

        try:
            # Store subscription info
            self.subscribed_symbols[symbol] = {
                'token': token,
                'exchange_type': exchange_type
            }
            
            # Prepare token list for subscription
            token_list = [{
                "exchangeType": exchange_type,
                "tokens": [token]
            }]
            
            # Subscribe to LTP mode (mode=1)
            correlation_id = f"ws_{symbol}"
            mode = 1  # LTP mode
            action = 1  # Subscribe
            
            self.sws.subscribe(correlation_id, mode, token_list)
            
            self.logger.info(f"Subscribed to live prices for {symbol} (token: {token})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error subscribing to {symbol}: {e}")
            return False

    def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from live price updates for a symbol"""
        if symbol not in self.subscribed_symbols:
            self.logger.warning(f"Symbol {symbol} not subscribed")
            return False

        try:
            token_info = self.subscribed_symbols[symbol]
            
            # Prepare token list for unsubscription
            token_list = [{
                "exchangeType": token_info['exchange_type'],
                "tokens": [token_info['token']]
            }]
            
            # Unsubscribe
            correlation_id = f"ws_{symbol}"
            mode = 1  # LTP mode
            action = 2  # Unsubscribe
            
            self.sws.unsubscribe(correlation_id, mode, token_list)
            
            # Remove from tracking
            del self.subscribed_symbols[symbol]
            if symbol in self.live_prices:
                del self.live_prices[symbol]
            if symbol in self.price_callbacks:
                del self.price_callbacks[symbol]
            
            self.logger.info(f"Unsubscribed from live prices for {symbol}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error unsubscribing from {symbol}: {e}")
            return False

    def get_live_price(self, symbol: str) -> Optional[float]:
        """Get the latest live price for a symbol"""
        return self.live_prices.get(symbol)
        
    def get_last_price(self, symbol: str) -> Optional[float]:
        """Alias for get_live_price for compatibility with other services"""
        return self.get_live_price(symbol)

    def add_price_callback(self, symbol: str, callback: Callable[[str, float, datetime], None]):
        """
        Add a callback function to be called when price updates for a symbol
        
        Args:
            symbol: Stock symbol
            callback: Function to call with (symbol, price, timestamp)
        """
        if symbol not in self.price_callbacks:
            self.price_callbacks[symbol] = []
        self.price_callbacks[symbol].append(callback)

    def remove_price_callback(self, symbol: str, callback: Callable):
        """Remove a price callback for a symbol"""
        if symbol in self.price_callbacks:
            try:
                self.price_callbacks[symbol].remove(callback)
            except ValueError:
                pass

    def _on_open(self, wsapp):
        """WebSocket open callback"""
        with self.connection_lock:
            self.is_connected = True
            self.connection_failures = 0  # Reset failure count on successful connection
            self.last_heartbeat = time.time()
        self.logger.info("🔗 WebSocket connection opened")

    def _on_data(self, wsapp, message):
        """WebSocket data callback - processes incoming price data"""
        try:
            # Update last data received timestamp for health monitoring
            self.last_data_received = time.time()

            # Extract price data from message
            token = message.get('token')
            last_traded_price = message.get('last_traded_price', 0) / 100  # Convert from paise to rupees
            exchange_timestamp = message.get('exchange_timestamp', 0)
            
            # Convert timestamp to IST
            timestamp = datetime.fromtimestamp(
                exchange_timestamp / 1000, 
                self.ist_timezone
            )
            
            # Find symbol by token
            symbol = None
            for sym, info in self.subscribed_symbols.items():
                if info['token'] == token:
                    symbol = sym
                    break
            
            if symbol:
                # Update live price
                self.live_prices[symbol] = last_traded_price
                
                # Log price update (less frequently to avoid spam)
                if not hasattr(self, '_last_log_time'):
                    self._last_log_time = {}
                
                current_time = time.time()
                if symbol not in self._last_log_time or (current_time - self._last_log_time[symbol]) > 5:
                    self.logger.debug(f"💰 {symbol}: ₹{last_traded_price:.2f} at {timestamp.strftime('%H:%M:%S')}")
                    self._last_log_time[symbol] = current_time
                
                # Call registered callbacks
                if symbol in self.price_callbacks:
                    for callback in self.price_callbacks[symbol]:
                        try:
                            callback(symbol, last_traded_price, timestamp)
                        except Exception as e:
                            self.logger.error(f"Error in price callback for {symbol}: {e}")
            
        except Exception as e:
            self.logger.error(f"Error processing WebSocket data: {e}")

    def _on_error(self, wsapp, error):
        """WebSocket error callback"""
        self.connection_failures += 1
        self.logger.error(f"WebSocket error (failure #{self.connection_failures}): {error}")

        # Attempt auto-reconnect if enabled and not exceeded max failures
        if (self.auto_reconnect and
            self.connection_failures <= self.max_connection_failures):
            self.logger.info(f"Attempting auto-reconnect in {self.reconnect_delay}s...")
            threading.Timer(self.reconnect_delay, self._attempt_reconnect).start()

    def _on_close(self, wsapp):
        """WebSocket close callback"""
        with self.connection_lock:
            self.is_connected = False
        self.logger.warning("🔌 WebSocket connection closed")

        # Attempt auto-reconnect if enabled
        if (self.auto_reconnect and
            self.connection_failures <= self.max_connection_failures):
            self.logger.info(f"Connection closed, attempting reconnect in {self.reconnect_delay}s...")
            threading.Timer(self.reconnect_delay, self._attempt_reconnect).start()

    def _attempt_reconnect(self):
        """Attempt to reconnect WebSocket"""
        try:
            self.logger.info("🔄 Attempting WebSocket reconnection...")
            if self.connect():
                self.logger.info("✅ WebSocket reconnection successful")
                # Re-subscribe to all previously subscribed symbols
                self._resubscribe_all_symbols()
            else:
                self.logger.error("❌ WebSocket reconnection failed")
        except Exception as e:
            self.logger.error(f"Error during reconnection attempt: {e}")

    def _resubscribe_all_symbols(self):
        """Re-subscribe to all previously subscribed symbols after reconnection"""
        if not self.subscribed_symbols:
            return

        self.logger.info(f"🔄 Re-subscribing to {len(self.subscribed_symbols)} symbols...")
        resubscribe_count = 0

        # Store current subscriptions to avoid modification during iteration
        symbols_to_resubscribe = dict(self.subscribed_symbols)

        for symbol, info in symbols_to_resubscribe.items():
            try:
                if self.subscribe_symbol(symbol, info['token'], info['exchange_type']):
                    resubscribe_count += 1
            except Exception as e:
                self.logger.error(f"Failed to re-subscribe to {symbol}: {e}")

        self.logger.info(f"✅ Re-subscribed to {resubscribe_count}/{len(symbols_to_resubscribe)} symbols")

    def get_connection_status(self) -> dict:
        """Get current connection status with health metrics"""
        current_time = time.time()

        # Calculate time since last data
        time_since_last_data = None
        if self.last_data_received:
            time_since_last_data = current_time - self.last_data_received

        # Calculate time since last heartbeat
        time_since_heartbeat = None
        if self.last_heartbeat:
            time_since_heartbeat = current_time - self.last_heartbeat

        # Determine connection health
        is_healthy = (
            self.is_connected and
            self.connection_failures < self.max_connection_failures and
            (time_since_last_data is None or time_since_last_data < 60)  # Data within last minute
        )

        return {
            'connected': self.is_connected,
            'healthy': is_healthy,
            'connection_failures': self.connection_failures,
            'max_failures': self.max_connection_failures,
            'time_since_last_data': time_since_last_data,
            'time_since_heartbeat': time_since_heartbeat,
            'subscribed_symbols': list(self.subscribed_symbols.keys()),
            'live_prices_count': len(self.live_prices),
            'callbacks_count': sum(len(callbacks) for callbacks in self.price_callbacks.values()),
            'auto_reconnect': self.auto_reconnect
        }

    def is_connection_healthy(self) -> bool:
        """Check if WebSocket connection is healthy"""
        status = self.get_connection_status()
        return status['healthy']

    def subscribe_multiple_symbols(self, symbols_info: List[dict]):
        """
        Subscribe to multiple symbols at once
        
        Args:
            symbols_info: List of dicts with 'symbol', 'token', 'exchange_type'
        """
        success_count = 0
        for info in symbols_info:
            if self.subscribe_symbol(
                info['symbol'], 
                info['token'], 
                info.get('exchange_type', 1)
            ):
                success_count += 1
        
        self.logger.info(f"Successfully subscribed to {success_count}/{len(symbols_info)} symbols")
        return success_count == len(symbols_info)
