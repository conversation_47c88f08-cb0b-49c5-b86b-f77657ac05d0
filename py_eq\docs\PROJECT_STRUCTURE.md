# py_eq Project Structure

This document describes the organized structure of the py_eq trading system.

## Directory Structure

```
py_eq/
├── main.py                    # Main entry point
├── requirements.txt           # Python dependencies
├── __init__.py               # Package initialization
│
├── config/                   # Configuration files
│   ├── config.py            # Main configuration
│   ├── enhanced_sl_config.py # Enhanced stop-loss config
│   └── telegram_config.py   # Telegram bot configuration
│
├── data/                     # Data files
│   ├── stocks_to_monitor.csv # Stocks watchlist
│   └── watchlist.csv        # Trading watchlist
│
├── docs/                     # Documentation
│   ├── README.md            # Main documentation
│   ├── IMPLEMENTATION_SUMMARY.md
│   ├── ENHANCED_SL_IMPLEMENTATION_SUMMARY.md
│   ├── REAL_TRADING_GUIDE.md
│   ├── TELEGRAM_BOT_README.md
│   ├── TELEGRAM_CONFIGURATION_COMPLETE.md
│   └── ENHANCED_STOP_LOSS.md
│
├── examples/                 # Example implementations
│   └── enhanced_sl_integration.py
│
├── logs/                     # Log files and directories
│   ├── trading_system.log   # Main log file
│   ├── performance_*.csv    # Performance logs
│   ├── signals_*.csv        # Signal logs
│   ├── trades_*.csv         # Trade logs
│   └── strategy_accounts/   # Strategy account logs
│
├── models/                   # Data models
│   ├── candle.py            # Candle data model
│   ├── order.py             # Order model
│   ├── signal.py            # Signal model
│   └── virtual_account.py   # Virtual account model
│
├── monitoring/               # Monitoring services
│   └── sl_performance_monitor.py
│
├── reports/                  # Generated reports
│   ├── daily/               # Daily reports
│   ├── weekly/              # Weekly reports
│   └── monthly/             # Monthly reports
│
├── samples/                  # Sample files
│   └── sample_daily_report.html
│
├── scripts/                  # Utility scripts
│   ├── README.md            # Scripts documentation
│   ├── telegram_bot_launcher.py # Bot launcher
│   ├── setup/               # Setup scripts
│   │   ├── setup.py
│   │   ├── setup_telegram_bot.py
│   │   └── quick_telegram_setup.py
│   └── debug/               # Debug scripts
│       ├── debug_auth.py
│       ├── debug_user_id.py
│       └── get_user_id.py
│
├── services/                 # Core services
│   ├── market_data_service.py
│   ├── order_service.py
│   ├── websocket_service.py
│   ├── telegram_bot_service.py
│   ├── mongodb_service.py
│   └── ... (other services)
│
├── strategies/               # Trading strategies
│   ├── strategy_manager.py
│   ├── ma_crossover_strategy.py
│   ├── support_resistance_strategy.py
│   ├── gap_and_go_strategy.py
│   ├── orb_strategy.py
│   └── ... (other strategies)
│
├── tests/                    # Test files
│   ├── test_enhanced_sl.py
│   ├── test_bot_simple.py
│   ├── test_fixes.py
│   ├── test_smartapi_connection.py
│   └── test_telegram_simple.py
│
└── utils/                    # Utility functions
    ├── helpers.py
    ├── safe_logging.py
    └── telegram_helpers.py
```

## Key Changes Made

1. **Documentation centralized** in `docs/` folder
2. **Scripts organized** by purpose in `scripts/setup/` and `scripts/debug/`
3. **Test files consolidated** in `tests/` directory
4. **Sample files** moved to `samples/` directory
5. **Virtual account model** moved to `models/` directory
6. **Clean root directory** with only essential files

## Usage

- Run the main system: `python main.py`
- Setup scripts: `python scripts/setup/setup.py`
- Debug utilities: `python scripts/debug/debug_auth.py`
- Launch Telegram bot: `python scripts/telegram_bot_launcher.py`

This organization follows Python project best practices and makes the codebase more maintainable.
