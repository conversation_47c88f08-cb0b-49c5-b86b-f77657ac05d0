#!/usr/bin/env python3
"""
Quick Telegram Bot Setup

This script provides a quick setup for the Telegram bot with minimal configuration.
Perfect for getting started quickly.
"""

import os
import sys

def quick_setup():
    """Quick setup with minimal questions"""
    print("🚀 QUICK TELEGRAM BOT SETUP")
    print("=" * 50)
    print("This will set up your Telegram bot in 3 simple steps!")
    print()
    
    # Step 1: Bot Token
    print("📱 STEP 1: Bot Token")
    print("Get your bot token from @BotFather on Telegram")
    bot_token = input("Enter your bot token: ").strip()
    
    if not bot_token or ':' not in bot_token:
        print("❌ Invalid bot token. Please get a valid token from @BotFather")
        return False
    
    # Step 2: Chat ID
    print("\n💬 STEP 2: Your Chat ID")
    print("Get your chat ID from @userinfobot on Telegram")
    chat_id = input("Enter your chat ID: ").strip()
    
    if not chat_id or not chat_id.isdigit():
        print("❌ Invalid chat ID. Please get your chat ID from @userinfobot")
        return False
    
    # Step 3: Create environment file
    print("\n⚙️ STEP 3: Creating configuration...")
    
    env_content = f"""# Telegram Bot Configuration - Quick Setup
TELEGRAM_BOT_TOKEN={bot_token}
TELEGRAM_CHAT_ID={chat_id}
TELEGRAM_AUTHORIZED_USERS={chat_id}
TELEGRAM_ADMIN_USERS={chat_id}
TELEGRAM_DAILY_REPORTS=true
TELEGRAM_WEEKLY_REPORTS=true
TELEGRAM_MONTHLY_REPORTS=true
TELEGRAM_TRADE_ALERTS=true
TELEGRAM_BALANCE_UPDATES=true
"""
    
    try:
        env_file_path = os.path.join(os.path.dirname(__file__), '.env.telegram')
        with open(env_file_path, 'w') as f:
            f.write(env_content)
        
        print(f"✅ Configuration saved to: {env_file_path}")
        
        # Set environment variables for current session
        os.environ['TELEGRAM_BOT_TOKEN'] = bot_token
        os.environ['TELEGRAM_CHAT_ID'] = chat_id
        os.environ['TELEGRAM_AUTHORIZED_USERS'] = chat_id
        os.environ['TELEGRAM_ADMIN_USERS'] = chat_id
        os.environ['TELEGRAM_DAILY_REPORTS'] = 'true'
        os.environ['TELEGRAM_WEEKLY_REPORTS'] = 'true'
        os.environ['TELEGRAM_MONTHLY_REPORTS'] = 'true'
        os.environ['TELEGRAM_TRADE_ALERTS'] = 'true'
        os.environ['TELEGRAM_BALANCE_UPDATES'] = 'true'
        
        print("✅ Environment variables set for current session")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        return False

def test_setup():
    """Test the bot setup"""
    print("\n🧪 TESTING BOT CONNECTION...")
    
    try:
        from telegram_bot_launcher import test_bot_connection
        if test_bot_connection():
            print("✅ Bot test successful!")
            return True
        else:
            print("❌ Bot test failed!")
            return False
    except Exception as e:
        print(f"❌ Error testing bot: {e}")
        return False

def show_next_steps():
    """Show what to do next"""
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 50)
    print("Your Telegram bot is now configured and ready!")
    print()
    print("📋 WHAT'S NEXT:")
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print()
    print("2. Start the bot:")
    print("   python telegram_bot_launcher.py")
    print()
    print("3. Or run with trading system:")
    print("   python main.py")
    print()
    print("📱 BOT FEATURES:")
    print("• Real-time order placement notifications")
    print("• Daily/Weekly/Monthly trading reports")
    print("• Interactive commands (/start, /status, /daily)")
    print("• Beautiful charts and analytics")
    print("• Balance and risk alerts")
    print()
    print("🔔 NOTIFICATIONS ENABLED:")
    print("• ✅ Order placement alerts")
    print("• ✅ Position closure alerts")
    print("• ✅ Daily summary reports")
    print("• ✅ System startup/shutdown")
    print("• ✅ Balance threshold alerts")
    print()
    print("Happy trading! 📈🤖")

def main():
    """Main setup function"""
    try:
        # Quick setup
        if not quick_setup():
            print("❌ Setup failed. Please try again.")
            return 1
        
        # Test connection
        if not test_setup():
            print("⚠️ Setup completed but test failed.")
            print("Please check your bot token and chat ID.")
            return 1
        
        # Show next steps
        show_next_steps()
        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 Setup cancelled by user")
        return 1
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
