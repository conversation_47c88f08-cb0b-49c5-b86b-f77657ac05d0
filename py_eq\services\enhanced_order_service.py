"""
Enhanced Order Service with optimized stop-loss execution
Provides immediate square-off capabilities with minimal latency
"""
import logging
import time
from datetime import datetime
from typing import Optional, Dict, List
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from models.order import Order, TransactionType
from services.order_service import SmartAPIOrderService, OrderServiceInterface


@dataclass
class OrderExecutionConfig:
    """Configuration for optimized order execution"""
    max_concurrent_orders: int = 5
    order_timeout_seconds: int = 10
    retry_attempts: int = 3
    retry_delay_ms: int = 100
    pre_auth_interval_minutes: int = 30
    connection_pool_size: int = 3


class EnhancedSmartAPIOrderService(SmartAPIOrderService):
    """Enhanced SmartAPI order service with optimized execution for stop-loss orders"""
    
    def __init__(self, api_key: str, client_id: str, client_pin: str, totp_key: str, 
                 logger: logging.Logger, config: Optional[OrderExecutionConfig] = None):
        super().__init__(api_key, client_id, client_pin, totp_key, logger)
        
        self.config = config or OrderExecutionConfig()
        self.executor = ThreadPoolExecutor(max_workers=self.config.max_concurrent_orders)
        
        # Connection management
        self._auth_lock = threading.Lock()
        self._last_auth_time = None
        self._auth_valid_duration = 3600  # 1 hour in seconds
        
        # Performance tracking
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'average_execution_time': 0.0,
            'fastest_execution': float('inf'),
            'slowest_execution': 0.0
        }
        
        # Pre-authentication timer
        self._start_auth_maintenance()
        
        self.logger.info("🚀 Enhanced SmartAPI Order Service initialized with optimized execution")

    def _start_auth_maintenance(self):
        """Start background authentication maintenance"""
        def maintain_auth():
            while True:
                try:
                    time.sleep(self.config.pre_auth_interval_minutes * 60)
                    if self._should_refresh_auth():
                        self._refresh_authentication()
                except Exception as e:
                    self.logger.error(f"Error in auth maintenance: {e}")
        
        auth_thread = threading.Thread(target=maintain_auth, daemon=True)
        auth_thread.start()

    def _should_refresh_auth(self) -> bool:
        """Check if authentication should be refreshed"""
        if not self._last_auth_time:
            return True
        
        time_since_auth = time.time() - self._last_auth_time
        return time_since_auth > (self._auth_valid_duration * 0.8)  # Refresh at 80% of validity

    def _refresh_authentication(self):
        """Refresh authentication proactively"""
        with self._auth_lock:
            try:
                self.logger.info("🔐 Proactively refreshing API authentication...")
                success = self.authenticate()
                if success:
                    self._last_auth_time = time.time()
                    self.logger.info("✅ Authentication refreshed successfully")
                else:
                    self.logger.error("❌ Failed to refresh authentication")
            except Exception as e:
                self.logger.error(f"Error refreshing authentication: {e}")

    def authenticate(self) -> bool:
        """Enhanced authentication with connection management"""
        with self._auth_lock:
            success = super().authenticate()
            if success:
                self._last_auth_time = time.time()
            return success

    def square_off_position_optimized(self, order: Order, exit_price: float, exit_reason: str, 
                                    priority: str = "NORMAL") -> bool:
        """Optimized square-off with priority handling and performance tracking"""
        start_time = time.time()
        
        try:
            # Ensure authentication is valid
            if not self._ensure_valid_auth():
                self.logger.error("❌ Authentication failed before square-off")
                return False

            # Determine opposite transaction type
            square_off_type = "SELL" if order.transaction_type == TransactionType.BUY else "BUY"

            # Prepare optimized order parameters
            square_off_params = {
                "variety": "NORMAL",
                "tradingsymbol": f"{order.symbol}-EQ",
                "symboltoken": order.symbol_token,
                "transactiontype": square_off_type,
                "exchange": order.exchange,
                "ordertype": "MARKET",  # Always use market orders for immediate execution
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",  # Market order
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(order.quantity)
            }

            self.logger.info(f"⚡ OPTIMIZED Square-off: {order.symbol} {square_off_type} {order.quantity} "
                           f"@ Market (Priority: {priority})")

            # Execute with retry logic
            success = self._execute_with_retry(square_off_params, order.symbol, priority)
            
            # Track performance
            execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            self._update_execution_stats(execution_time, success)
            
            if success:
                self.logger.info(f"✅ OPTIMIZED Square-off completed in {execution_time:.1f}ms: {order.symbol}")
                self.logger.info(f"📊 Reason: {exit_reason}")
            else:
                self.logger.error(f"❌ OPTIMIZED Square-off failed after retries: {order.symbol}")
            
            return success

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self._update_execution_stats(execution_time, False)
            self.logger.error(f"Error in optimized square-off for {order.order_id}: {e}")
            return False

    def _ensure_valid_auth(self) -> bool:
        """Ensure authentication is valid, refresh if needed"""
        if not self._authenticated:
            return self.authenticate()
        
        # Check if auth is still valid
        if self._should_refresh_auth():
            return self.authenticate()
        
        return True

    def _execute_with_retry(self, order_params: Dict, symbol: str, priority: str) -> bool:
        """Execute order with intelligent retry logic"""
        max_attempts = self.config.retry_attempts
        
        for attempt in range(max_attempts):
            try:
                # Add jitter to prevent thundering herd
                if attempt > 0:
                    jitter = (attempt * self.config.retry_delay_ms) + (time.time() % 10)
                    time.sleep(jitter / 1000)
                
                # Execute the order
                response = self.smart_api.placeOrderFullResponse(order_params)
                
                if response.get('status') and response.get('data'):
                    order_id = response['data'].get('orderid')
                    self.logger.info(f"✅ Order executed successfully: {order_id} (attempt {attempt + 1})")
                    return True
                else:
                    error_msg = response.get('message', 'Unknown error')
                    self.logger.warning(f"⚠️ Order attempt {attempt + 1} failed for {symbol}: {error_msg}")
                    
                    # Check if error is retryable
                    if not self._is_retryable_error(error_msg):
                        self.logger.error(f"❌ Non-retryable error for {symbol}: {error_msg}")
                        break
                        
            except Exception as e:
                self.logger.warning(f"⚠️ Exception in attempt {attempt + 1} for {symbol}: {e}")
                
                # Re-authenticate on auth errors
                if "authentication" in str(e).lower() or "token" in str(e).lower():
                    self.logger.info("🔐 Re-authenticating due to auth error...")
                    self.authenticate()
        
        return False

    def _is_retryable_error(self, error_msg: str) -> bool:
        """Determine if an error is retryable"""
        non_retryable_errors = [
            "insufficient funds",
            "invalid symbol",
            "market closed",
            "position not found",
            "already squared off"
        ]
        
        error_lower = error_msg.lower()
        return not any(err in error_lower for err in non_retryable_errors)

    def _update_execution_stats(self, execution_time: float, success: bool):
        """Update execution performance statistics"""
        self.execution_stats['total_orders'] += 1
        
        if success:
            self.execution_stats['successful_orders'] += 1
            
            # Update timing stats
            current_avg = self.execution_stats['average_execution_time']
            successful_count = self.execution_stats['successful_orders']
            self.execution_stats['average_execution_time'] = (
                (current_avg * (successful_count - 1) + execution_time) / successful_count
            )
            
            self.execution_stats['fastest_execution'] = min(
                self.execution_stats['fastest_execution'], execution_time
            )
            self.execution_stats['slowest_execution'] = max(
                self.execution_stats['slowest_execution'], execution_time
            )
        else:
            self.execution_stats['failed_orders'] += 1

    def square_off_position(self, order: Order, exit_price: float, exit_reason: str) -> bool:
        """Override base method to use optimized execution"""
        priority = "HIGH" if exit_reason in ["STOP_LOSS", "EMERGENCY_EXIT"] else "NORMAL"
        return self.square_off_position_optimized(order, exit_price, exit_reason, priority)

    def square_off_multiple_positions(self, orders: List[Order], exit_prices: List[float], 
                                    exit_reasons: List[str]) -> Dict[str, bool]:
        """Execute multiple square-offs in parallel for emergency situations"""
        if len(orders) != len(exit_prices) or len(orders) != len(exit_reasons):
            raise ValueError("Orders, exit_prices, and exit_reasons must have same length")
        
        self.logger.info(f"🚀 Executing {len(orders)} parallel square-offs...")
        
        # Submit all orders to thread pool
        future_to_symbol = {}
        for order, exit_price, exit_reason in zip(orders, exit_prices, exit_reasons):
            future = self.executor.submit(
                self.square_off_position_optimized, order, exit_price, exit_reason, "EMERGENCY"
            )
            future_to_symbol[future] = order.symbol
        
        # Collect results
        results = {}
        for future in as_completed(future_to_symbol, timeout=self.config.order_timeout_seconds):
            symbol = future_to_symbol[future]
            try:
                results[symbol] = future.result()
            except Exception as e:
                self.logger.error(f"Error in parallel square-off for {symbol}: {e}")
                results[symbol] = False
        
        successful = sum(results.values())
        self.logger.info(f"✅ Parallel square-off completed: {successful}/{len(orders)} successful")
        
        return results

    def get_execution_performance(self) -> Dict:
        """Get execution performance metrics"""
        stats = self.execution_stats.copy()
        if stats['total_orders'] > 0:
            stats['success_rate_percent'] = (stats['successful_orders'] / stats['total_orders']) * 100
        else:
            stats['success_rate_percent'] = 0.0
        
        return stats

    def emergency_square_off_all(self) -> bool:
        """Emergency square-off all positions with maximum priority"""
        try:
            self.logger.error("🚨 EMERGENCY SQUARE-OFF ALL POSITIONS INITIATED")
            
            # Get all open positions
            open_positions = self.get_open_positions()
            if not open_positions:
                self.logger.info("No open positions to square off")
                return True
            
            # Prepare for parallel execution
            exit_prices = []
            exit_reasons = []
            
            for position in open_positions:
                # Use current market price or entry price as fallback
                try:
                    current_price = self.market_data_service.get_last_price(position.symbol)
                    exit_prices.append(current_price or position.entry_price)
                except:
                    exit_prices.append(position.entry_price)
                exit_reasons.append("EMERGENCY_SQUARE_OFF")
            
            # Execute all square-offs in parallel
            results = self.square_off_multiple_positions(open_positions, exit_prices, exit_reasons)
            
            successful_count = sum(results.values())
            total_count = len(results)
            
            if successful_count == total_count:
                self.logger.info(f"✅ Emergency square-off completed: All {total_count} positions closed")
                return True
            else:
                self.logger.error(f"⚠️ Emergency square-off partial success: {successful_count}/{total_count} positions closed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in emergency square-off: {e}")
            return False
