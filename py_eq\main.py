"""
Main execution script for the Python trading system
"""
import asyncio
import logging
import sys
import os
import csv
from datetime import datetime
from typing import List, Dict, Set

# Add the current directory to the path so we can import our modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from models.signal import Signal, SignalType
from services.order_service import SmartAPIOrderService
from services.market_data_service import SimulatedMarketDataService, RealMarketDataService
from services.hybrid_market_data_service import HybridMarketDataService
from services.efficient_market_data_service import EfficientMarketDataService
from services.background_data_updater import BackgroundDataUpdater
from services.trading_logger import TradingLogger
from services.stock_monitor import StockMonitor
from services.position_monitor import PositionMonitor
from services.advanced_position_monitor import AdvancedPositionMonitor, TrailingStopLossConfig
from services.stock_symbol_integration_service import StockSymbolIntegrationService
from services.daily_trade_tracker import DailyTradeTracker, TradeStatus
from services.order_state_manager import OrderStateManager
from services.startup_validator import StartupValidator
# ActivePositionMonitor removed - handled by ProductionStrategyManager
from services.websocket_service import LivePriceWebSocket
from strategies.production_strategy_manager import ProductionStrategyManager, StrategyType
from strategies.production_async_strategy_manager import ProductionAsyncStrategyManager, ProductionAsyncMarketDataProcessor
from config.config import config
from utils.helpers import is_market_open, is_in_entry_time_window
from services.report_generator import TradingReportGenerator
from scripts.telegram_bot_launcher import run_bot_in_background

# Import Telegram notifier for system notifications
try:
    from services.telegram_notifier import notify_system_status, notify_daily_summary, is_telegram_available
except ImportError:
    def notify_system_status(*args, **kwargs):
        pass
    def notify_daily_summary(*args, **kwargs):
        pass
    def is_telegram_available():
        return False


# GlobalTradeManager removed - functionality moved to ProductionStrategyManager


class SafeFormatter(logging.Formatter):
    """Custom formatter that handles Unicode characters safely for console output"""

    # Emoji to text mapping for console output
    EMOJI_MAP = {
        '🛑': '[STOP]',
        '⏹️': '[STOP]',
        '🏁': '[FINISH]',
        '📊': '[CHART]',
        '🚀': '[START]',
        '🔄': '[UPDATE]',
        '🎯': '[TARGET]',
        '📈': '[UP]',
        '🕐': '[TIME]',
        '✅': '[OK]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '📡': '[SIGNAL]',
        '📋': '[LIST]',
        '💰': '[MONEY]',
        '🔍': '[SEARCH]',
        '⭐': '[STAR]',
        '🔥': '[HOT]',
        '💡': '[IDEA]',
        '🎉': '[CELEBRATE]',
        '🚨': '[ALERT]',
        '📞': '[CALL]',
        '📧': '[EMAIL]',
        '🌟': '[STAR]',
        '🎪': '[EVENT]',
        '🎭': '[MASK]',
        '🎨': '[ART]',
        '🎵': '[MUSIC]',
        '🎬': '[MOVIE]',
        '🎮': '[GAME]',
        '🎲': '[DICE]',
        '🎯': '[TARGET]',
        '🎪': '[CIRCUS]'
    }

    def __init__(self, fmt=None, datefmt=None, for_console=False):
        super().__init__(fmt, datefmt)
        self.for_console = for_console

    def format(self, record):
        # Get the formatted message
        formatted = super().format(record)

        # If this is for console output, replace emoji with text
        if self.for_console:
            for emoji, text in self.EMOJI_MAP.items():
                formatted = formatted.replace(emoji, text)

        return formatted


def setup_logging() -> logging.Logger:
    """Setup logging configuration with Unicode support"""
    logger = logging.getLogger('TradingSystem')
    logger.setLevel(getattr(logging, config.logging.log_level.upper()))

    # Create formatters
    console_formatter = SafeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        for_console=True
    )
    file_formatter = SafeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        for_console=False
    )

    # Console handler with safe formatting
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # File handler with UTF-8 encoding (if specified)
    if config.logging.log_file:
        try:
            file_handler = logging.FileHandler(
                config.logging.log_file,
                encoding='utf-8',
                errors='replace'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            # Fallback to console logging if file handler fails
            logger.warning(f"Could not create file handler: {e}")

    return logger


def create_services(logger: logging.Logger):
    """Create and initialize services"""

    # Create market data service
    market_data_service_type = os.getenv('MARKET_DATA_SERVICE', 'efficient').lower()

    # Use efficient mode for optimized data fetching
    market_data_service_type = 'efficient'

    # Initialize WebSocket service variables
    websocket_service = None
    auth_token = None
    feed_token = None

    if market_data_service_type == 'efficient':
        # Use efficient service (MongoDB caching + live updates)
        if not config.smartapi.validate():
            logger.warning("SmartAPI configuration not available - using YFinance fallback")
            smart_api = None
        else:
            from SmartApi import SmartConnect
            import pyotp

            smart_api = SmartConnect(api_key=config.smartapi.api_key)

            # Authenticate SmartAPI for live data access
            try:
                # Generate TOTP
                totp = pyotp.TOTP(config.smartapi.totp_token).now()

                # Generate session
                data = smart_api.generateSession(
                    config.smartapi.username,
                    config.smartapi.password,
                    totp
                )

                if data and data.get('status'):
                    logger.info("✅ SmartAPI authentication successful - Live data enabled")
                    auth_token = data['data']['jwtToken']
                    refresh_token = data['data']['refreshToken']
                    feed_token = smart_api.getfeedToken()
                    logger.info(f"Feed Token: {feed_token[:10]}...")

                    # Create WebSocket service for real-time price monitoring
                    websocket_service = LivePriceWebSocket(
                        auth_token=auth_token,
                        api_key=config.smartapi.api_key,
                        username=config.smartapi.username,
                        feed_token=feed_token,
                        logger=logger
                    )

                    # Connect to WebSocket
                    if websocket_service.connect():
                        logger.info("🔗 WebSocket service connected successfully")
                    else:
                        logger.warning("⚠️ WebSocket connection failed - falling back to polling")
                        websocket_service = None

                else:
                    logger.warning("⚠️ SmartAPI authentication failed - Using YFinance fallback")
                    smart_api = None

            except Exception as auth_error:
                logger.warning(f"⚠️ SmartAPI authentication error: {auth_error}")
                logger.warning("Continuing with YFinance fallback for live data")
                smart_api = None

        market_data_service = EfficientMarketDataService(smart_api=smart_api, logger=logger, websocket_service=websocket_service)
        logger.info("🚀 Using EFFICIENT market data service (MongoDB caching + live updates)")

    elif market_data_service_type == 'hybrid':
        # Use hybrid service (MongoDB + SmartAPI)
        if not config.smartapi.validate():
            logger.error("SmartAPI configuration required for hybrid market data service")
            sys.exit(1)

        from SmartApi import SmartConnect
        import pyotp

        smart_api = SmartConnect(api_key=config.smartapi.api_key)

        # Authenticate SmartAPI for live data access
        try:
            # Generate TOTP
            totp = pyotp.TOTP(config.smartapi.totp_token).now()

            # Generate session
            data = smart_api.generateSession(
                config.smartapi.username,
                config.smartapi.password,
                totp
            )

            if data and data.get('status'):
                logger.info("✅ SmartAPI authentication successful - Live data enabled")
                auth_token = data['data']['jwtToken']
                refresh_token = data['data']['refreshToken']
                feed_token = smart_api.getfeedToken()
                logger.info(f"Feed Token: {feed_token[:10]}...")

                # Create WebSocket service for real-time price monitoring
                websocket_service = LivePriceWebSocket(
                    auth_token=auth_token,
                    api_key=config.smartapi.api_key,
                    username=config.smartapi.username,
                    feed_token=feed_token,
                    logger=logger
                )

                # Connect to WebSocket
                if websocket_service.connect():
                    logger.info("🔗 WebSocket service connected successfully")
                else:
                    logger.warning("⚠️ WebSocket connection failed - falling back to polling")
                    websocket_service = None

            else:
                logger.warning("⚠️ SmartAPI authentication failed - Using fallback data sources")
                logger.warning(f"Auth response: {data}")

        except Exception as auth_error:
            logger.warning(f"⚠️ SmartAPI authentication error: {auth_error}")
            logger.warning("Continuing with YFinance fallback for live data")

        market_data_service = HybridMarketDataService(
            smart_api=smart_api,
            mongodb_config=config.mongodb,
            logger=logger
        )
        logger.info("Using hybrid market data service (MongoDB + SmartAPI)")

    elif market_data_service_type == 'real':
        # Use real SmartAPI service only
        if not config.smartapi.validate():
            logger.error("SmartAPI configuration required for real market data service")
            sys.exit(1)

        from SmartApi import SmartConnect
        smart_api = SmartConnect(api_key=config.smartapi.api_key)

        market_data_service = RealMarketDataService(smart_api, logger)
        logger.info("Using real SmartAPI market data service")

    else:
        # Use simulated service (default)
        market_data_service = SimulatedMarketDataService(logger)
        logger.info("Using simulated market data service")

    # Create order service - Production mode only uses SmartAPI
    if not config.smartapi.validate():
        logger.error("❌ SmartAPI configuration is incomplete. Please check your .env file.")
        logger.error("💡 Required for production trading:")
        logger.error("   - SMARTAPI_API_KEY")
        logger.error("   - SMARTAPI_USERNAME")
        logger.error("   - SMARTAPI_PASSWORD")
        logger.error("   - SMARTAPI_TOTP_TOKEN")
        sys.exit(1)

    order_service = SmartAPIOrderService(config.smartapi, logger)
    logger.info("🏭 Using SmartAPI order service for REAL TRADING")

    return market_data_service, order_service, websocket_service


def resume_trading_state(logger: logging.Logger, order_service: SmartAPIOrderService, log_directory: str = "logs") -> Dict:
    """
    Resume trading state from existing log files and fetch open positions from the broker.

    Args:
        logger: Logger instance
        order_service: Order service to fetch real positions
        log_directory: Directory where trade logs are stored

    Returns:
        Dict containing:
        - traded_stocks: Set of symbols already traded today
        - trades_count: Number of trades taken today
        - open_positions: List of open positions from broker
        - closed_positions: Set of symbols for positions closed today
    """
    today = datetime.now().strftime('%Y-%m-%d')
    trades_file = os.path.join(log_directory, f"trades_{today}.csv")

    traded_stocks = set()
    closed_positions = set()
    trades_count = 0

    # 1. Read trade logs to identify stocks traded and closed today
    if os.path.exists(trades_file):
        try:
            logger.info(f"📄 Reading trade log for today: {trades_file}")
            with open(trades_file, 'r', newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    symbol = row.get('symbol')
                    action = row.get('action')

                    if symbol:
                        traded_stocks.add(symbol)

                    if action == 'ORDER_PLACED':
                        trades_count += 1
                    elif action == 'POSITION_CLOSED':
                        closed_positions.add(symbol)
        except Exception as e:
            logger.error(f"❌ Error reading trade log: {e}")

    # 2. Fetch real open positions from the broker
    try:
        logger.info("📡 Fetching real open positions from broker...")
        open_positions = order_service.get_open_positions()

        # CRITICAL FIX: Count open positions as trades if they're not already counted in logs
        # This prevents the system from placing more trades when it already has open positions
        for pos in open_positions:
            traded_stocks.add(pos.symbol)

            # If this symbol's trade wasn't counted in the log, count it now
            # This handles cases where positions exist but weren't logged properly
            symbol_already_logged = False
            if os.path.exists(trades_file):
                try:
                    with open(trades_file, 'r', newline='', encoding='utf-8') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            if row.get('symbol') == pos.symbol and row.get('action') == 'ORDER_PLACED':
                                symbol_already_logged = True
                                break
                except Exception:
                    pass  # If we can't read the log, err on the side of caution

            if not symbol_already_logged:
                trades_count += 1
                logger.warning(f"⚠️ Found open position for {pos.symbol} not in trade log - counting as trade")
                logger.warning(f"📊 Adjusted trade count: {trades_count}")

    except Exception as e:
        logger.error(f"❌ Error fetching open positions from broker: {e}")
        open_positions = []

    # 3. Construct the resume state
    resume_state = {
        'traded_stocks': traded_stocks,
        'trades_count': trades_count,
        'open_positions': open_positions,
        'closed_positions': closed_positions
    }

    # 4. Log the resume summary
    logger.info("=" * 50)
    logger.info("🔄 RESUMED TRADING SESSION")
    logger.info("=" * 50)
    logger.info(f"📊 Total trades today (from logs): {trades_count}")
    logger.info(f"✅ Symbols for closed positions today: {len(closed_positions)}")
    if closed_positions:
        logger.info(f"   - Symbols: {', '.join(sorted(closed_positions))}")
    
    logger.info(f"🔓 Real open positions from broker: {len(open_positions)}")
    if open_positions:
        logger.info("   Open Positions:")
        for pos in open_positions:
            logger.info(f"   - {pos.symbol}: {pos.quantity} @ ₹{pos.price:.2f} (SL: ₹{pos.stop_loss:.2f}, Target: ₹{pos.target:.2f})")
            
    logger.info(f"📈 Total unique symbols traded today (including open positions): {len(traded_stocks)}")
    if traded_stocks:
        logger.info(f"   - Symbols: {', '.join(sorted(traded_stocks))}")
    logger.info("=" * 50)

    return resume_state


def load_monitored_stocks_only(logger: logging.Logger) -> List[Signal]:
    """Load signals ONLY from stocks_to_monitor.csv with strategy-specific assignment"""
    signals = []

    try:
        # Initialize stock symbol integration service
        from services.stock_symbol_integration_service import StockSymbolIntegrationService
        symbol_service = StockSymbolIntegrationService(data_directory="data", logger=logger)

        # Load monitored stocks and create signals based on their assigned strategy
        monitored_stocks = symbol_service.get_monitored_stocks(enabled_only=True)

        # Strategy mapping for production trading
        strategy_mapping = {
            'MA_CROSSOVER': 'MA_CROSSOVER',
            'SUPPORT_RESISTANCE': 'SUPPORT_RESISTANCE',
            'GAPANDGO': 'GAP_AND_GO',
            'ORB': 'ORB',
            'ALL': ['MA_CROSSOVER', 'SUPPORT_RESISTANCE', 'GAP_AND_GO', 'ORB']
        }

        for stock in monitored_stocks:
            # Create only one signal per stock for monitoring (signal type will be determined by strategy analysis)
            # This prevents creating both BUY and SELL signals that cause duplicate orders
            signal = Signal(
                symbol=stock.symbol,
                signal_type=SignalType.BUY,  # Default type, actual type determined by strategy analysis
                timeframe=stock.timeframe
            )
            # Add strategy metadata for enhanced strategy manager
            signal.preferred_strategy = stock.strategy

            signals.append(signal)

        # Log stock statistics
        stats = symbol_service.get_stock_statistics()
        logger.info(f"📊 Stock Monitor Stats: {stats}")
        logger.info(f"📋 Loaded {len(monitored_stocks)} unique stocks from stocks_to_monitor.csv")
        logger.info(f"🎯 Created {len(signals)} signals (one per stock for monitoring)")

        # Log MongoDB connection status
        mongo_status = symbol_service.get_connection_status()
        if mongo_status.get('mongodb_connected', False):
            logger.info(f"✅ MongoDB connected for symbol mappings")
            if 'collections' in mongo_status:
                logger.info(f"📊 MongoDB collections: {mongo_status['collections']}")
        else:
            logger.warning(f"⚠️ MongoDB not connected for symbol mappings")

        # Log strategy distribution
        strategy_counts = {}
        for stock in monitored_stocks:
            strategy_counts[stock.strategy] = strategy_counts.get(stock.strategy, 0) + 1

        logger.info("📈 Strategy Distribution:")
        for strategy, count in strategy_counts.items():
            mapped_strategy = strategy_mapping.get(strategy, 'Unknown')
            if isinstance(mapped_strategy, list):
                strategy_str = ', '.join(mapped_strategy)
            else:
                strategy_str = mapped_strategy
            logger.info(f"   {strategy}: {count} stocks → {strategy_str}")

        return signals

    except Exception as e:
        logger.error(f"Error loading monitored stocks: {e}")
        return []





def check_available_balance_and_calculate_risk(logger: logging.Logger, config, order_service=None) -> tuple:
    """
    Check available balance in the morning and calculate 1% risk per trade for the whole day

    Args:
        logger: Logger instance
        config: Configuration object
        order_service: Order service (SmartAPI for real trading, None for paper trading)

    Returns:
        tuple: (total_available_balance, daily_risk_per_trade, margin_multiplier)
    """

    # Production mode - always use real trading
    if False:  # Remove paper trading path
        pass
    else:
        # Production mode: Get actual balance from SmartAPI
        balance_source = "REAL ACCOUNT"

        logger.info("💰 MORNING BALANCE CHECK & RISK CALCULATION (PRODUCTION TRADING)")
        logger.info("=" * 70)
        logger.info(f"🏭 Trading Mode: PRODUCTION TRADING (Real Money)")
        logger.warning("⚠️  WARNING: This will use REAL MONEY from your Angel One account!")

        if order_service and hasattr(order_service, 'get_real_balance'):
            # Get real balance from SmartAPI
            real_balance = order_service.get_real_balance()

            if real_balance:
                # Use available margin for intraday trading, fallback to cash if margin is zero
                available_margin = real_balance.get('available_margin', 0)
                available_cash = real_balance.get('available_cash', 0)

                # If margin is zero but cash is available, use cash (common in new accounts)
                if available_margin == 0 and available_cash > 0:
                    total_available_balance = available_cash
                    logger.warning("⚠️  Available Margin is ₹0.00 - using Available Cash instead")
                    logger.warning("💡 Consider enabling intraday trading in your Angel One account for better margin")
                else:
                    total_available_balance = available_margin

                logger.info(f"💵 Available Cash: ₹{available_cash:,.2f}")
                logger.info(f"💳 Available Margin: ₹{available_margin:,.2f}")
                logger.info(f"🏛️  Collateral: ₹{real_balance.get('collateral', 0):,.2f}")
                logger.info(f"📊 Margin Used: ₹{real_balance.get('margin_used', 0):,.2f}")
                logger.info(f"🎯 Using for Trading: ₹{total_available_balance:,.2f}")

                # Check if we have any balance available for trading
                if total_available_balance <= 0:
                    logger.error(f"❌ NO BALANCE AVAILABLE: Available balance ₹{total_available_balance:,.2f}")
                    logger.error("💡 Please add funds to your Angel One account or switch to paper trading")
                    raise ValueError("No balance available for trading")

                # Log balance status
                logger.info(f"✅ BALANCE AVAILABLE: ₹{total_available_balance:,.2f} ready for trading")
                if total_available_balance < 5000:
                    logger.warning(f"⚠️ LOW BALANCE: ₹{total_available_balance:,.2f} - consider smaller position sizes")
                else:
                    logger.info(f"💪 GOOD BALANCE: ₹{total_available_balance:,.2f} - ready for trading")

            else:
                logger.error("❌ Failed to fetch real balance from SmartAPI")
                logger.error("💡 Cannot proceed without real balance in production mode")
                raise RuntimeError("Real balance required for production trading")
        else:
            logger.error("❌ SmartAPI order service not available for real balance check")
            logger.error("💡 Cannot proceed without SmartAPI in production mode")
            raise RuntimeError("SmartAPI service required for production trading")

    # Store raw balance before margin adjustment
    raw_available_balance = total_available_balance

    # Use available balance × 3.5 as requested (for margin calculation)
    margin_adjusted_balance = total_available_balance * 3.5
    margin_multiplier = 3.5

    # Calculate 1% of RAW available balance as risk per trade for whole day
    # This ensures risk is calculated on actual cash, not leveraged amount
    daily_risk_per_trade = raw_available_balance * 0.01  # 1% of raw available balance

    # Set total balance to margin-adjusted balance for order validation
    total_available_balance = margin_adjusted_balance

    # Calculate initial maximum order value (will change dynamically)
    initial_max_order_value = total_available_balance

    logger.info(f"📊 Balance Source: {balance_source}")
    logger.info(f"💰 Raw Available Balance: ₹{raw_available_balance:,.2f}")
    logger.info(f"📈 Intraday Margin (3.5x): ₹{total_available_balance:,.2f}")
    logger.info(f"🎯 Daily Risk Per Trade (1% of raw balance): ₹{daily_risk_per_trade:,.2f}")
    logger.info(f"💳 Max Order Value: ₹{initial_max_order_value:,.2f}")
    logger.info(f"🔢 Maximum Trades Today: 3 (across ALL strategies)")
    logger.info("⚠️  Position sizes will be auto-adjusted to fit within 3.5x margin limit")
    logger.info("=" * 70)

    return total_available_balance, daily_risk_per_trade, margin_multiplier


def check_and_wait_for_market_hours(logger: logging.Logger):
    """Check market hours and wait until 9:15 AM if needed"""
    from datetime import datetime, time
    import time as time_module

    current_time = datetime.now().time()
    market_open_time = time(9, 15)  # 9:15 AM
    market_close_time = time(15, 30)  # 3:30 PM

    # Check if it's a weekend
    current_day = datetime.now().weekday()
    if current_day >= 5:  # Saturday = 5, Sunday = 6
        logger.warning("⚠️ Market is closed (Weekend). Set OVERRIDE_MARKET_OPEN=true to continue anyway.")
        if os.getenv("OVERRIDE_MARKET_OPEN") != "true":
            sys.exit(0)
        return True

    # Check if market is closed
    if current_time < market_open_time:
        logger.info(f"🕐 Market opens at {market_open_time.strftime('%H:%M')}. Current time: {current_time.strftime('%H:%M')}")
        logger.info("🚀 System started in PRE-MARKET mode - downloading historical data...")
        return False  # Pre-market mode
    elif current_time > market_close_time:
        logger.warning(f"⚠️ Market is closed. Market hours: {market_open_time.strftime('%H:%M')} - {market_close_time.strftime('%H:%M')}")
        if os.getenv("OVERRIDE_MARKET_OPEN") != "true":
            sys.exit(0)
        return True
    else:
        logger.info(f"✅ Market is open! Current time: {current_time.strftime('%H:%M')}")
        return True


def preload_historical_data(logger: logging.Logger, market_data_service, signals: List[Signal]):
    """Preload historical data for all monitored symbols into cache using parallel processing"""
    import concurrent.futures
    import threading

    if not signals:
        logger.warning("No signals available for preloading data")
        return

    symbols = list(set([signal.symbol for signal in signals]))
    logger.info(f"📊 PRE-MARKET: Preloading historical data for {len(symbols)} symbols...")

    successful_loads = 0
    failed_loads = 0

    # Thread-safe counters
    success_lock = threading.Lock()
    fail_lock = threading.Lock()

    def load_symbol_data(symbol, timeframe):
        """Load data for a single symbol and timeframe"""
        nonlocal successful_loads, failed_loads
        try:
            candles = market_data_service.get_historical_data(symbol, timeframe, days=5)
            if candles:
                logger.info(f"✅ Preloaded {len(candles)} {timeframe} candles for {symbol}")
                with success_lock:
                    successful_loads += 1
                return True
            else:
                logger.warning(f"⚠️ Failed to preload {timeframe} data for {symbol}")
                with fail_lock:
                    failed_loads += 1
                return False
        except Exception as e:
            logger.error(f"❌ Error preloading data for {symbol} {timeframe}: {e}")
            with fail_lock:
                failed_loads += 1
            return False

    # Use ThreadPoolExecutor for parallel data loading
    max_workers = min(8, len(symbols))  # Limit concurrent requests to avoid overwhelming APIs

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Create tasks for all symbol-timeframe combinations
        tasks = []
        for symbol in symbols:
            for timeframe in ["15min", "5min"]:
                task = executor.submit(load_symbol_data, symbol, timeframe)
                tasks.append(task)

        # Wait for all tasks to complete
        concurrent.futures.wait(tasks)

    logger.info(f"📊 PRE-MARKET DATA LOADING COMPLETE")
    logger.info(f"✅ Successful: {successful_loads}, ❌ Failed: {failed_loads}")
    logger.info("🎯 System ready for trading when market opens!")


async def preload_historical_data_async(logger: logging.Logger, market_data_service, signals: List[Signal]):
    """Async version of preload_historical_data with better performance"""
    import asyncio
    import concurrent.futures
    from functools import partial

    if not signals:
        logger.warning("No signals available for preloading data")
        return

    symbols = list(set([signal.symbol for signal in signals]))
    logger.info(f"📊 PRE-MARKET: Async preloading historical data for {len(symbols)} symbols...")

    successful_loads = 0
    failed_loads = 0

    async def load_symbol_data_async(symbol, timeframe):
        """Async wrapper for loading data for a single symbol and timeframe"""
        nonlocal successful_loads, failed_loads
        try:
            # CRITICAL FIX: Use market_data_service directly, not websocket
            # Run the synchronous function in a thread pool
            loop = asyncio.get_event_loop()
            
            # Create a proper partial function call
            def get_data():
                return market_data_service.get_historical_data(symbol, timeframe, 5)
            
            candles = await loop.run_in_executor(None, get_data)

            if candles and len(candles) > 0:
                logger.info(f"✅ Preloaded {len(candles)} {timeframe} candles for {symbol}")
                successful_loads += 1
                return True
            else:
                logger.warning(f"⚠️ Failed to preload {timeframe} data for {symbol}")
                failed_loads += 1
                return False
        except Exception as e:
            logger.error(f"❌ Error preloading data for {symbol} {timeframe}: {e}")
            failed_loads += 1
            return False

    # Create tasks for all symbol-timeframe combinations
    tasks = []
    for symbol in symbols:
        for timeframe in ["15min", "5min"]:
            task = load_symbol_data_async(symbol, timeframe)
            tasks.append(task)

    # Run all tasks concurrently with semaphore to limit concurrent operations
    semaphore = asyncio.Semaphore(12)  # Limit to 12 concurrent operations

    async def bounded_task(task):
        async with semaphore:
            return await task

    bounded_tasks = [bounded_task(task) for task in tasks]
    await asyncio.gather(*bounded_tasks, return_exceptions=True)

    logger.info(f"📊 PRE-MARKET ASYNC DATA LOADING COMPLETE")
    logger.info(f"✅ Successful: {successful_loads}, ❌ Failed: {failed_loads}")
    logger.info("🎯 System ready for trading when market opens!")


def wait_for_market_open(logger: logging.Logger):
    """Wait until market opens at 9:15 AM with graceful interrupt handling"""
    from datetime import datetime, time
    import time as time_module
    import sys

    market_open_time = time(9, 15)

    try:
        while True:
            current_time = datetime.now().time()
            if current_time >= market_open_time:
                logger.info("🔔 Market is now OPEN! Starting trading operations...")
                break

            # Calculate time remaining
            current_datetime = datetime.now()
            market_open_datetime = current_datetime.replace(
                hour=market_open_time.hour,
                minute=market_open_time.minute,
                second=0,
                microsecond=0
            )

            if market_open_datetime <= current_datetime:
                # Market open time has passed for today
                break

            time_remaining = market_open_datetime - current_datetime
            minutes_remaining = int(time_remaining.total_seconds() / 60)

            logger.info(f"⏰ Waiting for market open... {minutes_remaining} minutes remaining")
            logger.info("💡 Press Ctrl+C to exit gracefully")
            time_module.sleep(60)  # Check every minute

    except KeyboardInterrupt:
        logger.info("🛑 Market wait interrupted by user. Exiting gracefully...")
        print("\n⏹️ Exiting before market open. No trades were placed.")
        sys.exit(0)


async def main():
    """Main execution function with enhanced balance checking and global trade management"""
    print("=" * 80)
    print("🏦 ENHANCED PYTHON TRADING SYSTEM - GLOBAL RISK MANAGEMENT")
    print("=" * 80)
    print("🏭 PRODUCTION TRADING FEATURES:")
    print("   • Real Balance Check & 1% Daily Risk Calculation")
    print("   • Global Trade Limit: 3 trades per day (across ALL strategies)")
    print("   • Dynamic Order Value Limit: Current Balance × 3.5x margin")
    print("   • Dynamic Risk Per Trade: 1% of total available balance")
    print("   • Real Order Execution through SmartAPI")
    print("   • No Virtual/Paper Trading Components")
    print("   • Pre-market Data Loading & MongoDB-First Strategy")
    print("   • ⚡ ASYNC OPTIMIZATION: 10x faster data processing")
    print("=" * 80)

    # Setup logging
    logger = setup_logging()
    logger.info("🚀 Starting Enhanced Python Trading System with Global Risk Management")

    # Start Telegram bot in background (optional)
    telegram_bot_thread = None
    try:
        telegram_bot_thread = run_bot_in_background()
        if telegram_bot_thread:
            logger.info("🤖 Telegram bot started in background")
        else:
            logger.info("⚠️ Telegram bot not started (check configuration)")
    except Exception as e:
        logger.warning(f"⚠️ Failed to start Telegram bot: {e}")
        logger.info("📈 Continuing without Telegram bot...")

    # Send system startup notification
    if is_telegram_available():
        try:
            startup_message = f"""
🚀 **Trading System Started**

📅 Date: {datetime.now().strftime('%Y-%m-%d')}
⏰ Time: {datetime.now().strftime('%H:%M:%S')}
🏦 Accounts: {len(config.accounts)}
📊 Strategies: MACrossover, SupportResistance, ORB, GapAndGo
🤖 Telegram Bot: {'✅ Active' if telegram_bot_thread else '❌ Inactive'}

System is ready for trading!
"""
            notify_system_status("startup", startup_message)
            logger.info("📱 System startup notification sent via Telegram")
        except Exception as e:
            logger.warning(f"Failed to send startup notification: {e}")

    # Initialize trading logger
    trading_logger = TradingLogger(log_directory="logs", logger=logger)
    logger.info("📝 Trading logger initialized")

    # Validate configuration
    if not config.validate():
        logger.error("❌ Configuration validation failed. Please check your .env file.")
        sys.exit(1)

    # Create services first to get order service for balance checking
    market_data_service, order_service, websocket_service = create_services(logger)

    # Morning balance check and risk calculation (with real balance if using SmartAPI)
    total_balance, daily_risk_per_trade, margin_multiplier = check_available_balance_and_calculate_risk(
        logger, config, order_service if not config.trading.paper_trading else None
    )

    # CRITICAL SAFETY: Set absolute maximum trades limit
    ABSOLUTE_MAX_TRADES = 3

    # Enhanced market hours check
    is_market_time = check_and_wait_for_market_hours(logger)

    # Initialize enhanced trade tracking and order management
    logger.info("🔧 Initializing enhanced trade tracking and order management...")

    # Initialize daily trade tracker
    trade_tracker = DailyTradeTracker(
        logger=logger,
        data_dir="data",
        max_trades_per_day=ABSOLUTE_MAX_TRADES
    )

    # Clean up any stale pending trades from previous runs
    logger.info("🧹 Cleaning up stale pending trades...")
    stale_count = trade_tracker.cleanup_stale_pending_trades(max_age_minutes=10)
    if stale_count > 0:
        logger.info(f"🧹 Cleaned up {stale_count} stale pending trades from previous runs")

    # Initialize order state manager
    order_manager = OrderStateManager(
        logger=logger,
        order_service=order_service,
        data_dir="data"
    )

    # Initialize startup validator
    startup_validator = StartupValidator(
        logger=logger,
        trade_tracker=trade_tracker,
        order_manager=order_manager,
        order_service=order_service,
        max_trades_per_day=ABSOLUTE_MAX_TRADES
    )

    # Run comprehensive startup validation
    system_ready, validation_results = startup_validator.run_full_validation()

    if not system_ready:
        can_trade, reason = startup_validator.get_trading_permission()
        if not can_trade:
            # Check if this is a monitoring-only situation (excess positions)
            position_summary = order_manager.get_position_summary()
            if position_summary['total_positions'] > ABSOLUTE_MAX_TRADES:
                logger.warning(f"⚠️ MONITORING MODE: {reason}")
                logger.warning("⚠️ System will monitor existing positions but NOT place new trades")
                logger.warning("⚠️ This is normal when you have excess positions from previous trading")
                # Continue execution in monitoring-only mode
                SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS = True
            else:
                logger.error(f"🛑 SYSTEM BLOCKED: {reason}")
                logger.error("🛑 Trading is not allowed due to validation failures")
                logger.error("🛑 Please resolve the issues above before restarting")
                sys.exit(1)
        else:
            SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS = False
    else:
        SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS = False

    # Get trading permission and summary
    can_trade, permission_reason = startup_validator.get_trading_permission()
    trade_summary = trade_tracker.get_daily_summary()
    position_summary = order_manager.get_position_summary()

    logger.info("=" * 60)
    logger.info("📊 ENHANCED TRADING SYSTEM STATUS")
    logger.info("=" * 60)

    if SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS:
        logger.warning(f"⚠️ MONITORING MODE: System locked due to excess positions")
        logger.warning(f"📊 Current Positions: {position_summary['total_positions']} > {ABSOLUTE_MAX_TRADES} maximum")
        logger.warning(f"🔒 New Trading: BLOCKED")
        logger.warning(f"👁️ Position Monitoring: ACTIVE")
    else:
        logger.info(f"🎯 Trading Permission: {'✅ ALLOWED' if can_trade else '❌ BLOCKED'}")

    logger.info(f"📈 Daily Trades: {trade_summary['executed_trades']}/{trade_summary['max_trades_per_day']} (Remaining: {trade_summary['remaining_trades']})")
    logger.info(f"📊 Open Positions: {position_summary['total_positions']}")
    logger.info(f"💰 Unrealized P&L: ₹{position_summary['total_unrealized_pnl']:.2f}")
    if trade_summary['traded_symbols']:
        logger.info(f"📋 Traded Symbols: {', '.join(trade_summary['traded_symbols'])}")
    if position_summary['symbols']:
        logger.info(f"📍 Position Symbols: {', '.join(position_summary['symbols'])}")
    logger.info("=" * 60)

    # Get final trading permission
    can_trade, permission_reason = startup_validator.get_trading_permission()

    # SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS is already set above in the validation section
    if SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS:
        logger.warning(f"⚠️ MONITORING MODE ACTIVE: {permission_reason}")
        logger.warning("⚠️ System will monitor existing positions but will NOT place any new trades")
    elif not can_trade:
        logger.error(f"🛑 TRADING BLOCKED: {permission_reason}")
        SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS = True

    # Services already created above for balance checking

    # Load monitored stocks early for pre-market data loading
    signals = load_monitored_stocks_only(logger)

    logger.info(f"💰 Balance initialized with ₹{total_balance:,.2f} starting balance")
    logger.info(f"📊 Daily risk per trade: ₹{daily_risk_per_trade:,.2f} (1% of balance)")
    logger.info(f"📈 Margin multiplier: {margin_multiplier}x")

    # Pre-market data loading if market is not open yet
    if not is_market_time and signals:
        await preload_historical_data_async(logger, market_data_service, signals)

        # Wait for market to open
        wait_for_market_open(logger)

    # Initialize background data updater for efficient data service with direct cache access
    background_updater = None
    if isinstance(market_data_service, EfficientMarketDataService):
        background_updater = BackgroundDataUpdater(
            logger=logger,
            smart_api=getattr(market_data_service, 'smart_api', None),
            market_data_service=market_data_service  # Pass reference for direct cache updates
        )

        # Add symbols to background updater
        if signals:
            symbols = list(set([signal.symbol for signal in signals]))
            background_updater.add_symbols_to_monitor(symbols)
            background_updater.start()
            logger.info(f"🔄 Background data updater started for {len(symbols)} symbols with CACHE_FIRST optimization")

    # Create production strategy manager with real trading
    # Temporarily modify config to use dynamic risk per trade
    modified_trading_config = config.trading
    modified_trading_config.max_risk_per_trade = daily_risk_per_trade

    # Get trade count and symbols from enhanced tracking
    existing_trade_count = trade_summary['executed_trades']
    traded_symbols = set(trade_summary['traded_symbols'])

    # CRITICAL SAFETY: Ensure we don't exceed the absolute maximum trades
    if existing_trade_count >= ABSOLUTE_MAX_TRADES:
        logger.error(f"🛑 CRITICAL SAFETY: Already reached or exceeded maximum trades")
        logger.error(f"🛑 Executed trades: {existing_trade_count}/{ABSOLUTE_MAX_TRADES}")
        logger.error("🛑 No more trades will be allowed today")
        existing_trade_count = ABSOLUTE_MAX_TRADES

    logger.info(f"Initializing strategy manager with {existing_trade_count} existing trades")
    logger.info(f"📊 Enhanced tracking: {existing_trade_count} executed trades, {position_summary['total_positions']} open positions")
    logger.info(f"Already traded symbols today: {', '.join(traded_symbols) if traded_symbols else 'None'}")

    strategy_manager = ProductionStrategyManager(
        logger=logger,
        market_data_service=market_data_service,
        order_service=order_service,
        config=modified_trading_config,
        trading_logger=trading_logger,
        initial_trade_count=existing_trade_count,
        initial_traded_symbols=traded_symbols,
        trade_tracker=trade_tracker,
        order_manager=order_manager
    )
    
    # Enhanced position monitoring with order state manager integration
    open_positions_from_manager = order_manager.get_open_positions()

    if open_positions_from_manager:
        # Convert position records to Order objects for the position monitor
        orders_for_monitoring = []
        for position in open_positions_from_manager:
            try:
                # Create Order object from position record
                order = Order(
                    order_id=position.order_id,
                    symbol=position.symbol,
                    symbol_token="",  # Will be fetched if needed
                    exchange="NSE",
                    transaction_type=TransactionType.BUY,  # Assume BUY for monitoring
                    order_type=OrderType.MARKET,
                    product_type=ProductType.INTRADAY,
                    quantity=position.quantity,
                    price=position.entry_price,
                    stop_loss=position.stop_loss,
                    target=position.target,
                    status=OrderStatus.COMPLETED,
                    entry_price=position.entry_price,
                    strategy=position.strategy
                )
                orders_for_monitoring.append(order)
                logger.info(f"📈 Added position to monitor: {position.symbol} @ ₹{position.entry_price}")
            except Exception as e:
                logger.error(f"Error converting position {position.symbol} for monitoring: {e}")

        if orders_for_monitoring:
            # Store for position monitor initialization
            prepared_positions = orders_for_monitoring
            logger.info(f"✅ Prepared {len(orders_for_monitoring)} positions for enhanced monitoring")
        else:
            prepared_positions = []
            logger.warning("⚠️ No valid positions could be prepared for monitoring")
    else:
        prepared_positions = []
        logger.info("ℹ️ No open positions found - starting with clean slate")

    # Global trade management is now handled internally by ProductionStrategyManager

    # Create async strategy manager for optimized performance
    async_strategy_manager = ProductionAsyncStrategyManager(strategy_manager, logger)
    async_data_processor = ProductionAsyncMarketDataProcessor(market_data_service, logger)

    # Log production trading summary
    logger.info("🏭 PRODUCTION TRADING SUMMARY")
    logger.info("=" * 50)
    strategy_summary = strategy_manager.get_strategy_summary()
    logger.info(f"📊 Global Trade Limit: {strategy_summary['max_trades_per_day']} trades/day")
    logger.info(f"🔢 Trades Today: {strategy_summary['total_trades_today']}")
    logger.info(f"📈 Remaining Trades: {strategy_summary['remaining_trades']}")
    logger.info(f"📍 Active Positions: {strategy_summary['active_positions']}")
    logger.info(f"🎯 Active Strategies: {len(strategy_summary['strategies'])}")
    logger.info("=" * 50)

    # Initialize advanced position monitor with enhanced exit strategies
    trailing_sl_config = TrailingStopLossConfig(
        activation_threshold=0.3,  # Activate trailing SL after price moves 30% beyond target
        trail_percentage=0.5,      # Trail 50% of the profit
        min_trail_distance=0.2,    # Minimum trail distance 0.2% of entry price
        max_trail_distance=2.0     # Maximum trail distance 2% of entry price
    )
    
    # Ensure compatibility between services
    if websocket_service and not hasattr(websocket_service, 'get_last_price') and hasattr(websocket_service, 'get_live_price'):
        websocket_service.get_last_price = websocket_service.get_live_price
        logger.info("Added get_last_price compatibility method to websocket service")
    
    if not hasattr(market_data_service, 'get_last_price') and hasattr(market_data_service, 'get_current_price'):
        market_data_service.get_last_price = market_data_service.get_current_price
        logger.info("Added get_last_price compatibility method to market data service")
    
    position_monitor = AdvancedPositionMonitor(
        order_service=order_service,
        market_data_service=market_data_service,
        logger=logger,
        websocket_service=websocket_service,
        trading_logger=trading_logger,
        square_off_time="15:12",   # Square off at 15:12 as required
        trailing_sl_config=trailing_sl_config
    )
    
    # Add prepared positions to monitor
    if prepared_positions:
        for order in prepared_positions:
            position_monitor.add_position(order)
        logger.info(f"📈 Added {len(prepared_positions)} positions to advanced monitor")

    # Start position monitoring
    position_monitor.start_monitoring()
    logger.info("🔍 Advanced Position Monitor started with enhanced exit strategies:")
    logger.info("   1. Immediate square-off on SL hit")
    logger.info("   2. Square off at 15:12 hrs")
    logger.info("   3. Square off if price goes above target and comes back to target price")
    logger.info("   4. Trailing stop loss that activates after price reaches beyond target")
    logger.info(f"⏰ Auto square-off time: 15:12")

    # Initialize stock symbol integration service
    from services.stock_symbol_integration_service import StockSymbolIntegrationService
    symbol_service = StockSymbolIntegrationService(data_directory="data", logger=logger)
    
    # Connect symbol service to market data service if it's the efficient type
    if isinstance(market_data_service, EfficientMarketDataService):
        market_data_service.set_symbol_integration_service(symbol_service)
        logger.info("🔗 Connected symbol integration service to market data service")
    
    # Use preloaded signals or load if not available
    if not signals:
        signals = load_monitored_stocks_only(logger)

    if not signals:
        logger.error("❌ No stocks to monitor from stocks_to_monitor.csv")
        sys.exit(1)

    # Subscribe WebSocket to all monitored symbols for real-time price updates
    if websocket_service:
        # Ensure websocket service has the get_last_price method for compatibility
        if not hasattr(websocket_service, 'get_last_price') and hasattr(websocket_service, 'get_live_price'):
            websocket_service.get_last_price = websocket_service.get_live_price
            logger.info("Added compatibility wrapper for WebSocket service")
            
        symbols_to_subscribe = list(set([signal.symbol for signal in signals]))
        logger.info(f"📡 Subscribing WebSocket to {len(symbols_to_subscribe)} symbols for real-time prices...")

        subscription_success = 0
        for symbol in symbols_to_subscribe:
            # Get token from market data service
            token_info = market_data_service.get_symbol_token(symbol)
            if token_info:
                token, exchange = token_info
                exchange_type = 1 if exchange == "NSE" else 2  # NSE=1, NFO=2

                if websocket_service.subscribe_symbol(symbol, token, exchange_type):
                    subscription_success += 1
                else:
                    logger.warning(f"⚠️ Failed to subscribe to {symbol}")
            else:
                logger.warning(f"⚠️ No token found for {symbol}")

        logger.info(f"✅ WebSocket subscribed to {subscription_success}/{len(symbols_to_subscribe)} symbols")
        logger.info("🚀 Real-time price updates enabled - API calls minimized!")
    else:
        logger.warning("⚠️ WebSocket service not available - using API fallback")

    logger.info(f"Starting continuous monitoring of {len(signals)//2} stocks")
    logger.info("System will run until market close or manual interruption")

    # Start continuous monitoring loop with async optimization
    import time
    import asyncio
    from datetime import datetime

    monitoring_interval = 30  # seconds between checks
    cooldown_period = 30 * 60  # 30 minutes cooldown for same stock
    last_trade_times = {}  # symbol -> last trade timestamp
    total_orders_placed = 0
    last_signal_processing_time = 0.0
    min_signal_processing_gap = 10.0  # Minimum 10 seconds between signal processing cycles
    last_cleanup_time = 0.0  # Track when we last cleaned up stale trades
    cleanup_interval = 300.0  # Clean up stale trades every 5 minutes

    try:
        logger.info("🚀 Starting optimized continuous monitoring loop...")
        logger.info(f"⏱️ Checking every {monitoring_interval} seconds")
        logger.info("📊 Press Ctrl+C to stop monitoring")

        while True:
            if not is_market_open() and os.getenv("OVERRIDE_MARKET_OPEN") != "true":
                logger.info("📴 Market has closed. Stopping monitoring.")
                break

            current_time = datetime.now().strftime("%H:%M:%S")

            # Periodic cleanup of stale pending trades
            current_processing_time = time.time()
            if current_processing_time - last_cleanup_time > cleanup_interval:
                stale_count = trade_tracker.cleanup_stale_pending_trades(max_age_minutes=5)
                if stale_count > 0:
                    logger.info(f"🧹 Periodic cleanup: Removed {stale_count} stale pending trades")
                last_cleanup_time = current_processing_time

            # Get enhanced trade summary
            enhanced_trade_summary = trade_tracker.get_daily_summary()
            enhanced_position_summary = order_manager.get_position_summary()
            legacy_trade_summary = strategy_manager.get_strategy_summary()

            ABSOLUTE_MAX_TRADES = 3

            # CRITICAL SAFETY: Check if system is locked due to excess positions
            if SYSTEM_LOCKED_DUE_TO_EXCESS_POSITIONS:
                logger.error(f"🛑 SYSTEM LOCKED: Too many existing positions detected - no new trades allowed")
                can_place_new_trades = False
            elif enhanced_trade_summary['executed_trades'] >= ABSOLUTE_MAX_TRADES:
                logger.error(f"🛑 ENHANCED SAFETY: Absolute trade limit reached: {enhanced_trade_summary['executed_trades']}/{ABSOLUTE_MAX_TRADES}")
                can_place_new_trades = False
            elif enhanced_position_summary['total_positions'] >= ABSOLUTE_MAX_TRADES:
                logger.error(f"🛑 POSITION SAFETY: Too many open positions: {enhanced_position_summary['total_positions']}/{ABSOLUTE_MAX_TRADES}")
                can_place_new_trades = False
            else:
                can_place_new_trades = enhanced_trade_summary['can_trade_more']

            open_positions_count = enhanced_position_summary['total_positions']
            logger.info(f"🎯 Enhanced Trade Status: {enhanced_trade_summary['executed_trades']}/{enhanced_trade_summary['max_trades_per_day']} trades used")
            logger.info(f"📊 Open Positions: {enhanced_position_summary['total_positions']}")

            # Double-check with legacy system for consistency
            if enhanced_trade_summary['executed_trades'] >= ABSOLUTE_MAX_TRADES:
                logger.error(f"🛑 ENHANCED SAFETY OVERRIDE: Trade limit reached, forcing can_place_new_trades = False")
                can_place_new_trades = False

            if can_place_new_trades:
                # ENHANCED SAFETY: Double-check trade limit before processing any signals
                current_enhanced_summary = trade_tracker.get_daily_summary()
                if current_enhanced_summary['executed_trades'] >= ABSOLUTE_MAX_TRADES:
                    logger.error(f"🛑 ENHANCED SAFETY: Trade limit reached in main loop check: {current_enhanced_summary['executed_trades']}/{ABSOLUTE_MAX_TRADES}")
                    logger.error("🛑 Skipping signal processing to prevent exceeding trade limit")
                    can_place_new_trades = False
                    continue

                current_processing_time = time.time()
                time_since_last_processing = current_processing_time - last_signal_processing_time
                if last_signal_processing_time > 0 and time_since_last_processing < min_signal_processing_gap:
                    remaining_wait = min_signal_processing_gap - time_since_last_processing
                    logger.info(f"⏳ Signal processing timing control: Must wait {remaining_wait:.1f}s before next processing cycle")
                else:
                    logger.info(f"🔍 [{current_time}] Scanning {len(signals)} stocks for entry opportunities...")
                    available_signals = []
                    for signal in signals:
                        # Skip if symbol already has an open position
                        if signal.symbol in strategy_manager.active_positions:
                            logger.debug(f"⏭️ Skipping {signal.symbol} - already has active position")
                            continue
                        # Skip if symbol has already been traded today
                        if signal.symbol in strategy_manager.traded_symbols:
                            logger.debug(f"⏭️ Skipping {signal.symbol} - already traded today")
                            continue
                        # Enforce cooldown for same stock
                        last_trade = last_trade_times.get(signal.symbol, 0)
                        if time.time() - last_trade < cooldown_period:
                            logger.debug(f"⏳ Skipping {signal.symbol} - cooldown period active")
                            continue
                        available_signals.append(signal)
                    if not available_signals:
                        logger.info("⏳ No available signals - all stocks either have positions, already traded today, or in cooldown")
                    else:
                        logger.info(f"🎯 Processing {len(available_signals)} available signals (filtered from {len(signals)} total)")
                        last_signal_processing_time = current_processing_time
                        # Check available balance before placing order
                        balance_info = order_service.get_real_balance()
                        available_balance = 0
                        if balance_info:
                            available_balance = max(balance_info.get('available_margin', 0), balance_info.get('available_cash', 0))
                        start_time = time.time()

                        # CRITICAL SAFETY: Final check before calling process_signals
                        final_trade_summary = strategy_manager.get_strategy_summary()
                        if final_trade_summary['total_trades_today'] >= ABSOLUTE_MAX_TRADES:
                            logger.error(f"🛑 CRITICAL SAFETY: Trade limit reached just before processing: {final_trade_summary['total_trades_today']}/{ABSOLUTE_MAX_TRADES}")
                            logger.error("🛑 Aborting signal processing")
                            continue

                        # ENHANCED ASYNC: Process all available signals asynchronously
                        # Only limit by remaining trades, not artificially to 1
                        max_signals_to_process = min(len(available_signals), ABSOLUTE_MAX_TRADES - final_trade_summary['total_trades_today'])
                        signals_to_process = available_signals[:max_signals_to_process]

                        logger.info(f"🚀 ASYNC: Processing {len(signals_to_process)} signals out of {len(available_signals)} available")

                        # Use async strategy manager to process signals concurrently
                        new_orders = await async_strategy_manager.process_signals_batch_async(signals_to_process)
                        # Update last trade times for processed signals
                        for order in new_orders:
                            if hasattr(order, 'symbol'):
                                last_trade_times[order.symbol] = time.time()
                        
                        processing_time = time.time() - start_time
                        if new_orders:
                            total_orders_placed += len(new_orders)
                            logger.info(f"✅ Placed {len(new_orders)} new orders! Total today: {total_orders_placed}")
                            logger.info(f"⚡ Processing time: {processing_time:.2f}s (optimized)")
                            logger.info(f"⏱️ Next signal processing allowed in {min_signal_processing_gap}s")
                            trade_summary = strategy_manager.get_strategy_summary()
                            logger.info(f"📊 Global trades today: {trade_summary['total_trades_today']}/{trade_summary['max_trades_per_day']}")
                            for i, order in enumerate(new_orders, 1):
                                order_value = order.price * order.quantity
                                # Safe handling of transaction_type which might be None or an enum
                                transaction_type_str = "UNKNOWN"
                                if hasattr(order, 'transaction_type') and order.transaction_type:
                                    if hasattr(order.transaction_type, 'value'):
                                        transaction_type_str = order.transaction_type.value
                                    else:
                                        transaction_type_str = str(order.transaction_type)
                                
                                # Safe handling of stop_loss and target which might be None
                                stop_loss_str = f"{order.stop_loss:.2f}" if order.stop_loss else "None"
                                target_str = f"{order.target:.2f}" if order.target else "None"
                                
                                logger.info(f"📈 Order {i}: {order.symbol} {transaction_type_str} "
                                          f"Qty:{order.quantity} Price:{order.price:.2f} "
                                          f"SL:{stop_loss_str} Target:{target_str}")
                                logger.info(f"💰 Order Value: ₹{order_value:,.2f} | Risk: ₹{daily_risk_per_trade:,.2f}")
                            if len(new_orders) + trade_summary['total_trades_today'] >= ABSOLUTE_MAX_TRADES:
                                logger.error("🛑 EXITING SIGNAL PROCESSING: Trade limit reached!")
                                break
                        else:
                            logger.info(f"⏳ No entry conditions met. Processing time: {processing_time:.2f}s")
            else:
                logger.info(f"🛑 [{current_time}] Max trades reached. Monitoring {open_positions_count} open positions...")

            # Log current statistics for enhanced production trading
            enhanced_trade_summary = trade_tracker.get_daily_summary()
            enhanced_position_summary = order_manager.get_position_summary()
            legacy_trade_summary = strategy_manager.get_strategy_summary()

            logger.info(f"📊 Enhanced Stats: {enhanced_trade_summary['executed_trades']} executed trades, "
                       f"{enhanced_position_summary['total_positions']} positions open across {len(legacy_trade_summary['strategies'])} strategies")

            if enhanced_trade_summary['traded_symbols']:
                logger.info(f"📈 Traded symbols: {', '.join(enhanced_trade_summary['traded_symbols'])}")

            if enhanced_position_summary['symbols']:
                logger.info(f"📍 Position symbols: {', '.join(enhanced_position_summary['symbols'])}")

            # Log strategy breakdown
            for strategy_name, trade_count in legacy_trade_summary['trades_by_strategy'].items():
                logger.info(f"   {strategy_name}: {trade_count} trades today")

            # Only exit if trade limit reached AND no open positions to monitor
            if enhanced_trade_summary['executed_trades'] >= ABSOLUTE_MAX_TRADES and enhanced_position_summary['total_positions'] == 0:
                logger.info("🏁 Enhanced trade limit reached and no open positions. Monitoring complete.")
                logger.info(f"📊 Final Enhanced Status: {enhanced_trade_summary['executed_trades']}/{ABSOLUTE_MAX_TRADES} trades used today")
                break
            elif enhanced_trade_summary['executed_trades'] >= ABSOLUTE_MAX_TRADES:
                logger.info(f"🛑 Enhanced trade limit reached ({enhanced_trade_summary['executed_trades']}/{ABSOLUTE_MAX_TRADES})")
                logger.info(f"📊 Continuing to monitor {open_positions_count} open positions...")
                # Continue monitoring but don't place new trades

            # If no open positions and can't place new trades, we can exit
            if not can_place_new_trades and open_positions_count == 0:
                logger.info("🏁 Global trade limit reached and no open positions. Monitoring complete.")
                logger.info(f"📊 Final Status: {trade_summary['total_trades_today']}/{trade_summary['max_trades_per_day']} trades used today")
                break

            # Wait before next scan
            logger.info(f"⏸️ Waiting {monitoring_interval} seconds before next scan...")
            try:
                await asyncio.sleep(monitoring_interval)
            except asyncio.CancelledError:
                logger.info("🛑 Sleep interrupted, shutting down gracefully...")
                break

        # Final summary for enhanced production trading
        final_enhanced_trade_summary = trade_tracker.get_daily_summary()
        final_enhanced_position_summary = order_manager.get_position_summary()
        final_legacy_summary = strategy_manager.get_strategy_summary()

        print("\n" + "=" * 80)
        print("🏭 FINAL ENHANCED PRODUCTION TRADING SUMMARY")
        print("=" * 80)
        print("🎯 ENHANCED TRADE MANAGEMENT:")
        print(f"   • Executed Trades: {final_enhanced_trade_summary['executed_trades']}/{final_enhanced_trade_summary['max_trades_per_day']}")
        print(f"   • Pending Trades: {final_enhanced_trade_summary['pending_trades']}")
        print(f"   • Rejected Trades: {final_enhanced_trade_summary['rejected_trades']}")
        print(f"   • Raw Available Balance: ₹{total_balance/3.5:,.2f}")
        print(f"   • Intraday Margin (3.5x): ₹{total_balance:,.2f}")
        print(f"   • Daily Risk Per Trade: ₹{daily_risk_per_trade:,.2f} (1% of raw balance)")
        print(f"   • Max Order Value: ₹{total_balance:,.2f} (with auto-adjustment)")
        print()
        print("💰 ENHANCED POSITION SUMMARY:")
        print(f"   • Open Positions: {final_enhanced_position_summary['total_positions']}")
        print(f"   • Unrealized P&L: ₹{final_enhanced_position_summary['total_unrealized_pnl']:.2f}")
        print(f"   • Active Strategies: {len(final_legacy_summary['strategies'])}")
        print(f"   • Remaining Trades: {final_enhanced_trade_summary['remaining_trades']}")

        if final_enhanced_trade_summary['traded_symbols']:
            print(f"   • Traded Symbols: {', '.join(final_enhanced_trade_summary['traded_symbols'])}")

        if final_enhanced_position_summary['symbols']:
            print(f"   • Position Symbols: {', '.join(final_enhanced_position_summary['symbols'])}")

        print("\n📋 STRATEGY BREAKDOWN:")
        print("-" * 80)
        for strategy_name, trade_count in final_legacy_summary['trades_by_strategy'].items():
            print(f"{strategy_name}: {trade_count} trades today")

        print("=" * 80)

        # Log daily performance
        trading_logger.log_daily_performance(active_positions=final_summary['active_positions'])

        # Send daily summary notification via Telegram
        if is_telegram_available():
            try:
                # Calculate win rate (simplified for production)
                total_trades = final_summary['total_trades_today']
                winning_trades = 0
                if total_trades > 0:
                    # Estimate winning trades (simplified - would need actual P&L tracking)
                    winning_trades = max(0, int(total_trades * 0.6))
                win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

                notify_daily_summary(
                    total_trades,
                    0.0,  # P&L would need to be calculated from actual trades
                    win_rate,
                    final_summary['active_positions']
                )
                logger.info("📱 Daily summary notification sent via Telegram")
            except Exception as e:
                logger.warning(f"Failed to send daily summary notification: {e}")

        # Generate comprehensive trading reports
        print("\n📊 GENERATING TRADING REPORTS...")
        print("-" * 80)

        try:
            # Initialize report generator
            report_generator = TradingReportGenerator(
                log_directory="logs",
                reports_directory="reports",
                logger=logger
            )

            # Generate daily report
            daily_report_path = report_generator.generate_daily_report()
            print(f"✅ Daily Report: {daily_report_path}")

            # Check and generate pending weekly/monthly reports
            current_date = datetime.now()

            # Generate weekly report if it's Friday or later in the week
            if current_date.weekday() >= 4:  # Friday = 4
                weekly_report_path = report_generator.generate_weekly_report()
                if weekly_report_path:
                    print(f"✅ Weekly Report: {weekly_report_path}")

            # Generate monthly report if it's near end of month (after 25th)
            if current_date.day > 25:
                monthly_report_path = report_generator.generate_monthly_report()
                if monthly_report_path:
                    print(f"✅ Monthly Report: {monthly_report_path}")

            # Check for any pending reports from missed days
            report_generator.check_and_generate_pending_reports()

        except Exception as e:
            logger.error(f"❌ Error generating reports: {e}")
            print(f"❌ Report generation failed: {e}")

        # Log files information
        print(f"\n📁 Files created:")
        print(f"   📊 Reports: 'reports/' directory (HTML dashboards)")
        print(f"   📝 Logs: 'logs/' directory")
        print(f"   - trades_{datetime.now().strftime('%Y-%m-%d')}.csv")
        print(f"   - signals_{datetime.now().strftime('%Y-%m-%d')}.csv")
        print(f"   - performance_{datetime.now().strftime('%Y-%m-%d')}.csv")

        # Square off any remaining open positions using advanced position monitor
        logger.info("🔄 Squaring off all remaining positions...")
        position_monitor.square_off_all_positions("END_OF_DAY")
        position_monitor.stop_monitoring()
        logger.info("✅ All positions squared off")

        # Stop background data updater
        if background_updater:
            background_updater.stop()
            logger.info("🛑 Background data updater stopped")

        # Cleanup async managers
        async_strategy_manager.cleanup()
        async_data_processor.cleanup()
        logger.info("🧹 Async managers cleaned up")

        # Disconnect WebSocket
        if websocket_service:
            websocket_service.disconnect()

        # If using real trading, logout from SmartAPI
        if not config.trading.paper_trading and hasattr(order_service, 'logout'):
            order_service.logout()

    except (KeyboardInterrupt, asyncio.CancelledError):
        logger.info("🛑 Monitoring interrupted by user or cancelled")
        print(f"\n⏹️ Monitoring stopped. Total orders placed today: {total_orders_placed}")

        # Generate reports before cleanup
        try:
            logger.info("📊 Generating reports before exit...")
            report_generator = TradingReportGenerator(log_directory="logs", reports_directory="reports", logger=logger)
            daily_report_path = report_generator.generate_daily_report()
            print(f"✅ Daily Report Generated: {daily_report_path}")
        except Exception as report_error:
            logger.error(f"Error generating reports during cleanup: {report_error}")

        # Clean up and square off positions
        try:
            # Square off all positions using advanced position monitor
            logger.info("📤 Emergency square off for all positions")
            position_monitor.square_off_all_positions("EMERGENCY")
            position_monitor.stop_monitoring()

            # Stop services
            if background_updater:
                background_updater.stop()
            if websocket_service:
                websocket_service.disconnect()
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")

    except Exception as e:
        logger.error(f"❌ Error during monitoring: {e}")
        import traceback
        traceback.print_exc()

        # Generate reports before cleanup on error
        try:
            logger.info("📊 Generating reports before error cleanup...")
            report_generator = TradingReportGenerator(log_directory="logs", reports_directory="reports", logger=logger)
            daily_report_path = report_generator.generate_daily_report()
            print(f"✅ Daily Report Generated: {daily_report_path}")
        except Exception as report_error:
            logger.error(f"Error generating reports during error cleanup: {report_error}")

        # Clean up and square off positions on error
        try:
            # Square off all positions using advanced position monitor
            logger.info("📤 Error square off for all positions")
            position_monitor.square_off_all_positions("ERROR")
            position_monitor.stop_monitoring()

            # Stop services
            if background_updater:
                background_updater.stop()
            if websocket_service:
                websocket_service.disconnect()
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {cleanup_error}")

    logger.info("🏁 Python Trading System monitoring completed")

    # Send system shutdown notification
    if is_telegram_available():
        try:
            shutdown_message = f"""
🛑 **Trading System Shutdown**

📅 Date: {datetime.now().strftime('%Y-%m-%d')}
⏰ Time: {datetime.now().strftime('%H:%M:%S')}

System has completed trading operations for the day.
Check reports for detailed analysis.
"""
            notify_system_status("shutdown", shutdown_message)
            logger.info("📱 System shutdown notification sent via Telegram")
        except Exception as e:
            logger.warning(f"Failed to send shutdown notification: {e}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
