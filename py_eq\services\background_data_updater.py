"""
Background Data Updater Service
Maintains MongoDB cache with historical data and live updates
Runs in background to keep data fresh without blocking trading operations
"""
import logging
import threading
import time
import pymongo
from datetime import datetime, timedelta
from typing import List, Dict, Set
import os
import yfinance as yf
from SmartApi import SmartConnect

from models.candle import Candle
from utils.safe_logging import safe_info, safe_error, safe_warning, safe_debug


class BackgroundDataUpdater:
    """
    Background service that maintains MongoDB cache with fresh market data using YFinance
    Reduces API calls during trading by pre-loading and updating data
    Uses YFinance as primary source for historical data with SmartAPI fallback
    """

    def __init__(self, logger: logging.Logger, smart_api=None, market_data_service=None):
        self.logger = logger
        self.smart_api = smart_api
        self.market_data_service = market_data_service  # Reference for direct cache updates
        self.running = False
        self.update_thread = None

        # MongoDB connection
        self.mongodb_client = None
        self.db = None
        self.mongodb_connected = False

        # Update settings
        self.update_interval = 300  # 5 minutes
        self.symbols_to_monitor: Set[str] = set()
        self.timeframes = ["5min", "15min"]

        # Symbol mapping
        self.symbol_mapping = {
            'RELIANCE': ('2885', 'NSE'),
            'HDFCBANK': ('1333', 'NSE'),
            'INFY': ('1594', 'NSE'),
            'TCS': ('11536', 'NSE'),
            'ICICIBANK': ('4963', 'NSE'),
            'SIEMENS': ('3150', 'NSE'),
            'AUBANK': ('21238', 'NSE'),
            'APOLLOTYRE': ('163', 'NSE'),
            'NMDC': ('15332', 'NSE'),
            'GODREJPROP': ('17875', 'NSE'),
            'ASHOKLEY': ('212', 'NSE'),
            'GODREJCP': ('1232', 'NSE'),
            'ICICIGI': ('21770', 'NSE')
        }

        # Initialize MongoDB
        self._connect_mongodb()

    def _connect_mongodb(self):
        """Connect to MongoDB"""
        try:
            connection_string = os.getenv("MONGODB_CONNECTION_STRING", "mongodb://localhost:27017/")
            database_name = os.getenv("MONGODB_DATABASE_NAME", "trading_db")

            self.mongodb_client = pymongo.MongoClient(connection_string)
            self.db = self.mongodb_client[database_name]

            # Test connection
            self.mongodb_client.admin.command('ping')
            self.mongodb_connected = True
            safe_info(self.logger, "✅ Background updater connected to MongoDB")

        except Exception as e:
            safe_error(self.logger, f"❌ Background updater MongoDB connection failed: {e}")
            self.mongodb_connected = False

    def add_symbols_to_monitor(self, symbols: List[str]):
        """Add symbols to the monitoring list"""
        for symbol in symbols:
            self.symbols_to_monitor.add(symbol)
        safe_info(self.logger, f"📊 Added {len(symbols)} symbols to background monitoring: {symbols}")

    def start(self):
        """Start the background data updater"""
        if self.running:
            self.logger.warning("Background updater is already running")
            return

        if not self.mongodb_connected:
            self.logger.error("Cannot start background updater: MongoDB not connected")
            return

        self.running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        safe_info(self.logger, "🚀 Background data updater started")

    def stop(self):
        """Stop the background data updater"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=10)
        safe_info(self.logger, "🛑 Background data updater stopped")

    def _update_loop(self):
        """Main update loop that runs in background"""
        safe_info(self.logger, f"🔄 Background update loop started (interval: {self.update_interval}s)")

        while self.running:
            try:
                if self.symbols_to_monitor:
                    self._update_all_symbols()
                else:
                    self.logger.debug("No symbols to monitor, skipping update")

                # Wait for next update
                for _ in range(self.update_interval):
                    if not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                self.logger.error(f"Error in background update loop: {e}")
                time.sleep(30)  # Wait before retrying

    def _update_all_symbols(self):
        """Update data for all monitored symbols"""
        safe_info(self.logger, f"🔄 Updating data for {len(self.symbols_to_monitor)} symbols")

        for symbol in self.symbols_to_monitor:
            if not self.running:
                break

            try:
                self._update_symbol_data(symbol)
                time.sleep(1)  # Small delay between symbols
            except Exception as e:
                self.logger.error(f"Error updating {symbol}: {e}")

    def _update_symbol_data(self, symbol: str):
        """Update data for a specific symbol"""
        for timeframe in self.timeframes:
            try:
                # Check what data we already have
                latest_timestamp = self._get_latest_timestamp(symbol, timeframe)

                if latest_timestamp:
                    # Only fetch data after the latest timestamp
                    from_date = latest_timestamp + timedelta(minutes=1)
                    to_date = datetime.now()

                    # Skip if no new data needed
                    if from_date >= to_date:
                        continue

                    self.logger.debug(f"Updating {symbol} {timeframe} from {from_date}")
                else:
                    # No existing data, fetch last 3 days (sufficient for intraday)
                    to_date = datetime.now()
                    from_date = to_date - timedelta(days=3)
                    self.logger.info(f"Initial data load for {symbol} {timeframe}")

                # Fetch new data
                new_candles = self._fetch_candles(symbol, timeframe, from_date, to_date)

                if new_candles:
                    # OPTIMIZED SEQUENCE: Update memory cache FIRST for faster strategy analysis
                    self._update_memory_cache(symbol, timeframe, new_candles)

                    # Then save to MongoDB for persistence
                    self._save_candles_to_mongodb(symbol, timeframe, new_candles)

                    self.logger.debug(f"✅ Updated {symbol} {timeframe}: {len(new_candles)} new candles (cache→MongoDB)")

            except Exception as e:
                self.logger.error(f"Error updating {symbol} {timeframe}: {e}")

    def _get_latest_timestamp(self, symbol: str, timeframe: str) -> datetime:
        """Get the latest timestamp for a symbol/timeframe from MongoDB"""
        try:
            collection_name = f"{timeframe}_candles"
            collection = self.db[collection_name]

            latest_doc = collection.find_one(
                {"symbol": symbol},
                sort=[("timestamp", pymongo.DESCENDING)]
            )

            if latest_doc:
                return latest_doc["timestamp"]

        except Exception as e:
            self.logger.error(f"Error getting latest timestamp for {symbol}: {e}")

        return None

    def _fetch_candles(self, symbol: str, timeframe: str, from_date: datetime, to_date: datetime) -> List[Candle]:
        """Fetch candles from YFinance (primary) with SmartAPI fallback"""
        # Use YFinance as primary source for historical data
        candles = self._fetch_from_yfinance(symbol, timeframe, from_date, to_date)
        if candles:
            return candles

        # Fallback to SmartAPI only if YFinance fails
        if self.smart_api:
            self.logger.warning(f"YFinance failed for {symbol}, trying SmartAPI fallback")
            return self._fetch_from_smartapi(symbol, timeframe, from_date, to_date)

        return []

    def _fetch_from_smartapi(self, symbol: str, timeframe: str, from_date: datetime, to_date: datetime) -> List[Candle]:
        """Fetch candles from SmartAPI"""
        try:
            token_info = self.symbol_mapping.get(symbol)
            if not token_info:
                return []

            token, exchange = token_info

            # Format dates for SmartAPI
            from_date_str = from_date.strftime("%Y-%m-%d %H:%M")
            to_date_str = to_date.strftime("%Y-%m-%d %H:%M")

            # Map timeframe to SmartAPI format
            interval_map = {
                "5min": "FIVE_MINUTE",
                "15min": "FIFTEEN_MINUTE",
                "1hour": "ONE_HOUR",
                "1day": "ONE_DAY"
            }

            interval = interval_map.get(timeframe, "FIFTEEN_MINUTE")

            # Get historical data from SmartAPI
            response = self.smart_api.getCandleData({
                "exchange": exchange,
                "symboltoken": token,
                "interval": interval,
                "fromdate": from_date_str,
                "todate": to_date_str
            })

            if response and response.get('status') and response.get('data'):
                candles = []
                for data_point in response['data']:
                    try:
                        timestamp_str = data_point[0]
                        timestamp = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S%z")

                        candle = Candle(
                            timestamp=timestamp.replace(tzinfo=None),
                            open=float(data_point[1]),
                            high=float(data_point[2]),
                            low=float(data_point[3]),
                            close=float(data_point[4]),
                            volume=float(data_point[5])
                        )
                        candles.append(candle)
                    except Exception:
                        continue

                return candles

        except Exception as e:
            self.logger.warning(f"SmartAPI fetch failed for {symbol}: {e}")

        return []

    def _fetch_from_yfinance(self, symbol: str, timeframe: str, from_date: datetime, to_date: datetime) -> List[Candle]:
        """Fetch candles from YFinance"""
        try:
            yf_symbol = f"{symbol}.NS"

            # Map timeframe to YFinance format
            interval_map = {
                "5min": "5m",
                "15min": "15m",
                "1hour": "1h",
                "1day": "1d"
            }

            interval = interval_map.get(timeframe, "15m")

            # Fetch data
            ticker = yf.Ticker(yf_symbol)
            data = ticker.history(start=from_date, end=to_date, interval=interval, prepost=False, auto_adjust=True)

            if data.empty:
                return []

            import pytz
            candles = []
            ist_tz = pytz.timezone('Asia/Kolkata')

            for timestamp, row in data.iterrows():
                # Handle timezone-aware timestamps properly
                if hasattr(timestamp, 'tz_localize'):
                    # If timezone-aware, convert to IST then make naive
                    if timestamp.tz is not None:
                        timestamp_naive = timestamp.tz_convert(ist_tz).tz_localize(None)
                    else:
                        # If timezone-naive, assume it's already in IST
                        timestamp_naive = timestamp
                else:
                    # Fallback for other timestamp types
                    timestamp_naive = timestamp.to_pydatetime()
                    if hasattr(timestamp_naive, 'tzinfo') and timestamp_naive.tzinfo is not None:
                        timestamp_naive = timestamp_naive.replace(tzinfo=None)

                candle = Candle(
                    timestamp=timestamp_naive,
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume']) if 'Volume' in row else 0
                )
                candles.append(candle)

            return candles

        except Exception as e:
            self.logger.warning(f"YFinance fetch failed for {symbol}: {e}")

        return []

    def _update_memory_cache(self, symbol: str, timeframe: str, new_candles: List[Candle]):
        """
        Update memory cache with new candles for faster strategy analysis
        This is called BEFORE MongoDB storage for optimal performance
        """
        try:
            # Direct cache update if we have access to the market data service
            if self.market_data_service and hasattr(self.market_data_service, 'candle_cache'):
                # Update cache for different day ranges that might be requested
                for days in [1, 3, 5, 7]:
                    cache_key = f"{symbol}_{timeframe}_{days}"

                    # If this cache entry exists, invalidate it so it gets refreshed with new data
                    if cache_key in self.market_data_service.candle_cache:
                        # Remove old cache entry to force refresh with new data
                        del self.market_data_service.candle_cache[cache_key]
                        self.logger.debug(f"🗑️ CACHE_INVALIDATED: {cache_key} - will be refreshed with new data")

                self.logger.debug(f"🚀 CACHE_FIRST: Invalidated cache entries for {symbol} {timeframe} - strategies will get fresh data immediately")
            else:
                # Fallback notification system
                self.logger.debug(f"🚀 CACHE_FIRST: No direct cache access for {symbol} {timeframe} with {len(new_candles)} candles")
                self._notify_cache_update(symbol, timeframe, new_candles)

        except Exception as e:
            self.logger.error(f"Error updating memory cache for {symbol} {timeframe}: {e}")

    def _notify_cache_update(self, symbol: str, timeframe: str, new_candles: List[Candle]):
        """
        Notify that new candles are available for immediate cache update
        This ensures strategies get the latest data without waiting for MongoDB
        """
        try:
            # Create a cache key similar to EfficientMarketDataService
            cache_key = f"{symbol}_{timeframe}_3"  # Assuming 3 days default

            # Log the notification for monitoring
            self.logger.debug(f"📢 CACHE_NOTIFICATION: {cache_key} has {len(new_candles)} new candles available")

            # In a more sophisticated implementation, we could:
            # 1. Use a message queue (Redis/RabbitMQ) for cache invalidation
            # 2. Directly update shared memory cache
            # 3. Use observer pattern to notify subscribers
            # 4. Implement cache coherency protocols

        except Exception as e:
            self.logger.error(f"Error notifying cache update: {e}")

    def _save_candles_to_mongodb(self, symbol: str, timeframe: str, candles: List[Candle]):
        """Save candles to MongoDB"""
        try:
            collection_name = f"{timeframe}_candles"
            collection = self.db[collection_name]

            # Prepare documents
            documents = []
            for candle in candles:
                doc = {
                    "symbol": symbol,
                    "timestamp": candle.timestamp,
                    "open": candle.open,
                    "high": candle.high,
                    "low": candle.low,
                    "close": candle.close,
                    "volume": candle.volume
                }
                documents.append(doc)

            if documents:
                # Use upsert to avoid duplicates
                for doc in documents:
                    collection.replace_one(
                        {"symbol": symbol, "timestamp": doc["timestamp"]},
                        doc,
                        upsert=True
                    )

        except Exception as e:
            self.logger.error(f"Error saving candles to MongoDB: {e}")

    def close(self):
        """Close connections"""
        self.stop()
        if self.mongodb_client:
            self.mongodb_client.close()
