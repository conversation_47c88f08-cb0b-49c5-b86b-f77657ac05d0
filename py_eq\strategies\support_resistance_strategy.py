"""
Support/Resistance Bounce Strategy
Based on the Golang implementation
"""
import logging
from typing import List, Optional, Tuple, Dict

from models.candle import Candle
from models.signal import Signal
from models.order import Order, TransactionType
from services.order_service import OrderServiceInterface
from services.market_data_service import MarketDataServiceInterface
from utils.helpers import (
    is_market_open, calculate_rsi, calculate_quantity,
    calculate_average_volume, find_support_resistance_levels
)
from config.config import TradingConfig


class SupportResistanceStrategy:
    """Support/Resistance Bounce Strategy"""

    def __init__(
        self,
        logger: logging.Logger,
        market_data_service: MarketDataServiceInterface,
        order_service: OrderServiceInterface,
        config: TradingConfig
    ):
        self.logger = logger
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config

        # Strategy-specific data
        self.support_levels = {}
        self.resistance_levels = {}

    def analyze_signal(self, signal: Signal) -> Optional[Dict]:
        """
        Analyze a signal and return entry conditions without placing order
        Used by Enhanced Strategy Manager for virtual account trading
        """
        try:
            # Get historical data for analysis
            candles = self.market_data_service.get_historical_data(signal.symbol, signal.timeframe, days=3)
            if not candles or len(candles) < 50:
                return None

            # Calculate support and resistance levels using existing method
            self._identify_levels(signal.symbol, candles)
            support_levels = self.support_levels.get(signal.symbol, [])
            resistance_levels = self.resistance_levels.get(signal.symbol, [])

            if not support_levels and not resistance_levels:
                return None

            # Check entry conditions using existing method
            entry_result = self._check_entry_conditions(candles, signal.symbol)
            if not entry_result[4]:  # should_enter is False
                return None

            # Get entry details from the result tuple
            entry_price, stop_loss, target, direction, should_enter, reason = entry_result
            current_price = candles[-1].close

            # Convert direction to string format and use calculated values
            if direction == 1:
                direction_str = 'LONG'
            elif direction == -1:
                direction_str = 'SHORT'
            else:
                direction_str = 'LONG'  # Default to LONG

            return {
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'target': target,
                'direction': direction_str,
                'confidence': 0.7,  # Default confidence
                'support_levels': support_levels,
                'resistance_levels': resistance_levels,
                'reason': reason
            }

        except Exception as e:
            self.logger.error(f"Error analyzing signal for {signal.symbol}: {e}")
            return None

    def process_signal(self, signal: Signal) -> Optional[Order]:
        """Process a trading signal using Support/Resistance strategy"""

        # Check if market is open (using user's preferred function)
        if not is_market_open():
            self.logger.info(f"Market is closed. Skipping signal for {signal.symbol}")
            return None

        # Get symbol token and exchange
        token_info = self.market_data_service.get_symbol_token(signal.symbol)
        if not token_info:
            self.logger.error(f"No token found for symbol: {signal.symbol}")
            return None

        token, exchange = token_info

        # Get last price to verify symbol exists
        last_price = self.market_data_service.get_last_price(signal.symbol)
        if last_price is None:
            self.logger.error(f"Could not get last price for {signal.symbol}")
            return None

        # Get historical data (only need 3 days for intraday trading)
        candles = self.market_data_service.get_historical_data(
            signal.symbol, signal.timeframe, days=3
        )

        if len(candles) < 50:
            self.logger.warning(f"Not enough historical data for {signal.symbol}: {len(candles)} candles")
            return None

        # Calculate technical indicators
        candles_with_indicators = self._calculate_indicators(candles)

        # Identify support and resistance levels
        self._identify_levels(signal.symbol, candles_with_indicators)

        # Check entry conditions
        entry_result = self._check_entry_conditions(candles_with_indicators, signal.symbol)

        if not entry_result[4]:  # should_enter is False
            self.logger.info(f"No entry conditions met for {signal.symbol}: {entry_result[5]}")
            return None

        entry_price, stop_loss, target, direction, should_enter, reason = entry_result

        # Calculate quantity using the formula: 800/(entry_price - stop_loss) * 4x margin
        quantity = calculate_quantity(
            entry_price,
            stop_loss,
            self.config.max_risk_per_trade,
            self.config.margin_multiplier
        )

        # Determine transaction type
        transaction_type = TransactionType.BUY if direction > 0 else TransactionType.SELL

        self.logger.info(
            f"Placing {transaction_type.value} order for {signal.symbol}: "
            f"Entry: {entry_price:.2f}, Stop-Loss: {stop_loss:.2f}, "
            f"Target: {target:.2f}, Quantity: {quantity}"
        )

        # Place order
        order = self.order_service.place_order(
            symbol=signal.symbol,
            token=token,
            exchange=exchange,
            transaction_type=transaction_type,
            entry_price=entry_price,
            stop_loss=stop_loss,
            target=target,
            quantity=quantity
        )

        if order:
            self.logger.info(f"Order placed successfully for {signal.symbol}: {order.order_id}")
        else:
            self.logger.error(f"Failed to place order for {signal.symbol}")

        return order

    def _calculate_indicators(self, candles: List[Candle]) -> List[Candle]:
        """Calculate technical indicators for candles"""
        if len(candles) < 20:
            return candles

        # Extract close prices and volumes
        close_prices = [candle.close for candle in candles]
        volumes = [candle.volume for candle in candles]

        # Calculate RSI
        rsi_values = calculate_rsi(close_prices, self.config.rsi_period)

        # Calculate average volume
        avg_volume_values = calculate_average_volume(volumes, self.config.volume_period)

        # Update candles with indicators
        result_candles = candles.copy()

        for i, candle in enumerate(result_candles):
            # RSI (starts from index 14)
            if i >= self.config.rsi_period and i - self.config.rsi_period < len(rsi_values):
                candle.rsi = rsi_values[i - self.config.rsi_period]

            # Average Volume (starts from index 9)
            if i >= self.config.volume_period - 1 and i - self.config.volume_period + 1 < len(avg_volume_values):
                candle.avg_volume = avg_volume_values[i - self.config.volume_period + 1]

        return result_candles

    def _identify_levels(self, symbol: str, candles: List[Candle]):
        """Identify support and resistance levels with volume weighting"""
        if len(candles) < 50:
            self.logger.warning(f"Not enough candles to identify levels for {symbol}: {len(candles)}")
            return

        # Extract highs, lows, and volumes
        highs = [candle.high for candle in candles]
        lows = [candle.low for candle in candles]
        volumes = [candle.volume for candle in candles]

        # Find support and resistance levels with volume weighting
        support_levels, resistance_levels = find_support_resistance_levels(highs, lows, volumes)

        self.support_levels[symbol] = support_levels
        self.resistance_levels[symbol] = resistance_levels

        self.logger.info(
            f"Identified {len(support_levels)} support levels and "
            f"{len(resistance_levels)} resistance levels for {symbol}"
        )

        if support_levels:
            self.logger.info(f"Support levels for {symbol}: {[f'{level:.2f}' for level in support_levels]}")
        if resistance_levels:
            self.logger.info(f"Resistance levels for {symbol}: {[f'{level:.2f}' for level in resistance_levels]}")

    def _check_entry_conditions(self, candles: List[Candle], symbol: str) -> Tuple[float, float, float, int, bool, str]:
        """
        Check if entry conditions are met for Support/Resistance strategy

        Returns:
            Tuple of (entry_price, stop_loss, target, direction, should_enter, reason)
        """
        if len(candles) < 50:
            return 0, 0, 0, 0, False, f"not enough candles: have {len(candles)}, need at least 50"

        # Get the last two candles
        current = candles[-1]
        previous = candles[-2]

        # Check if we have all required indicators
        if current.rsi is None or current.avg_volume is None:
            return 0, 0, 0, 0, False, "missing technical indicators"

        # Get support and resistance levels
        support_levels = self.support_levels.get(symbol, [])
        resistance_levels = self.resistance_levels.get(symbol, [])

        # Check for volume confirmation (made more lenient - 80% of average volume)
        volume_confirmation = current.volume > (current.avg_volume * 0.8)

        # Check for RSI confirmation
        rsi_oversold = current.rsi < self.config.rsi_oversold
        rsi_overbought = current.rsi > self.config.rsi_overbought

        # Log indicator values for debugging
        self.logger.info(
            f"S/R Indicators for {symbol}: RSI={current.rsi:.2f}, "
            f"Volume={current.volume:.0f}, AvgVolume={current.avg_volume:.0f}"
        )

        self.logger.info(
            f"S/R Conditions for {symbol}: RSI Oversold={rsi_oversold}, "
            f"RSI Overbought={rsi_overbought}, Volume Confirmation={volume_confirmation}"
        )

        # Check if we have any support or resistance levels
        if not support_levels and not resistance_levels:
            return 0, 0, 0, 0, False, "no support or resistance levels identified"

        # Check for support bounce (bullish)
        for support in support_levels:
            # Check if price bounced off support
            support_touched = (previous.low <= support * 1.005 and
                             previous.low >= support * 0.995)
            bullish_candle = current.close > current.open

            self.logger.info(
                f"Support level {support:.2f}: Touched={support_touched}, "
                f"Bullish Candle={bullish_candle}"
            )

            if support_touched and bullish_candle and volume_confirmation and rsi_oversold:
                entry_price = current.close

                # Stop-loss: Below the support level
                stop_loss = support * 0.99  # 1% below support

                # Calculate risk
                risk = entry_price - stop_loss

                # Calculate target based on risk-reward ratio
                target = entry_price + (risk * self.config.risk_reward_ratio)

                return entry_price, stop_loss, target, 1, True, "bullish support bounce conditions met"

        # Check for resistance rejection (bearish)
        for resistance in resistance_levels:
            # Check if price rejected at resistance
            resistance_touched = (previous.high >= resistance * 0.995 and
                                previous.high <= resistance * 1.005)
            bearish_candle = current.close < current.open

            self.logger.info(
                f"Resistance level {resistance:.2f}: Touched={resistance_touched}, "
                f"Bearish Candle={bearish_candle}"
            )

            if resistance_touched and bearish_candle and volume_confirmation and rsi_overbought:
                entry_price = current.close

                # Stop-loss: Above the resistance level
                stop_loss = resistance * 1.01  # 1% above resistance

                # Calculate risk
                risk = stop_loss - entry_price

                # Calculate target based on risk-reward ratio
                target = entry_price - (risk * self.config.risk_reward_ratio)

                return entry_price, stop_loss, target, -1, True, "bearish resistance rejection conditions met"

        # Determine the reason why entry conditions weren't met
        if support_levels and not rsi_oversold:
            reason = "support levels found but RSI not oversold"
        elif resistance_levels and not rsi_overbought:
            reason = "resistance levels found but RSI not overbought"
        elif not volume_confirmation:
            reason = "volume not confirming"
        else:
            reason = "price not touching support/resistance levels"

        return 0, 0, 0, 0, False, reason
