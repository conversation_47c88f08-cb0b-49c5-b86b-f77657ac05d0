#!/usr/bin/env python3
"""
WebSocket Diagnostics and Testing Script
Run comprehensive diagnostics and test the enhanced WebSocket solutions
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Local imports
from utils.websocket_diagnostics import WebSocketDiagnostics
from utils.robust_websocket_manager import RobustWebSocketManager
from utils.dynamic_stock_selection_workflow import DynamicStockSelectionWorkflow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'websocket_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)

logger = logging.getLogger(__name__)

class WebSocketTestSuite:
    """Comprehensive WebSocket testing suite"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run all WebSocket tests and diagnostics"""
        print("\n" + "=" * 80)
        print("🚀 WEBSOCKET COMPREHENSIVE TEST SUITE")
        print("=" * 80)
        
        # Test 1: Basic diagnostics
        await self._test_basic_diagnostics()
        
        # Test 2: Enhanced WebSocket manager
        await self._test_enhanced_websocket_manager()
        
        # Test 3: Dynamic stock selection workflow
        await self._test_dynamic_workflow()
        
        # Generate final report
        self._generate_final_report()
    
    async def _test_basic_diagnostics(self):
        """Test basic WebSocket diagnostics"""
        print("\n📋 TEST 1: Basic WebSocket Diagnostics")
        print("-" * 50)
        
        try:
            diagnostics = WebSocketDiagnostics()
            results = await diagnostics.run_full_diagnostics()
            
            self.test_results['basic_diagnostics'] = {
                'status': 'completed',
                'results': results,
                'recommendations_count': len(results.get('recommendations', []))
            }
            
            # Check if any critical issues found
            env_issues = not all([
                results.get('environment', {}).get('smartapi_available', False),
                results.get('environment', {}).get('api_key_set', False),
                results.get('environment', {}).get('totp_generation', False)
            ])
            
            auth_issues = not results.get('authentication', {}).get('login_successful', False)
            network_issues = not results.get('network', {}).get('internet_connectivity', False)
            
            if env_issues or auth_issues or network_issues:
                print("   ⚠️ Critical issues detected - check diagnostics output above")
                self.test_results['basic_diagnostics']['critical_issues'] = True
            else:
                print("   ✅ Basic diagnostics passed - no critical issues")
                self.test_results['basic_diagnostics']['critical_issues'] = False
                
        except Exception as e:
            print(f"   ❌ Diagnostics test failed: {e}")
            self.test_results['basic_diagnostics'] = {
                'status': 'failed',
                'error': str(e)
            }
    
    async def _test_enhanced_websocket_manager(self):
        """Test the enhanced WebSocket manager"""
        print("\n🔧 TEST 2: Enhanced WebSocket Manager")
        print("-" * 50)
        
        try:
            # Configuration for testing
            config = {
                'max_retry_attempts': 3,
                'retry_delay_base': 2,
                'connection_timeout': 10,
                'heartbeat_interval': 30
            }
            
            manager = RobustWebSocketManager(config)
            
            # Test authentication
            print("   🔐 Testing authentication...")
            auth_success = await manager.authenticate()
            
            if auth_success:
                print("   ✅ Authentication successful")
                
                # Test connection
                print("   📡 Testing WebSocket connection...")
                connection_success = await manager.connect()
                
                if connection_success:
                    print("   ✅ WebSocket connection successful")
                    
                    # Test subscription (with dummy symbols)
                    print("   📊 Testing symbol subscription...")
                    test_symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
                    subscription_success = await manager.subscribe_symbols(test_symbols)
                    
                    if subscription_success:
                        print("   ✅ Symbol subscription successful")
                    else:
                        print("   ⚠️ Symbol subscription failed (may be normal in test)")
                    
                    # Wait a moment to receive data
                    print("   ⏳ Waiting for market data...")
                    await asyncio.sleep(5)
                    
                    # Get status
                    status = manager.get_connection_status()
                    print(f"   📈 Connection status: {status['state']}")
                    print(f"   📊 Messages received: {status['metrics']['total_messages_received']}")
                    
                    # Disconnect
                    await manager.disconnect()
                    print("   ✅ Disconnection successful")
                    
                    self.test_results['enhanced_websocket'] = {
                        'status': 'success',
                        'authentication': True,
                        'connection': True,
                        'subscription': subscription_success,
                        'final_status': status
                    }
                    
                else:
                    print("   ❌ WebSocket connection failed")
                    self.test_results['enhanced_websocket'] = {
                        'status': 'partial_success',
                        'authentication': True,
                        'connection': False
                    }
            else:
                print("   ❌ Authentication failed")
                self.test_results['enhanced_websocket'] = {
                    'status': 'failed',
                    'authentication': False
                }
                
        except Exception as e:
            print(f"   ❌ Enhanced WebSocket test failed: {e}")
            self.test_results['enhanced_websocket'] = {
                'status': 'error',
                'error': str(e)
            }
    
    async def _test_dynamic_workflow(self):
        """Test the dynamic stock selection workflow"""
        print("\n🎯 TEST 3: Dynamic Stock Selection Workflow")
        print("-" * 50)
        
        try:
            # Configuration for testing
            config = {
                'websocket': {
                    'max_retry_attempts': 3,
                    'retry_delay_base': 2,
                    'connection_timeout': 10
                },
                'initial_monitoring_minutes': 1,  # Short for testing
                'reselection_interval_hours': 0.1,  # Short for testing
                'selection_criteria': {
                    'max_stocks': 10,
                    'min_market_cap': 'Mid'
                }
            }
            
            workflow = DynamicStockSelectionWorkflow(config)
            
            # Set up callbacks to track events
            stocks_selected = False
            market_data_received = False
            
            def on_stocks_selected(stocks, market_condition):
                nonlocal stocks_selected
                stocks_selected = True
                print(f"   📈 Stocks selected: {len(stocks)} stocks")
                print(f"   🌡️ Market condition: {market_condition.trend_direction}")
            
            def on_market_data(data):
                nonlocal market_data_received
                market_data_received = True
            
            workflow.set_callbacks(
                on_stocks_selected=on_stocks_selected,
                on_market_data=on_market_data
            )
            
            print("   🚀 Starting workflow (limited test)...")
            
            # Start workflow with timeout for testing
            try:
                workflow_task = asyncio.create_task(workflow.start_workflow())
                await asyncio.wait_for(workflow_task, timeout=30)  # 30 second test
            except asyncio.TimeoutError:
                print("   ⏰ Workflow test timeout (expected for testing)")
                await workflow.stop_workflow()
            
            # Get final status
            status = workflow.get_workflow_status()
            print(f"   📊 Final status: {status['current_phase']}")
            print(f"   🔄 Monitoring active: {status['monitoring_active']}")
            
            self.test_results['dynamic_workflow'] = {
                'status': 'completed',
                'stocks_selected': stocks_selected,
                'market_data_received': market_data_received,
                'final_status': status
            }
            
        except Exception as e:
            print(f"   ❌ Dynamic workflow test failed: {e}")
            self.test_results['dynamic_workflow'] = {
                'status': 'error',
                'error': str(e)
            }
    
    def _generate_final_report(self):
        """Generate final test report"""
        print("\n" + "=" * 80)
        print("📊 FINAL TEST REPORT")
        print("=" * 80)
        
        # Summary
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') in ['completed', 'success'])
        
        print(f"\n📈 Test Summary: {successful_tests}/{total_tests} tests completed successfully")
        
        # Detailed results
        for test_name, result in self.test_results.items():
            status = result.get('status', 'unknown')
            status_icon = {
                'completed': '✅',
                'success': '✅', 
                'partial_success': '⚠️',
                'failed': '❌',
                'error': '❌'
            }.get(status, '❓')
            
            print(f"\n{status_icon} {test_name.replace('_', ' ').title()}: {status}")
            
            if 'error' in result:
                print(f"   Error: {result['error']}")
            
            if test_name == 'basic_diagnostics' and result.get('critical_issues'):
                print("   ⚠️ Critical issues detected - review diagnostics output")
            
            if test_name == 'enhanced_websocket':
                auth = result.get('authentication', False)
                conn = result.get('connection', False)
                print(f"   Authentication: {'✅' if auth else '❌'}")
                print(f"   Connection: {'✅' if conn else '❌'}")
        
        # Recommendations
        print("\n💡 Recommendations:")
        
        basic_diag = self.test_results.get('basic_diagnostics', {})
        if basic_diag.get('critical_issues'):
            print("   1. Fix critical issues identified in basic diagnostics")
        
        enhanced_ws = self.test_results.get('enhanced_websocket', {})
        if enhanced_ws.get('status') == 'failed':
            print("   2. Check SmartAPI credentials and network connectivity")
        elif enhanced_ws.get('status') == 'partial_success':
            print("   2. WebSocket connection issues - check firewall/network settings")
        
        if successful_tests == total_tests:
            print("   ✅ All tests passed! WebSocket system is working properly.")
        else:
            print("   ⚠️ Some tests failed - review the issues above")
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"websocket_test_report_{timestamp}.json"
        
        import json
        with open(report_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_file}")

async def main():
    """Main test execution"""
    print("🔍 WebSocket Diagnostics and Testing Suite")
    print("This will test your WebSocket connection and the enhanced solutions")
    print("\nPress Ctrl+C to stop at any time...")
    
    try:
        test_suite = WebSocketTestSuite()
        await test_suite.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Test suite error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
