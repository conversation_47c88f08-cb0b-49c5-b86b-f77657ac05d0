"""
Yahoo Finance service for downloading historical data to fill gaps
"""
import logging
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict

from models.candle import Candle
from services.centralized_mapping_client import CentralizedMappingClient


class YFinanceService:
    """Yahoo Finance service for historical data download"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

        # Initialize centralized mapping client
        self.mapping_client = CentralizedMappingClient(logger=logger)

        # Legacy symbol mapping from NSE to Yahoo Finance format (for fallback)
        self._legacy_symbol_mapping = {
            'RELIANCE': 'RELIANCE.NS',
            'TCS': 'TCS.NS',
            'HDFCBANK': 'HDFCBANK.NS',
            'INFY': 'INFY.NS',
            'ICICIBANK': 'ICICIBANK.NS',
            'HINDUNILVR': 'HINDUNILVR.NS',
            'SBIN': 'SBIN.NS',
            'BAJFINANCE': 'BAJFINANCE.NS',
            'BHARTIARTL': 'BHARTIARTL.NS',
            'KOTAKBANK': 'KOTAKBANK.NS',
            'ITC': 'ITC.NS',
            'ASIANPAINT': 'ASIANPAINT.NS',
            'AXISBANK': 'AXISBANK.NS',
            'MARUTI': 'MARUTI.NS',
            'TATAMOTORS': 'TATAMOTORS.NS',
            'NESTLEIND': 'NESTLEIND.NS',
            'WIPRO': 'WIPRO.NS',
            'HCLTECH': 'HCLTECH.NS',
            'SUNPHARMA': 'SUNPHARMA.NS',
            'ULTRACEMCO': 'ULTRACEMCO.NS',
            'LT': 'LT.NS',
            'TECHM': 'TECHM.NS',
            'POWERGRID': 'POWERGRID.NS',
            'NTPC': 'NTPC.NS',
            'COALINDIA': 'COALINDIA.NS',
            'ONGC': 'ONGC.NS',
            'IOC': 'IOC.NS',
            'BPCL': 'BPCL.NS',
            'GRASIM': 'GRASIM.NS',
            'JSWSTEEL': 'JSWSTEEL.NS',
            'TATASTEEL': 'TATASTEEL.NS',
            'HINDALCO': 'HINDALCO.NS',
            'ADANIPORTS': 'ADANIPORTS.NS',
            'APOLLOHOSP': 'APOLLOHOSP.NS',
            'DRREDDY': 'DRREDDY.NS',
            'CIPLA': 'CIPLA.NS',
            'DIVISLAB': 'DIVISLAB.NS',
            'BRITANNIA': 'BRITANNIA.NS',
            'TITAN': 'TITAN.NS',
            'BAJAJFINSV': 'BAJAJFINSV.NS',
            'HDFCLIFE': 'HDFCLIFE.NS',
            'SBILIFE': 'SBILIFE.NS',
            'EICHERMOT': 'EICHERMOT.NS',
            'HEROMOTOCO': 'HEROMOTOCO.NS',
            'M&M': 'M&M.NS',
            'BAJAJ-AUTO': 'BAJAJ-AUTO.NS',
            # Additional mappings for stocks in stocks_to_monitor.csv
            'INDHOTEL': 'INDHOTEL.NS',
            'CUMMINSIND': 'CUMMINSIND.NS',
            'SIEMENS': 'SIEMENS.NS',
            'AUBANK': 'AUBANK.NS',
            'APOLLOTYRE': 'APOLLOTYRE.NS',
            'NMDC': 'NMDC.NS',
            'GODREJPROP': 'GODREJPROP.NS',
            'ASHOKLEY': 'ASHOKLEY.NS',
            'GODREJCP': 'GODREJCP.NS',
            'ICICIGI': 'ICICIGI.NS'
        }

    def get_yahoo_symbol(self, nse_symbol: str) -> Optional[str]:
        """Convert NSE symbol to Yahoo Finance symbol using centralized mapping"""
        try:
            # Try centralized mapping first
            yahoo_symbol = self.mapping_client.get_yahoo_symbol(nse_symbol)
            if yahoo_symbol:
                return yahoo_symbol

            # Fallback to legacy mapping
            legacy_result = self._legacy_symbol_mapping.get(nse_symbol)
            if legacy_result:
                self.logger.debug(f"Using legacy Yahoo mapping for {nse_symbol}: {legacy_result}")
                return legacy_result

            # Final fallback: construct default format
            default_symbol = f"{nse_symbol}.NS"
            self.logger.debug(f"Using default Yahoo format for {nse_symbol}: {default_symbol}")
            return default_symbol

        except Exception as e:
            self.logger.error(f"Error getting Yahoo symbol for {nse_symbol}: {e}")
            # Fallback to legacy mapping on error
            return self._legacy_symbol_mapping.get(nse_symbol, f"{nse_symbol}.NS")

    def download_historical_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        interval: str = "15m"
    ) -> List[Candle]:
        """
        Download historical data from Yahoo Finance

        Args:
            symbol: NSE symbol (e.g., 'RELIANCE')
            start_date: Start date for data
            end_date: End date for data
            interval: Data interval ('5m', '15m', '1h', '1d')

        Returns:
            List of Candle objects
        """
        yahoo_symbol = self.get_yahoo_symbol(symbol)
        if not yahoo_symbol:
            self.logger.warning(f"No Yahoo Finance mapping found for symbol: {symbol}")
            return []

        try:
            self.logger.info(f"Downloading data for {symbol} ({yahoo_symbol}) from {start_date} to {end_date}")

            # Download data from Yahoo Finance
            ticker = yf.Ticker(yahoo_symbol)

            # Adjust interval format for yfinance
            yf_interval = self._convert_interval(interval)

            # Download the data
            data = ticker.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d'),
                interval=yf_interval,
                prepost=False,  # Don't include pre/post market data
                auto_adjust=True,
                back_adjust=False
            )

            if data.empty:
                self.logger.warning(f"No data downloaded for {symbol}")
                return []

            # Convert to Candle objects
            candles = []
            for timestamp, row in data.iterrows():
                try:
                    # Handle timezone-aware timestamps
                    if hasattr(timestamp, 'tz_localize'):
                        timestamp = timestamp.tz_localize(None)
                    elif hasattr(timestamp, 'tz_convert'):
                        timestamp = timestamp.tz_convert(None).replace(tzinfo=None)

                    candle = Candle(
                        timestamp=timestamp,
                        open=float(row['Open']),
                        high=float(row['High']),
                        low=float(row['Low']),
                        close=float(row['Close']),
                        volume=float(row['Volume'])
                    )
                    candles.append(candle)

                except Exception as e:
                    self.logger.warning(f"Error processing candle data for {symbol}: {e}")
                    continue

            self.logger.info(f"Downloaded {len(candles)} candles for {symbol}")
            return candles

        except Exception as e:
            self.logger.error(f"Error downloading data for {symbol}: {e}")
            return []

    def _convert_interval(self, interval: str) -> str:
        """Convert interval format to yfinance format"""
        interval_mapping = {
            '5m': '5m',
            '5min': '5m',
            '15m': '15m',
            '15min': '15m',
            '1h': '1h',
            '1hour': '1h',
            '1d': '1d',
            '1day': '1d'
        }
        return interval_mapping.get(interval, '15m')

    def download_multiple_symbols(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        interval: str = "15m"
    ) -> Dict[str, List[Candle]]:
        """
        Download historical data for multiple symbols

        Args:
            symbols: List of NSE symbols
            start_date: Start date for data
            end_date: End date for data
            interval: Data interval

        Returns:
            Dictionary mapping symbol to list of candles
        """
        results = {}

        for symbol in symbols:
            try:
                candles = self.download_historical_data(symbol, start_date, end_date, interval)
                if candles:
                    results[symbol] = candles
                    self.logger.info(f"Successfully downloaded data for {symbol}: {len(candles)} candles")
                else:
                    self.logger.warning(f"No data downloaded for {symbol}")

            except Exception as e:
                self.logger.error(f"Error downloading data for {symbol}: {e}")
                continue

        return results

    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get the latest price for a symbol"""
        yahoo_symbol = self.get_yahoo_symbol(symbol)
        if not yahoo_symbol:
            return None

        try:
            ticker = yf.Ticker(yahoo_symbol)
            info = ticker.info

            # Try different price fields
            price_fields = ['regularMarketPrice', 'currentPrice', 'previousClose']

            for field in price_fields:
                if field in info and info[field]:
                    return float(info[field])

            # Fallback: get latest from history
            data = ticker.history(period="1d", interval="1m")
            if not data.empty:
                return float(data['Close'].iloc[-1])

            return None

        except Exception as e:
            self.logger.error(f"Error getting latest price for {symbol}: {e}")
            return None

    def validate_symbol(self, symbol: str) -> bool:
        """Validate if a symbol exists and has data"""
        yahoo_symbol = self.get_yahoo_symbol(symbol)
        if not yahoo_symbol:
            return False

        try:
            ticker = yf.Ticker(yahoo_symbol)
            info = ticker.info

            # Check if symbol has basic info
            return 'symbol' in info or 'shortName' in info

        except Exception as e:
            self.logger.warning(f"Symbol validation failed for {symbol}: {e}")
            return False

    def add_symbol_mapping(self, nse_symbol: str, yahoo_symbol: str):
        """Add a new symbol mapping to legacy mapping (for backward compatibility)"""
        self._legacy_symbol_mapping[nse_symbol] = yahoo_symbol
        self.logger.info(f"Added legacy symbol mapping: {nse_symbol} -> {yahoo_symbol}")

    def get_available_symbols(self) -> List[str]:
        """Get list of available NSE symbols from legacy mapping"""
        return list(self._legacy_symbol_mapping.keys())

    def download_gap_data(
        self,
        symbol: str,
        existing_candles: List[Candle],
        required_start: datetime,
        required_end: datetime,
        interval: str = "15m"
    ) -> List[Candle]:
        """
        Download data to fill gaps in existing candles

        Args:
            symbol: NSE symbol
            existing_candles: List of existing candles
            required_start: Required start date
            required_end: Required end date
            interval: Data interval

        Returns:
            List of candles to fill gaps
        """
        if not existing_candles:
            # No existing data, download all
            return self.download_historical_data(symbol, required_start, required_end, interval)

        # Sort existing candles by timestamp
        existing_candles.sort(key=lambda x: x.timestamp)

        gap_candles = []

        # Check for gap at the beginning
        if existing_candles[0].timestamp > required_start:
            gap_end = existing_candles[0].timestamp - timedelta(minutes=1)
            gap_data = self.download_historical_data(symbol, required_start, gap_end, interval)
            gap_candles.extend(gap_data)
            self.logger.info(f"Downloaded {len(gap_data)} candles for beginning gap in {symbol}")

        # Check for gaps in the middle
        for i in range(len(existing_candles) - 1):
            current_candle = existing_candles[i]
            next_candle = existing_candles[i + 1]

            # Calculate expected next timestamp based on interval
            if interval in ['5m', '5min']:
                expected_next = current_candle.timestamp + timedelta(minutes=5)
            elif interval in ['15m', '15min']:
                expected_next = current_candle.timestamp + timedelta(minutes=15)
            else:
                expected_next = current_candle.timestamp + timedelta(minutes=15)

            # If there's a gap larger than the interval
            if next_candle.timestamp > expected_next + timedelta(minutes=5):
                gap_start = current_candle.timestamp + timedelta(minutes=1)
                gap_end = next_candle.timestamp - timedelta(minutes=1)
                gap_data = self.download_historical_data(symbol, gap_start, gap_end, interval)
                gap_candles.extend(gap_data)
                self.logger.info(f"Downloaded {len(gap_data)} candles for middle gap in {symbol}")

        # Check for gap at the end
        if existing_candles[-1].timestamp < required_end:
            gap_start = existing_candles[-1].timestamp + timedelta(minutes=1)
            gap_data = self.download_historical_data(symbol, gap_start, required_end, interval)
            gap_candles.extend(gap_data)
            self.logger.info(f"Downloaded {len(gap_data)} candles for ending gap in {symbol}")

        return gap_candles
