"""
Telegram Bot Configuration for Trading Reports

This module contains configuration settings for the Telegram bot
that sends trading reports and provides real-time trading updates.
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class TelegramConfig:
    """Configuration class for Telegram bot settings"""
    
    # Bot credentials
    bot_token: str = ""
    chat_id: str = ""  # Your personal chat ID or group chat ID
    
    # Bot settings
    bot_name: str = "TradingReportBot"
    bot_username: str = "@your_trading_bot"
    
    # Report settings
    send_daily_reports: bool = True
    send_weekly_reports: bool = True
    send_monthly_reports: bool = True
    send_trade_alerts: bool = True
    send_balance_updates: bool = True
    
    # Timing settings (24-hour format)
    daily_report_time: str = "16:00"  # After market close
    weekly_report_day: int = 5  # Friday (0=Monday, 6=Sunday)
    weekly_report_time: str = "17:00"
    monthly_report_day: int = -1  # Last day of month
    monthly_report_time: str = "18:00"
    
    # Alert thresholds
    max_daily_loss_alert: float = 5000.0  # Alert if daily loss exceeds this
    max_drawdown_alert: float = 10000.0  # Alert if drawdown exceeds this
    min_balance_alert: float = 300000.0  # Alert if balance falls below this
    
    # Chart settings
    chart_width: int = 800
    chart_height: int = 600
    chart_dpi: int = 100
    chart_style: str = "seaborn-v0_8"  # matplotlib style
    
    # Message settings
    max_message_length: int = 4096  # Telegram limit
    use_markdown: bool = True
    use_html: bool = False
    
    # Authorized users (chat IDs that can interact with the bot)
    authorized_users: List[str] = None
    
    # Admin users (can access all commands)
    admin_users: List[str] = None
    
    def __post_init__(self):
        """Initialize default values after object creation"""
        if self.authorized_users is None:
            self.authorized_users = []
        if self.admin_users is None:
            self.admin_users = []
        
        # Load from environment variables if available
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN', self.bot_token)
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID', self.chat_id)
        
        # Load authorized users from environment
        auth_users_env = os.getenv('TELEGRAM_AUTHORIZED_USERS', '')
        if auth_users_env:
            self.authorized_users = auth_users_env.split(',')
        
        admin_users_env = os.getenv('TELEGRAM_ADMIN_USERS', '')
        if admin_users_env:
            self.admin_users = admin_users_env.split(',')
    
    def is_authorized(self, user_id: str) -> bool:
        """Check if user is authorized to use the bot"""
        return str(user_id) in self.authorized_users or str(user_id) in self.admin_users
    
    def is_admin(self, user_id: str) -> bool:
        """Check if user is an admin"""
        return str(user_id) in self.admin_users
    
    def reload_from_env(self):
        """Reload configuration from environment variables"""
        # Load from environment variables if available
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN', self.bot_token)
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID', self.chat_id)

        # Load authorized users from environment
        auth_users_env = os.getenv('TELEGRAM_AUTHORIZED_USERS', '')
        if auth_users_env:
            self.authorized_users = auth_users_env.split(',')

        admin_users_env = os.getenv('TELEGRAM_ADMIN_USERS', '')
        if admin_users_env:
            self.admin_users = admin_users_env.split(',')

    def validate(self) -> bool:
        """Validate configuration settings"""
        if not self.bot_token:
            raise ValueError("Bot token is required")

        if not self.chat_id:
            raise ValueError("Chat ID is required")

        if not self.authorized_users and not self.admin_users:
            raise ValueError("At least one authorized user is required")

        return True


# Default configuration instance
telegram_config = TelegramConfig()


# Bot command descriptions for BotFather
BOT_COMMANDS = {
    "start": "🚀 Start the bot and show main menu",
    "status": "📊 Current trading status and live P&L",
    "daily": "📈 Today's trading report with charts",
    "weekly": "📅 Weekly performance summary",
    "monthly": "📆 Monthly analysis report",
    "trades": "📋 Recent trades list",
    "balance": "💰 Account balance and risk metrics",
    "strategies": "🎯 Strategy performance breakdown",
    "alerts": "🔔 Set up trading alerts",
    "help": "❓ Show available commands",
    "settings": "⚙️ Bot settings (admin only)"
}

# Emoji mappings for different message types
EMOJIS = {
    "profit": "💚",
    "loss": "❤️",
    "neutral": "💙",
    "warning": "⚠️",
    "error": "❌",
    "success": "✅",
    "info": "ℹ️",
    "chart": "📊",
    "money": "💰",
    "trend_up": "📈",
    "trend_down": "📉",
    "fire": "🔥",
    "rocket": "🚀",
    "target": "🎯",
    "clock": "🕐",
    "calendar": "📅",
    "bell": "🔔"
}

# Message templates
MESSAGE_TEMPLATES = {
    "welcome": """
🚀 *Welcome to Trading Report Bot!*

I'll help you monitor your trading performance with:
• 📊 Real-time trading status
• 📈 Daily/Weekly/Monthly reports
• 🔔 Trade alerts and notifications
• 💰 Balance and risk monitoring

Use /help to see all available commands.
""",
    
    "unauthorized": """
❌ *Access Denied*

You are not authorized to use this bot.
Please contact the administrator.
""",
    
    "error": """
❌ *Error Occurred*

Something went wrong: {error}
Please try again or contact support.
""",
    
    "no_data": """
ℹ️ *No Data Available*

No trading data found for the requested period.
""",
    
    "processing": """
⏳ *Processing...*

Generating your report, please wait...
"""
}
