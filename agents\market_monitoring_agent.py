#!/usr/bin/env python3
"""
Market Monitoring Agent - Real-time Market Data Tracking and Strategy Triggering

Features:
[DEBUG] 1. Live Market Data Tracking
- Real-time tick/quote stream for tracked symbols
- 1-min / 5-min candle builder for intra-bar analytics
- Live computation of EMA, VWAP, RSI, MACD, SuperTrend, etc.
- Market breadth analysis (% of stocks above 20 EMA)
- Unusual volume detection
- VWAP deviation analysis

🧠 2. Contextual Environment Detection
- Market regime detection (Bull/Bear/Sideways)
- Volatility analysis (Low/High)
- Liquidity checks
- Cross-stock correlation matrix
- News/events integration (optional)

🧪 3. Strategy Triggering Logic
- AI model integration for strategy recommendation
- Entry condition monitoring
- Execution filters (slippage, liquidity, volatility)
- Dynamic R:R ratio based on market regime

📣 4. Notifications & Logging
- Telegram/Slack notifications for trade signals
- Signal logging with full context
- Dashboard feed for real-time monitoring
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Add current directory and parent directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Technical analysis with Polars extension
try:
    import polars_talib as ta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    print("[WARN]  polars-talib not installed. Install with: pip install polars-talib")
    POLARS_TALIB_AVAILABLE = False

# Statistical functions using PyArrow compute
# Removed scipy dependency for better performance

# WebSocket and API clients
import websocket
import requests
import pyotp

# Rich imports for enhanced terminal output
try:
    from rich.console import Console
    from rich.logging import RichHandler
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeElapsedColumn
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich.live import Live
    from rich.layout import Layout
    from rich.status import Status
    from rich import print as rprint
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    # Print a warning if Rich is not available, but don't exit
    print("Rich library not available. Install with: pip install rich for enhanced output.")

# Notifications
import telegram
from telegram import Bot
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Enhanced WebSocket Manager
try:
    from utils.robust_websocket_manager import RobustWebSocketManager
except ImportError:
    print("[WARN] Robust WebSocket Manager not found. Using fallback.")
    RobustWebSocketManager = None

# Enhanced WebSocket Service
try:
    from utils.enhanced_websocket_service import EnhancedWebSocketService
except ImportError:
    print("[WARN] Enhanced WebSocket Service not found.")
    EnhancedWebSocketService = None

# AI Training Agent integration
try:
    from ai_training_agent import AITrainingAgent
except ImportError:
    print("[WARN]  AI Training Agent not found. Strategy classification will be disabled.")
    AITrainingAgent = None

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class MarketTick:
    """Real-time market tick data"""
    symbol: str
    token: str
    timestamp: datetime
    ltp: float
    volume: int
    open_price: float = None
    high_price: float = None
    low_price: float = None
    close_price: float = None
    
@dataclass
class OHLCV:
    """OHLC candle data"""
    symbol: str
    timestamp: datetime
    timeframe: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    
@dataclass
class MarketIndicators:
    """Technical indicators for a symbol"""
    symbol: str
    timestamp: datetime
    
    # Moving Averages
    ema_5: float = None
    ema_10: float = None
    ema_13: float = None
    ema_20: float = None
    ema_21: float = None
    ema_30: float = None
    ema_50: float = None
    ema_100: float = None
    sma_20: float = None
    
    # Momentum Indicators
    rsi_5: float = None
    rsi_14: float = None
    macd: float = None
    macd_signal: float = None
    macd_histogram: float = None
    stoch_k: float = None
    stoch_d: float = None
    
    # Other Indicators
    cci: float = None
    adx: float = None
    mfi: float = None
    bb_upper: float = None
    bb_lower: float = None
    bb_middle: float = None
    atr: float = None
    vwap: float = None
    supertrend: float = None
    supertrend_direction: int = None
    donchian_high: float = None
    donchian_low: float = None
    
@dataclass
class MarketRegime:
    """Market regime information"""
    regime: str  # 'bull', 'bear', 'sideways'
    confidence: float
    volatility_level: str  # 'low', 'medium', 'high'
    trend_strength: float
    market_breadth: float
    correlation_level: float
    timestamp: datetime
    
@dataclass
class TradingSignal:
    """Trading signal generated by the agent"""
    symbol: str
    strategy: str
    action: str  # 'BUY', 'SELL'
    price: float
    target: float
    stop_loss: float
    quantity: int
    confidence: float
    market_regime: str
    timestamp: datetime
    context: Dict[str, Any]
    
@dataclass
class MarketMonitoringConfig:
    """Configuration for Market Monitoring Agent"""
    
    # SmartAPI Configuration
    smartapi_config: Dict[str, Any]
    
    # Market Data Configuration
    market_data_config: Dict[str, Any]
    
    # Environment Detection Configuration
    environment_config: Dict[str, Any]
    
    # Strategy Triggering Configuration
    strategy_config: Dict[str, Any]
    
    # Notifications Configuration
    notifications_config: Dict[str, Any]
    
    # Logging Configuration
    logging_config: Dict[str, Any]
    
    # Storage Configuration
    storage_config: Dict[str, Any]
    
    # Performance Configuration
    performance_config: Dict[str, Any]
    
    # Error Handling Configuration
    error_handling_config: Dict[str, Any]

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def load_config(config_path: str = "config/market_monitoring_config.yaml") -> MarketMonitoringConfig:
    """Load configuration from YAML file with environment variable resolution"""
    try:
        # Use ConfigurationLoader for proper environment variable resolution
        from utils.config_loader import ConfigurationLoader
        config_loader = ConfigurationLoader()
        config_data = config_loader.load_agent_config(config_path)
        
        return MarketMonitoringConfig(
            smartapi_config=config_data.get('smartapi', {}),
            market_data_config=config_data.get('market_data', {}),
            environment_config=config_data.get('environment', {}),
            strategy_config=config_data.get('strategy_triggering', {}),
            notifications_config=config_data.get('notifications', {}),
            logging_config=config_data.get('logging', {}),
            storage_config=config_data.get('storage', {}),
            performance_config=config_data.get('performance', {}),
            error_handling_config=config_data.get('error_handling', {})
        )
    except Exception as e:
        logger.error(f"Failed to load configuration: {e}")
        raise

def setup_logging(config: MarketMonitoringConfig):
    """Setup logging configuration with Unicode support for Windows"""
    log_config = config.logging_config

    # Force stdout and stderr to UTF-8 for Windows compatibility
    import io
    if sys.stdout.encoding != 'utf-8':
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if sys.stderr.encoding != 'utf-8':
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    # Create logs directory
    log_dir = log_config.get('file_logging', {}).get('log_dir', 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # Configure logging
    log_level = getattr(logging, log_config.get('level', 'INFO').upper())

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Clear existing handlers to prevent duplicates, especially if Rich is also configuring
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Console handler with UTF-8 encoding for Windows compatibility
    # Only add if Rich is not available, to avoid duplicate console output
    if not RICH_AVAILABLE:
        console_handler = logging.StreamHandler(sys.stdout) # Use sys.stdout directly
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # File handler with UTF-8 encoding
    if log_config.get('file_logging', {}).get('enable', True):
        from logging.handlers import RotatingFileHandler

        file_handler = RotatingFileHandler(
            filename=os.path.join(log_dir, 'market_monitoring.log'),
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'  # Explicit UTF-8 encoding for file handler
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # Reduce websocket ping noise
    logging.getLogger('websocket').setLevel(logging.WARNING)

    logger.info("[SUCCESS] Logging configured successfully with Unicode support")

def get_system_info() -> Dict[str, Any]:
    """Get system information for monitoring"""
    import psutil
    
    return {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage": psutil.disk_usage('/').percent,
        "timestamp": datetime.now().isoformat()
    }

def check_dependencies() -> Dict[str, bool]:
    """Check if required dependencies are available"""
    dependencies = {
        "smartapi": SmartConnect is not None,
        "talib": True,
        "telegram": True,
        "ai_training_agent": AITrainingAgent is not None,
        "websocket": True,
        "polars": True,
        "pandas": True,
        "numpy": True
    }

    return dependencies

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MARKET MONITORING AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class MarketMonitoringAgent:
    """
    Market Monitoring Agent for real-time market data tracking and strategy triggering

    Features:
    - Live market data tracking with WebSocket
    - Real-time indicator calculations
    - Market regime detection
    - Strategy triggering with AI integration
    - Multi-channel notifications
    - Comprehensive logging and monitoring
    """

    def __init__(self, config_path: str = "config/market_monitoring_config.yaml"):
        """Initialize Market Monitoring Agent"""

        # Load configuration
        self.config = load_config(config_path)

        # Setup logging
        setup_logging(self.config)

        # Initialize components
        self.smartapi_client = None
        self.websocket_client = None
        self.ai_agent = None
        self.telegram_bot = None

        # Data storage
        self.market_data = defaultdict(lambda: defaultdict(deque))  # symbol -> timeframe -> deque of OHLCV
        self.live_data = {}  # For storing live data in memory by timeframe
        self.indicators = defaultdict(dict)  # symbol -> indicators
        self.market_regime = None
        self.active_signals = []
        self.performance_metrics = {}

        # State management
        self.is_running = False
        self.is_connected = False
        self.subscribed_symbols = set()

        # Thread-safe operation flags
        self._subscription_pending = False
        self._reconnection_pending = False
        self._pending_messages = []

        # Event handlers
        self.signal_handlers = []
        self.regime_change_handlers = []

        logger.info("Market Monitoring Agent initialized")

    async def setup(self):
        """Setup all components"""
        logger.info("[CONFIG] Setting up Market Monitoring Agent...")

        try:
            # Setup SmartAPI client
            await self._setup_smartapi()

            # Setup AI Training Agent
            await self._setup_ai_agent()

            # Setup notification systems
            await self._setup_notifications()

            # Create storage directories
            self._create_storage_directories()

            # Load symbols from strategy config
            await self._load_symbols()

            logger.info("[SUCCESS] Market Monitoring Agent setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Setup failed: {e}")
            raise

    async def download_live_historical_data(self, days_back: int = 35, max_symbols: int = 500, testing_mode: bool = False) -> bool:
        """
        Download live historical data from SmartAPI for live market monitoring with robust retry logic

        Args:
            days_back: Number of days of historical data to download (default: 35 for 100 1hr candles)
            max_symbols: Maximum number of symbols to download
            testing_mode: If True, use top 20 stocks for testing

        Note:
            - For 100 1-hour candles, we need 1,200 5-min candles (16 trading days)
            - 35 calendar days ensures 25 trading days with safety buffer for weekends/holidays
            - If started during market hours, also downloads today's data
        """
        try:
            # Check for testing mode - limit to top 20 stocks if enabled
            if testing_mode or os.getenv('TESTING_MODE', 'false').lower() == 'true':
                max_symbols = min(max_symbols, int(os.getenv('MAX_SYMBOLS', '20')))
                logger.info(f"[TESTING] Testing mode enabled - limiting to {max_symbols} symbols")

            logger.info(f"[INIT] Starting live historical data download for {max_symbols} symbols, {days_back} days back")
            logger.info(f"[INFO] Downloading {days_back} days to ensure sufficient data for higher timeframe conversions (15min/30min/1hr)")

            if not self.smartapi_client:
                logger.error("[ERROR] SmartAPI client not initialized")
                return False

            # Load symbols
            symbols = await self._get_symbol_list(max_symbols)
            if not symbols:
                logger.error("[ERROR] No symbols loaded")
                return False

            # Ensure we process all requested symbols
            logger.info(f"[CONFIG] Loaded {len(symbols)} symbols for live data processing")

            # Calculate date range with proper market hours for better data coverage
            from datetime import datetime, timedelta
            import pytz

            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            end_date = datetime.now(ist).replace(tzinfo=None)

            # Check if we should include today's data (if in market hours)
            include_today = self._should_download_today_data()
            if include_today:
                logger.info(f"[MARKET HOURS] Including today's data (current time: {end_date.strftime('%H:%M')})")
                # Keep current time as end_date to get today's data
            else:
                # If not in market hours, set to previous market close
                if end_date.weekday() < 5:  # Monday to Friday
                    end_date = end_date.replace(hour=15, minute=30)  # Previous market close
                else:
                    # If weekend, go to Friday's close
                    days_to_friday = (end_date.weekday() - 4) % 7
                    end_date = end_date - timedelta(days=days_to_friday)
                    end_date = end_date.replace(hour=15, minute=30)

            # For live data, we need 35 calendar days to ensure 25 trading days (1,875 5-min candles)
            # This provides 100+ 1-hour candles with safety buffer for weekends/holidays
            start_date = end_date - timedelta(days=days_back)

            # Set proper market hours for start date
            if start_date.weekday() < 5:  # Monday to Friday
                start_date = start_date.replace(hour=9, minute=15)  # Market open

            logger.info(f"[CONFIG] Downloading LIVE data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"[CONFIG] Processing {len(symbols)} symbols via SmartAPI only (no yfinance fallback for live data)")

            # Initialize tracking variables for robust downloading
            all_data = []
            failed_symbols = {}
            successful_downloads = 0

            # Process symbols with retry logic (similar to historical_data_downloader.py)
            logger.info(f"[DOWNLOAD] Starting initial download for {len(symbols)} symbols...")

            for i, symbol_info in enumerate(symbols):
                symbol = symbol_info['symbol']
                token = symbol_info['token']
                exchange = symbol_info.get('exchange', 'NSE')

                logger.debug(f"[PROGRESS] Processing {symbol} ({i+1}/{len(symbols)})")

                # Download with retry logic
                df = await self._download_from_smartapi_with_retry(
                    symbol, token, exchange, start_date, end_date
                )

                if df is not None and len(df) > 0:
                    all_data.append(df)
                    successful_downloads += 1
                    logger.debug(f"[SUCCESS] Downloaded {symbol}: {len(df)} records")
                else:
                    failed_symbols[symbol] = {"token": token, "exchange": exchange}
                    logger.warning(f"[FAILED] Failed to download {symbol}")

                # Rate limiting between downloads (same as historical downloader)
                await asyncio.sleep(0.75)

            # Retry failed symbols
            if failed_symbols:
                logger.warning(f"[RETRY] Retrying {len(failed_symbols)} failed symbols...")
                retry_success = 0

                for symbol, info in failed_symbols.copy().items():
                    logger.info(f"[RETRY] Retrying: {symbol}")

                    df = await self._download_from_smartapi_with_retry(
                        symbol, info['token'], info['exchange'], start_date, end_date
                    )

                    if df is not None and len(df) > 0:
                        all_data.append(df)
                        retry_success += 1
                        successful_downloads += 1
                        del failed_symbols[symbol]
                        logger.info(f"[RETRY_SUCCESS] Successfully downloaded {symbol}: {len(df)} records")

                    await asyncio.sleep(1.0)  # Longer wait for retries

                logger.info(f"[RETRY_SUMMARY] Retry success for {retry_success} symbols")

            # Log final statistics
            logger.info(f"[DOWNLOAD_SUMMARY] Total symbols attempted: {len(symbols)}")
            logger.info(f"[DOWNLOAD_SUMMARY] Successfully downloaded: {successful_downloads}")
            logger.info(f"[DOWNLOAD_SUMMARY] Failed after retry: {len(failed_symbols)}")

            if failed_symbols:
                logger.warning("[FAILED_SYMBOLS] List of symbols still failed after retry:")
                for symbol in failed_symbols:
                    logger.warning(f" - {symbol}")

            # Save all data to live parquet file
            if all_data:
                await self._save_live_data_parquet(all_data, start_date, end_date)
                
                # Generate multiple timeframes and load to memory
                await self._generate_and_load_timeframes()
                
                logger.info(f"[SUCCESS] Downloaded live data for {len(all_data)} symbols, {len(failed_symbols)} failed")
                return True
            else:
                logger.error("[ERROR] No live data downloaded")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Live historical data download failed: {e}")
            return False

    # Keep the original method for backward compatibility
    async def download_historical_data_batch(self, days_back: int = 35, max_symbols: int = 500) -> bool:
        """Backward compatibility method - delegates to download_live_historical_data"""
        return await self.download_live_historical_data(days_back, max_symbols, testing_mode=False)

    async def _get_symbol_list(self, max_symbols: int) -> List[Dict]:
        """Get symbol list with tokens for historical data download"""
        try:
            # Check if we're in testing mode and should use top 20 stocks
            testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
            if testing_mode and max_symbols <= 20:
                # Use predefined top 20 stocks for testing mode
                top_20_symbols = await self._get_top_20_stocks()
                if top_20_symbols:
                    logger.info(f"[TESTING] Using predefined top 20 stocks for testing mode")
                    return top_20_symbols[:max_symbols] if max_symbols > 0 else top_20_symbols

            # First try to load from historical data (most comprehensive)
            historical_symbols = await self._load_symbols_from_historical_data()
            if historical_symbols:
                logger.info(f"[SUCCESS] Loaded {len(historical_symbols)} symbols from historical data")
                return historical_symbols[:max_symbols] if max_symbols > 0 else historical_symbols

            # Try to load from symbols.json as fallback
            symbols_file = "config/symbols.json"
            if os.path.exists(symbols_file):
                with open(symbols_file, 'r') as f:
                    symbols = json.load(f)
                logger.info(f"[SUCCESS] Loaded {len(symbols)} symbols from {symbols_file}")
                return symbols[:max_symbols] if max_symbols > 0 else symbols

            # Load symbols from nse_500_universe.json
            nse_500_universe_file = "config/nse_500_universe.json"
            if os.path.exists(nse_500_universe_file):
                with open(nse_500_universe_file, 'r') as f:
                    universe_data = json.load(f)
                
                # Filter for active Nifty 500 stocks
                all_symbols = [
                    {"symbol": stock["symbol"], "token": stock["token"], "exchange": stock["exchange"]}
                    for stock in universe_data["stocks"]
                    if stock.get("is_active", False) and stock.get("nifty_500", False)
                ]
                logger.info(f"[SUCCESS] Loaded {len(all_symbols)} active Nifty 500 symbols from {nse_500_universe_file}")
                return all_symbols[:max_symbols] if max_symbols > 0 else all_symbols
            else:
                logger.warning(f"[WARN] NSE 500 universe file not found: {nse_500_universe_file}")
                return []

        except Exception as e:
            logger.error(f"[ERROR] Error loading symbol list: {e}")
            return []

    async def _get_top_20_stocks(self) -> List[Dict]:
        """Dynamically get top 20 stocks from NSE 500 universe based on market cap or other criteria"""
        try:
            nse_500_universe_file = "config/nse_500_universe.json"
            if os.path.exists(nse_500_universe_file):
                with open(nse_500_universe_file, 'r') as f:
                    universe_data = json.load(f)
                
                # Filter for active Nifty 500 stocks and sort by market_cap_value (descending)
                # Assuming 'market_cap_value' is a numerical representation of market cap
                top_stocks = sorted(
                    [
                        {"symbol": stock["symbol"], "token": stock["token"], "exchange": stock["exchange"]}
                        for stock in universe_data["stocks"]
                        if stock.get("is_active", False) and stock.get("nifty_500", False)
                    ],
                    key=lambda x: x.get("market_cap_value", 0),
                    reverse=True
                )
                
                # Return top 20 or fewer if not enough active Nifty 500 stocks
                logger.info(f"[TOP_STOCKS] Dynamically selected {min(20, len(top_stocks))} top stocks from NSE 500 universe")
                return top_stocks[:20]
            else:
                logger.warning(f"[WARN] NSE 500 universe file not found: {nse_500_universe_file}")
                return []

        except Exception as e:
            logger.error(f"[ERROR] Error dynamically loading top stocks: {e}")
            return []

    async def _load_symbols_from_historical_data(self) -> List[Dict]:
        """Load symbols from historical data files"""
        try:
            import polars as pl

            # Try to load from backup 5min data first (as per user preference)
            historical_files = [
                "data/historical/historical_1hr.parquet",
                "data/historical/backup/historical_5min.parquet",
                "data/historical/historical_30min.parquet",
                "data/historical/historical_15min.parquet",
                "data/historical/historical_5min.parquet"
            ]

            for file_path in historical_files:
                if os.path.exists(file_path):
                    logger.info(f"[LOAD] Loading symbols from {file_path}")
                    df = pl.read_parquet(file_path)

                    # Extract unique symbols and tokens
                    symbols_df = df.select([
                        pl.col(" Stock_Name").alias("symbol"),
                        pl.col(" SymbolToken").alias("token")
                    ]).unique().sort("symbol")

                    symbols_list = []
                    for row in symbols_df.iter_rows(named=True):
                        # Clean symbol name (remove -EQ suffix if present)
                        symbol = row["symbol"].replace("-EQ", "")
                        symbols_list.append({
                            "symbol": symbol,
                            "token": str(row["token"]),
                            "exchange": "NSE"
                        })

                    logger.info(f"[SUCCESS] Extracted {len(symbols_list)} symbols from historical data")
                    return symbols_list

            logger.warning("[WARN] No historical data files found")
            return []

        except Exception as e:
            logger.error(f"[ERROR] Error loading symbols from historical data: {e}")
            return []

    async def _download_symbol_data_async(self, symbol_info: Dict, start_date: datetime, end_date: datetime, semaphore: asyncio.Semaphore):
        """Download historical data for a single symbol using SmartAPI (no yfinance fallback for live data)"""
        async with semaphore:
            try:
                symbol = symbol_info['symbol']
                token = symbol_info['token']
                exchange = symbol_info.get('exchange', 'NSE')

                logger.debug(f"[DOWNLOAD] {symbol} ({token})")

                # Use SmartAPI directly for live data (more accurate for live trading)
                smartapi_data = await self._download_from_smartapi(symbol, token, exchange, start_date, end_date)
                if smartapi_data is not None:
                    logger.debug(f"[SUCCESS] Downloaded {symbol} from SmartAPI: {len(smartapi_data)} records")
                    return smartapi_data

                logger.warning(f"[WARN] SmartAPI download failed for {symbol}, skipping yfinance fallback for live data")
                return None

            except Exception as e:
                logger.error(f"[ERROR] Error downloading {symbol_info.get('symbol', 'unknown')}: {e}")
                return None

    async def _download_symbol_data_live(self, symbol_info: Dict, start_date: datetime, end_date: datetime, semaphore: asyncio.Semaphore):
        """Download live historical data for a single symbol using SmartAPI only (no yfinance fallback)"""
        async with semaphore:
            try:
                symbol = symbol_info['symbol']
                token = symbol_info['token']
                exchange = symbol_info.get('exchange', 'NSE')

                logger.debug(f"[LIVE_DOWNLOAD] {symbol} ({token})")

                # Use SmartAPI only for live data (more accurate and up-to-date)
                smartapi_data = await self._download_from_smartapi(symbol, token, exchange, start_date, end_date)
                if smartapi_data is not None:
                    logger.debug(f"[SUCCESS] Downloaded live data for {symbol}: {len(smartapi_data)} records")
                    return smartapi_data

                logger.warning(f"[WARN] Live data download failed for {symbol} via SmartAPI")
                return None

            except Exception as e:
                logger.error(f"[ERROR] Error downloading live data for {symbol_info.get('symbol', 'unknown')}: {e}")
                return None

    async def _download_from_smartapi_with_retry(self, symbol: str, token: str, exchange: str, start_date: datetime, end_date: datetime):
        """Wrapper method for robust downloading with retry logic"""
        return await self._download_from_smartapi(symbol, token, exchange, start_date, end_date)

    async def _download_from_smartapi(self, symbol: str, token: str, exchange: str, start_date: datetime, end_date: datetime):
        """Download historical data from SmartAPI with retry logic"""
        try:
            # Prepare request with correct SmartAPI format
            hist_params = {
                "exchange": exchange,
                "symboltoken": token,
                "interval": "FIVE_MINUTE",  # Correct interval format
                "fromdate": start_date.strftime("%Y-%m-%d %H:%M"),
                "todate": end_date.strftime("%Y-%m-%d %H:%M")
            }

            logger.debug(f"[SMARTAPI] Requesting {symbol} with params: {hist_params}")

            # Call API with enhanced retry logic for rate limiting
            max_retries = 5  # Increased retries for rate limiting
            base_delay = 2  # Base delay in seconds

            for attempt in range(max_retries):
                try:
                    # Add rate limiting delay before each request
                    if attempt > 0:
                        # Exponential backoff with jitter for rate limiting
                        delay = base_delay * (2 ** attempt) + (attempt * 0.5)
                        logger.debug(f"[RATE_LIMIT] Waiting {delay:.1f}s before retry {attempt + 1} for {symbol}")
                        await asyncio.sleep(delay)

                    # Run the blocking getCandleData in a separate thread
                    response = await asyncio.to_thread(self.smartapi_client.getCandleData, hist_params)

                    if response.get('status'):
                        break  # Success, exit retry loop
                    else:
                        error_msg = response.get('message', 'Unknown error')
                        error_code = response.get('errorcode', '')

                        # Handle specific AB1004 rate limiting error
                        if error_code == 'AB1004' or 'Something Went Wrong, Please Try After Sometime' in error_msg:
                            if attempt < max_retries - 1:
                                wait_time = base_delay * (2 ** (attempt + 1))  # Exponential backoff
                                logger.warning(f"[RATE_LIMIT] AB1004 error for {symbol}, waiting {wait_time}s before retry {attempt + 1}/{max_retries}")
                                await asyncio.sleep(wait_time)
                                continue
                            else:
                                logger.error(f"[RATE_LIMIT] AB1004 error for {symbol} after {max_retries} attempts, skipping")
                                return None

                        # Handle other errors
                        if attempt < max_retries - 1:
                            logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {error_msg}. Retrying...")
                            await asyncio.sleep(base_delay)
                        else:
                            logger.error(f"Failed to download {symbol} after {max_retries} attempts: {error_msg}")
                            return None

                except Exception as e:
                    if attempt < max_retries - 1:
                        wait_time = base_delay * (attempt + 1)
                        logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}. Retrying in {wait_time}s...")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"Failed to download {symbol} after {max_retries} attempts: {e}")
                        return None

            data = response.get('data', [])
            if not data:
                logger.warning(f"[SMARTAPI] No data for {symbol}")
                return None

            # Convert to Polars DataFrame with separate date and time columns like simple_historical_downloader
            import polars as pl
            timestamps = [datetime.fromisoformat(row[0]) for row in data]
            df = pl.DataFrame({
                'timestamp': [row[0] for row in data],  # Keep original timestamp for compatibility
                'date': [ts.strftime('%d-%m-%Y') for ts in timestamps],  # Add date column
                'time': [ts.strftime('%H:%M:%S') for ts in timestamps],  # Add time column
                'open': [float(row[1]) for row in data],
                'high': [float(row[2]) for row in data],
                'low': [float(row[3]) for row in data],
                'close': [float(row[4]) for row in data],
                'volume': [int(row[5]) for row in data],
                'symbol': [symbol] * len(data),
                'exchange': [exchange] * len(data)
            })

            # Log the actual date range of the downloaded data
            if len(df) > 0:
                min_ts = df['timestamp'].min()
                max_ts = df['timestamp'].max()
                logger.info(f"[SMARTAPI_DATA] Downloaded {symbol} data range: {min_ts} to {max_ts} ({len(df)} records)")
            else:
                logger.info(f"[SMARTAPI_DATA] Downloaded {symbol} data is empty.")

            # Enhanced rate limiting based on live trading system
            await asyncio.sleep(0.75)  # 0.75 second delay between requests (same as simple_historical_downloader)

            logger.debug(f"[SUCCESS] Downloaded {symbol} from SmartAPI: {len(df)} records")
            return df

        except Exception as e:
            logger.error(f"[SMARTAPI] Error downloading {symbol}: {e}")
            return None

    async def _download_from_yfinance(self, symbol: str, start_date: datetime, end_date: datetime):
        """Download historical data from YFinance (no rate limits)"""
        try:
            import yfinance as yf
            import polars as pl

            # Convert symbol to YFinance format
            yf_symbol = f"{symbol}.NS"

            # Create ticker object
            ticker = yf.Ticker(yf_symbol)

            # Download 5-minute data
            data = ticker.history(
                start=start_date,
                end=end_date,
                interval="5m",
                prepost=False,
                auto_adjust=True
            )

            if data.empty:
                logger.debug(f"[YFINANCE] No data available for {symbol}")
                return None

            # Convert to Polars DataFrame format
            df = pl.DataFrame({
                'timestamp': [dt.strftime('%Y-%m-%dT%H:%M:%S+05:30') for dt in data.index],
                'open': data['Open'].values,
                'high': data['High'].values,
                'low': data['Low'].values,
                'close': data['Close'].values,
                'volume': data['Volume'].values
            }).with_columns([
                pl.lit(symbol).alias('symbol'),
                pl.lit('NSE').alias('exchange')
            ])

            return df

        except ImportError:
            logger.debug(f"[YFINANCE] yfinance not installed, skipping for {symbol}")
            return None
        except Exception as e:
            logger.debug(f"[YFINANCE] Error downloading {symbol}: {e}")
            return None

    async def _save_live_data_parquet(self, data_frames: List, start_date: datetime, end_date: datetime):
        """Save live data to parquet file specifically for live trading"""
        try:
            import polars as pl
            from pathlib import Path

            # Combine all new dataframes
            logger.info(f"[SAVE_LIVE] Combining {len(data_frames)} live dataframes...")
            new_df = pl.concat(data_frames, how="vertical")

            # Sort by symbol and timestamp
            new_df = new_df.sort(['symbol', 'timestamp'])

            # Create output directory for live data
            output_dir = Path("data/live")
            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename for live 5min data
            filename = "live_5min.parquet"
            output_path = output_dir / filename

            # Intelligent data management for live data (keep last 5 days for current time)
            final_df = await self._merge_with_existing_data(new_df, output_path)

            # Save with compression
            final_df.write_parquet(
                output_path,
                compression="zstd",  # Fast compression
                use_pyarrow=True
            )

            # Log summary
            total_records = len(final_df)
            unique_symbols = final_df['symbol'].n_unique()
            file_size_mb = output_path.stat().st_size / (1024 * 1024)

            logger.info(f"[SUCCESS] Live 5min data saved to {output_path}")
            logger.info(f"[SUMMARY] Records: {total_records:,}, Symbols: {unique_symbols}, Size: {file_size_mb:.2f} MB")

            # Also save to memory for immediate use
            self.live_data['5min'] = final_df

        except Exception as e:
            logger.error(f"[ERROR] Error saving live data: {e}")

    async def _generate_and_load_timeframes(self):
        """Generate higher timeframes from 5min data and load to memory"""
        try:
            import polars as pl
            from pathlib import Path

            # Load 5min live data
            live_5min_path = Path("data/live/live_5min.parquet")
            if not live_5min_path.exists():
                logger.warning("[TIMEFRAMES] No 5min live data found for timeframe generation")
                return

            logger.info("[TIMEFRAMES] Generating higher timeframes from 5min live data...")
            df_5min = pl.read_parquet(live_5min_path)

            # Generate 15min, 30min, and 1hr timeframes
            timeframes_to_generate = {
                '15min': 3,   # 3 x 5min = 15min
                '30min': 6,   # 6 x 5min = 30min
                '1hr': 12     # 12 x 5min = 1hr
            }

            for timeframe, multiplier in timeframes_to_generate.items():
                logger.info(f"[TIMEFRAMES] Generating {timeframe} data...")

                # Group by symbol and resample using proper aggregation
                df_resampled = (
                    df_5min
                    .with_columns(pl.col('timestamp').str.to_datetime())
                    .sort(['symbol', 'timestamp'])
                    .with_row_index()
                    .with_columns((pl.col('index') // multiplier).alias('group_id'))
                    .group_by(['symbol', 'group_id'])
                    .agg([
                        pl.col('timestamp').first().alias('timestamp'),
                        pl.col('open').first().alias('open'),
                        pl.col('high').max().alias('high'),
                        pl.col('low').min().alias('low'),
                        pl.col('close').last().alias('close'),
                        pl.col('volume').sum().alias('volume')
                    ])
                    .drop('group_id')
                    .sort(['symbol', 'timestamp'])
                )

                # Save to memory for immediate use
                self.live_data[timeframe] = df_resampled
                logger.info(f"[TIMEFRAMES] Generated {timeframe}: {len(df_resampled)} records")

            logger.info("[TIMEFRAMES] All timeframes generated and loaded to memory")

        except Exception as e:
            logger.error(f"[ERROR] Error generating timeframes: {e}")

    async def _save_historical_data_parquet(self, data_frames: List, start_date: datetime, end_date: datetime):
        """Save historical data to parquet file with intelligent append/update logic"""
        try:
            import polars as pl
            from pathlib import Path

            # Combine all new dataframes
            logger.info(f"[SAVE] Combining {len(data_frames)} dataframes...")
            new_df = pl.concat(data_frames, how="vertical")

            # Sort by symbol and timestamp
            new_df = new_df.sort(['symbol', 'timestamp'])

            # Create output directory for live data
            output_dir = Path("data/live")
            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename for live data
            filename = "live_5min.parquet"
            output_path = output_dir / filename

            # Intelligent data management
            final_df = await self._merge_with_existing_data(new_df, output_path)

            # Save with compression
            final_df.write_parquet(
                output_path,
                compression="zstd",  # Fast compression
                use_pyarrow=True
            )

            # Log summary
            total_records = len(final_df)
            unique_symbols = final_df['symbol'].n_unique()
            file_size_mb = output_path.stat().st_size / (1024 * 1024)

            logger.info(f"[SUCCESS] Live data saved to {output_path}")
            logger.info(f"[SUMMARY] Records: {total_records:,}, Symbols: {unique_symbols}, Size: {file_size_mb:.2f} MB")

            # Also save to memory for immediate use
            self._load_historical_data_to_memory(final_df)

        except Exception as e:
            logger.error(f"[ERROR] Error saving historical data: {e}")

    async def _merge_with_existing_data(self, new_df: pl.DataFrame, output_path: Path) -> pl.DataFrame:
        """Merge new data with existing data, keeping sufficient historical data for analysis"""
        try:
            import polars as pl
            from datetime import datetime, timedelta

            # Calculate cutoff date (35 days ago to match days_back parameter)
            # This ensures we keep enough data for proper analysis
            cutoff_date = datetime.now() - timedelta(days=35)
            cutoff_str = cutoff_date.strftime("%Y-%m-%dT%H:%M:%S")

            if output_path.exists():
                logger.info("[MERGE] Loading existing live data...")
                existing_df = pl.read_parquet(output_path)

                # Remove data older than 35 days (not 8 days)
                logger.info(f"[CLEANUP] Removing data older than {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
                existing_df = existing_df.filter(pl.col('timestamp') >= cutoff_str)

                # Get the latest timestamp in existing data
                if len(existing_df) > 0:
                    latest_timestamp = existing_df['timestamp'].max()
                    logger.info(f"[INFO] Latest existing data: {latest_timestamp}")

                    # Keep all new data (don't filter by latest timestamp)
                    # This allows for backfilling and ensures we get all requested historical data
                    logger.info(f"[APPEND] Adding {len(new_df)} new records (including potential backfill)")

                    if len(new_df) > 0:
                        # Combine existing and new data
                        combined_df = pl.concat([existing_df, new_df], how="vertical")
                    else:
                        logger.info("[INFO] No new data to append")
                        combined_df = existing_df
                else:
                    logger.info("[INFO] Existing file is empty, using all new data")
                    combined_df = new_df
            else:
                logger.info("[NEW] Creating new live data file")
                # Keep all new data (don't apply cutoff for initial download)
                combined_df = new_df

            # Sort by symbol and timestamp
            combined_df = combined_df.sort(['symbol', 'timestamp'])

            # Remove duplicates (keep latest)
            combined_df = combined_df.unique(['symbol', 'timestamp'], keep='last')

            # Log date range of final data
            if len(combined_df) > 0:
                min_date = combined_df['timestamp'].min()
                max_date = combined_df['timestamp'].max()
                logger.info(f"[RESULT] Final dataset: {len(combined_df)} records, {combined_df['symbol'].n_unique()} symbols")
                logger.info(f"[DATE_RANGE] Data spans from {min_date} to {max_date}")
            else:
                logger.warning("[WARNING] No data in final dataset")

            return combined_df

        except Exception as e:
            logger.error(f"[ERROR] Error merging data: {e}")
            return new_df

    def _load_historical_data_to_memory(self, df):
        """Load historical data into memory for immediate use"""
        try:
            logger.info("[MEMORY] Loading historical data into memory...")

            # Group by symbol and store in market_data structure
            for symbol in df['symbol'].unique():
                symbol_data = df.filter(pl.col('symbol') == symbol).sort('timestamp')

                # Initialize symbol in market_data if not exists
                if symbol not in self.market_data:
                    self.market_data[symbol] = {
                        '1min': deque(maxlen=1000),
                        '5min': deque(maxlen=1000),
                        '15min': deque(maxlen=1000),
                        '30min': deque(maxlen=1000),
                        '1hr': deque(maxlen=1000)
                    }

                # Convert to OHLCV objects and store
                for row in symbol_data.iter_rows(named=True):
                    candle = OHLCV(
                        symbol=symbol,
                        timestamp=datetime.fromisoformat(row['timestamp']),
                        timeframe='5min',
                        open=row['open'],
                        high=row['high'],
                        low=row['low'],
                        close=row['close'],
                        volume=row['volume']
                    )
                    self.market_data[symbol]['5min'].append(candle)

            logger.info(f"[SUCCESS] Loaded historical data for {len(self.market_data)} symbols into memory")

            # Note: Additional timeframe loading will be done during agent start

        except Exception as e:
            logger.error(f"[ERROR] Error loading historical data to memory: {e}")

    async def _load_all_timeframes_from_live_data(self):
        """Load live data for all configured timeframes (not training data)"""
        try:
            import polars as pl

            timeframes = self.config.market_data_config.get('timeframes', ['5min', '15min', '30min', '1hr'])

            # Try to load live data first, then fallback to historical data
            live_5min_path = "data/live/live_5min.parquet"
            historical_5min_path = "data/historical/historical_5min.parquet"

            df_5min = None

            # Try live data first
            if os.path.exists(live_5min_path):
                try:
                    logger.info(f"[LOAD] Loading 5min live data from {live_5min_path}")
                    df_5min = pl.read_parquet(live_5min_path)
                    logger.info(f"[SUCCESS] Loaded live data: {len(df_5min)} records, columns: {df_5min.columns}")
                except Exception as e:
                    logger.warning(f"[WARN] Failed to load live data: {e}")
                    df_5min = None
            else:
                logger.info(f"[INFO] Live data file not found: {live_5min_path}")

            # Always try to load and combine historical data for comprehensive coverage
            if os.path.exists(historical_5min_path):
                try:
                    logger.info(f"[LOAD] Loading 5min historical data from {historical_5min_path}")
                    df_historical = pl.read_parquet(historical_5min_path)
                    logger.info(f"[SUCCESS] Loaded historical data: {len(df_historical)} records, columns: {df_historical.columns}")

                    # Preprocess historical data to match live data format
                    df_historical = self._preprocess_market_data(df_historical)

                    # Combine live and historical data if both exist
                    if df_5min is not None and len(df_5min) > 0:
                        logger.info("[MERGE] Combining live and historical data...")
                        # Get unique symbols from both datasets
                        live_symbols = set(df_5min['symbol'].unique().to_list())
                        historical_symbols = set(df_historical['symbol'].unique().to_list())
                        logger.info(f"[MERGE] Live symbols: {len(live_symbols)}, Historical symbols: {len(historical_symbols)}")

                        # Combine datasets, with live data taking precedence for overlapping symbols/timestamps
                        df_5min = pl.concat([df_historical, df_5min]).unique(subset=['symbol', 'timestamp'], keep='last').sort(['symbol', 'timestamp'])
                        logger.info(f"[MERGE] Combined data: {len(df_5min)} records for {df_5min['symbol'].n_unique()} symbols")
                    else:
                        logger.info("[FALLBACK] Using historical data only")
                        df_5min = df_historical

                except Exception as e:
                    logger.error(f"[ERROR] Failed to load historical data: {e}")
                    import traceback
                    logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
                    # Keep live data if historical loading fails
                    if df_5min is None:
                        df_5min = None
            else:
                logger.warning(f"[WARN] Historical data file not found: {historical_5min_path}")
                if df_5min is None or len(df_5min) < 100:
                    logger.warning("[WARN] Insufficient data available for signal generation")

            if df_5min is not None and len(df_5min) > 0:
                # Preprocess data to ensure correct format
                df_5min = self._preprocess_market_data(df_5min)

                # Check and fix data sorting if needed
                df_5min = self._ensure_data_sorted(df_5min)
                logger.info(f"[PROCESS] Processing {len(df_5min)} records for {df_5min['symbol'].n_unique()} symbols")

                # Generate all timeframes from 5min data
                logger.info(f"[GENERATE] Starting timeframe generation for {df_5min['symbol'].n_unique()} symbols")
                await self._generate_timeframes_from_5min(df_5min, timeframes)
                logger.info(f"[GENERATE] Timeframe generation completed. Market data now has {len(self.market_data)} symbols")
            else:
                logger.warning("[WARN] No market data available, attempting to download...")

                # Determine max symbols based on testing mode
                max_symbols = 20 if os.getenv('TESTING_MODE') == 'true' else 500
                testing_mode = os.getenv('TESTING_MODE') == 'true'

                # Download historical data
                success = await self.download_live_historical_data(
                    days_back=35,
                    max_symbols=max_symbols,
                    testing_mode=testing_mode
                )
                if not success:
                    logger.error("Failed to download historical data")
                    return

                # Try loading again after download
                if os.path.exists(live_5min_path):
                    logger.info("Loading data after successful download...")
                    df_5min = pl.read_parquet(live_5min_path)
                    df_5min = self._ensure_data_sorted(df_5min)
                    await self._generate_timeframes_from_5min(df_5min, timeframes)

        except Exception as e:
            logger.error(f"[ERROR] Error loading live timeframes: {e}")

    def _preprocess_market_data(self, df: pl.DataFrame) -> pl.DataFrame:
        """Preprocess market data to ensure correct format"""
        try:
            # Check if we need to combine date and time columns
            if 'timestamp' not in df.columns and 'date' in df.columns and 'time' in df.columns:
                logger.info("[PREPROCESS] Combining date and time columns into timestamp")

                # Combine date and time into timestamp
                df = df.with_columns([
                    pl.concat_str([
                        pl.col('date'),
                        pl.lit(' '),
                        pl.col('time')
                    ]).str.strptime(pl.Datetime, format='%d-%m-%Y %H:%M:%S').alias('timestamp')
                ])

                # Drop the original date and time columns
                df = df.drop(['date', 'time'])

            # Ensure timestamp column exists
            if 'timestamp' not in df.columns:
                logger.error("[PREPROCESS] No timestamp column found and cannot create one")
                return df

            # Ensure required columns exist
            required_columns = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"[PREPROCESS] Missing required columns: {missing_columns}")
                return df

            # Convert timestamp to string format if it's not already
            if df['timestamp'].dtype != pl.Utf8:
                df = df.with_columns([
                    pl.col('timestamp').dt.strftime('%Y-%m-%d %H:%M:%S').alias('timestamp')
                ])

            # Normalize symbol names by removing exchange suffixes (e.g., "-EQ", "-BE")
            df = df.with_columns([
                pl.col('symbol').str.replace(r'-EQ$', '').str.replace(r'-BE$', '').alias('symbol')
            ])

            logger.info(f"[PREPROCESS] Data preprocessed successfully: {len(df)} records")
            logger.info(f"[PREPROCESS] Sample symbols after normalization: {df['symbol'].unique().to_list()[:10]}")
            return df

        except Exception as e:
            logger.error(f"[ERROR] Error preprocessing market data: {e}")
            return df

    def _is_market_hours(self) -> bool:
        """Check if current time is during market hours (9:15 AM to 3:30 PM IST)"""
        try:
            import pytz
            from datetime import datetime, time

            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()

            # Market hours: 9:15 AM to 3:30 PM
            market_open = time(9, 15)
            market_close = time(15, 30)

            # Check if it's a weekday (Monday=0, Sunday=6)
            is_weekday = now.weekday() < 5

            return is_weekday and market_open <= current_time <= market_close
        except Exception as e:
            logger.warning(f"Error checking market hours: {e}")
            return False

    def _should_download_today_data(self) -> bool:
        """Check if we should download today's data (if started during market hours)"""
        try:
            if not self._is_market_hours():
                return False

            import pytz
            from datetime import datetime, time

            # Get current time in IST
            ist = pytz.timezone('Asia/Kolkata')
            now = datetime.now(ist)
            current_time = now.time()

            # Only download if we're at least 30 minutes into the market
            # This ensures we have some meaningful data
            market_start_buffer = time(9, 45)

            return current_time >= market_start_buffer
        except Exception as e:
            logger.warning(f"Error checking if should download today's data: {e}")
            return False

    def _ensure_data_sorted(self, df: pl.DataFrame) -> pl.DataFrame:
        """Ensure data is properly sorted by symbol and timestamp"""
        try:
            # Check if data is already sorted
            is_sorted = True
            for symbol in df['symbol'].unique()[:5]:  # Check first 5 symbols
                symbol_data = df.filter(pl.col('symbol') == symbol)
                if not symbol_data['timestamp'].is_sorted():
                    is_sorted = False
                    break

            if not is_sorted:
                logger.info("[SORT] Data not sorted, sorting by symbol and timestamp...")
                df = df.sort(['symbol', 'timestamp'])
                logger.info("[SORT] Data sorting completed")
            else:
                logger.info("[SORT] Data already properly sorted")

            return df

        except Exception as e:
            logger.error(f"[ERROR] Error checking/sorting data: {e}")
            # Return original data if sorting fails
            return df

    async def _generate_timeframes_from_5min(self, df_5min: pl.DataFrame, timeframes: List[str]):
        """Generate all timeframes from 5min data and load into memory"""
        try:
            import polars as pl

            logger.info(f"[GENERATE] Input data: {len(df_5min)} records for {df_5min['symbol'].n_unique()} symbols")
            logger.info(f"[GENERATE] Timeframes to generate: {timeframes}")

            # First, ensure 5min data is sorted by symbol and timestamp for consistency
            df_5min_sorted = df_5min.sort(['symbol', 'timestamp'])
            logger.info(f"[GENERATE] Data sorted successfully")

            for timeframe in timeframes:
                if timeframe == "5min":
                    logger.info(f"[LOAD] Loading 5min data directly (no conversion needed)")
                    df_timeframe = df_5min_sorted
                else:
                    logger.info(f"[GENERATE] Creating {timeframe} data from 5min data")
                    # Convert 5min to other timeframes
                    df_timeframe = self._convert_5min_to_timeframe(df_5min_sorted, timeframe)

                    # Save timeframe data to file
                    await self._save_timeframe_data(df_timeframe, timeframe)

                # Load into memory for each symbol
                symbols_loaded = 0
                for symbol in df_timeframe['symbol'].unique():
                    # Initialize symbol if not exists
                    if symbol not in self.market_data:
                        self.market_data[symbol] = {tf: deque(maxlen=1000) for tf in timeframes}

                    # Get symbol data (already sorted for 5min, or sorted in conversion for others)
                    symbol_data = df_timeframe.filter(pl.col('symbol') == symbol)

                    # For non-5min data, ensure it's sorted by timestamp
                    if timeframe != "5min":
                        symbol_data = symbol_data.sort('timestamp')

                    # Convert to OHLCV objects and store
                    rows_processed = 0
                    for row in symbol_data.iter_rows(named=True):
                        try:
                            timestamp = datetime.fromisoformat(row['timestamp'])

                            candle = OHLCV(
                                symbol=symbol,
                                timestamp=timestamp,
                                timeframe=timeframe,
                                open=float(row['open']),
                                high=float(row['high']),
                                low=float(row['low']),
                                close=float(row['close']),
                                volume=int(row['volume'])
                            )

                            # Store in appropriate timeframe
                            if timeframe in self.market_data[symbol]:
                                self.market_data[symbol][timeframe].append(candle)
                                rows_processed += 1

                        except Exception as e:
                            logger.debug(f"[SKIP] Error processing row for {symbol}: {e}")
                            continue

                    if rows_processed > 0:
                        symbols_loaded += 1

                logger.info(f"[SUCCESS] Loaded {timeframe} data for {symbols_loaded} symbols")

        except Exception as e:
            logger.error(f"[ERROR] Error generating timeframes: {e}")
            import traceback
            logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")

    async def _save_timeframe_data(self, df: pl.DataFrame, timeframe: str):
        """Save timeframe data to separate parquet files"""
        try:
            from pathlib import Path

            # Create output directory for live data
            output_dir = Path("data/live")
            output_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename for timeframe data
            filename = f"live_{timeframe}.parquet"
            output_path = output_dir / filename

            # Save with compression
            df.write_parquet(
                output_path,
                compression="zstd",  # Fast compression
                use_pyarrow=True
            )

            # Log summary
            total_records = len(df)
            unique_symbols = df['symbol'].n_unique()
            file_size_mb = output_path.stat().st_size / (1024 * 1024)

            logger.info(f"[SUCCESS] {timeframe} data saved to {output_path}")
            logger.info(f"[SUMMARY] {timeframe}: {total_records:,} records, {unique_symbols} symbols, {file_size_mb:.2f} MB")

        except Exception as e:
            logger.error(f"[ERROR] Error saving {timeframe} data: {e}")

    def _convert_5min_to_timeframe(self, df_5min: pl.DataFrame, target_timeframe: str) -> pl.DataFrame:
        """Convert 5min data to other timeframes"""
        try:
            # Define resampling rules
            timeframe_map = {
                "15min": "15m",
                "30min": "30m",
                "1hr": "1h"
            }

            if target_timeframe not in timeframe_map:
                logger.error(f"[ERROR] Unsupported timeframe: {target_timeframe}")
                return df_5min

            resample_rule = timeframe_map[target_timeframe]

            # Group by symbol and resample
            result_dfs = []

            for symbol in df_5min['symbol'].unique():
                symbol_df = df_5min.filter(pl.col('symbol') == symbol)

                # Convert timestamp to datetime for resampling and sort by timestamp
                symbol_df = symbol_df.with_columns([
                    pl.col('timestamp').str.strptime(pl.Datetime, "%Y-%m-%dT%H:%M:%S%z").alias('datetime')
                ]).sort('datetime')  # Sort by datetime to fix group_by_dynamic error

                # Skip if no data for this symbol
                if symbol_df.height == 0:
                    continue

                # Resample OHLCV data
                resampled = symbol_df.group_by_dynamic(
                    'datetime',
                    every=resample_rule,
                    closed='left'
                ).agg([
                    pl.col('open').first().alias('open'),
                    pl.col('high').max().alias('high'),
                    pl.col('low').min().alias('low'),
                    pl.col('close').last().alias('close'),
                    pl.col('volume').sum().alias('volume'),
                    pl.col('symbol').first().alias('symbol'),
                    pl.col('exchange').first().alias('exchange')
                ]).with_columns([
                    pl.col('datetime').dt.strftime("%Y-%m-%dT%H:%M:%S+05:30").alias('timestamp')
                ]).drop('datetime')

                # Only add if we have data
                if resampled.height > 0:
                    result_dfs.append(resampled)

            # Combine all symbols
            if result_dfs:
                return pl.concat(result_dfs)
            else:
                logger.warning(f"[WARN] No data available for {target_timeframe} conversion")
                return df_5min

        except Exception as e:
            logger.error(f"[ERROR] Error converting timeframe: {e}")
            import traceback
            logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")
            return df_5min



    async def _setup_smartapi(self):
        """Setup SmartAPI client and WebSocket connection"""
        if not SmartConnect:
            logger.warning("[WARN]  SmartAPI not available, using mock data")
            return

        try:
            smartapi_config = self.config.smartapi_config

            # Initialize SmartAPI client
            self.smartapi_client = SmartConnect(api_key=smartapi_config['api_key'])

            # Generate session
            totp = pyotp.TOTP(smartapi_config['totp_token']).now()
            data = self.smartapi_client.generateSession(
                smartapi_config['username'],
                smartapi_config['password'],
                totp
            )

            if not data['status']:
                raise Exception(f"SmartAPI login failed: {data}")

            # Get tokens
            self.auth_token = data['data']['jwtToken']
            refresh_token = data['data']['refreshToken']
            self.feed_token = self.smartapi_client.getfeedToken()

            # Setup Enhanced WebSocket Service (preferred)
            if EnhancedWebSocketService:
                # Initialize without selected symbols first (will be updated later)
                self.enhanced_websocket = EnhancedWebSocketService(
                    auth_token=self.auth_token,
                    api_key=smartapi_config['api_key'],
                    username=smartapi_config['username'],
                    feed_token=self.feed_token,
                    selected_symbols=None  # Will be updated with selected stocks
                )

                # Setup callbacks for integration with market monitoring
                self.enhanced_websocket.add_tick_callback(self._on_enhanced_tick)
                self.enhanced_websocket.add_candle_callback(self._on_enhanced_candle)

                logger.info("[SUCCESS] Enhanced WebSocket Service initialized")

            # Setup Enhanced WebSocket Manager (fallback)
            elif RobustWebSocketManager:
                # Configuration for robust WebSocket
                ws_config = {
                    'max_retry_attempts': smartapi_config.get('websocket', {}).get('reconnect_attempts', 5),
                    'retry_delay_base': smartapi_config.get('websocket', {}).get('reconnect_delay', 5),
                    'connection_timeout': smartapi_config.get('websocket', {}).get('connection_timeout', 15),
                    'heartbeat_interval': smartapi_config.get('websocket', {}).get('heartbeat_interval', 30)
                }

                self.websocket_manager = RobustWebSocketManager(ws_config)

                # Set authentication tokens
                self.websocket_manager.auth_token = self.auth_token
                self.websocket_manager.feed_token = self.feed_token
                self.websocket_manager.api_key = smartapi_config['api_key']
                self.websocket_manager.username = smartapi_config['username']

                # Setup callbacks
                self.websocket_manager.set_callbacks(
                    on_connected=self._on_websocket_connected,
                    on_data=self._on_websocket_data_enhanced,
                    on_error=self._on_websocket_error_enhanced,
                    on_disconnected=self._on_websocket_disconnected
                )

                logger.info("[SUCCESS] Enhanced WebSocket Manager initialized")

            elif SmartWebSocketV2:
                # Fallback to original WebSocket implementation
                self.websocket_client = SmartWebSocketV2(
                    self.auth_token,
                    smartapi_config['api_key'],
                    smartapi_config['username'],
                    self.feed_token
                )

                # Setup WebSocket callbacks
                self.websocket_client.on_open = self._on_websocket_open
                self.websocket_client.on_data = self._on_websocket_data
                self.websocket_client.on_error = self._on_websocket_error
                self.websocket_client.on_close = self._on_websocket_close

                logger.info("[SUCCESS] Fallback WebSocket initialized")

            logger.info("[SUCCESS] SmartAPI client setup completed")

        except Exception as e:
            logger.error(f"[ERROR] SmartAPI setup failed: {e}")
            raise

    async def _setup_ai_agent(self):
        """Setup AI Training Agent for strategy classification"""
        if not AITrainingAgent:
            logger.warning("[WARN]  AI Training Agent not available")
            return

        try:
            if self.config.strategy_config.get('ai_model', {}).get('enable', True):
                self.ai_agent = AITrainingAgent()

                # Load trained models if available
                model_path = self.config.strategy_config['ai_model']['model_path']
                if os.path.exists(model_path):
                    # Load models (implementation depends on AI agent structure)
                    logger.info("[STATUS] AI models loaded successfully")
                else:
                    logger.warning("[WARN]  No trained AI models found")

        except Exception as e:
            logger.error(f"[ERROR] AI agent setup failed: {e}")

    async def _setup_notifications(self):
        """Setup notification systems"""
        try:
            # Setup Telegram bot
            telegram_config = self.config.notifications_config.get('telegram', {})
            if telegram_config.get('enable', False):
                bot_token = telegram_config.get('bot_token')
                if bot_token and bot_token != "YOUR_BOT_TOKEN":
                    self.telegram_bot = Bot(token=bot_token)
                    logger.info("[MOBILE] Telegram bot setup completed")
                else:
                    logger.warning("[WARN]  Telegram bot token not configured")

            # Setup other notification systems (Slack, Email) here

        except Exception as e:
            logger.error(f"[ERROR] Notification setup failed: {e}")

    def _create_storage_directories(self):
        """Create necessary storage directories"""
        storage_config = self.config.storage_config

        directories = [
            storage_config.get('realtime_data', {}).get('storage_path', 'data/realtime'),
            storage_config.get('signals', {}).get('storage_path', 'data/signals'),
            storage_config.get('market_context', {}).get('storage_path', 'data/market_context'),
            self.config.logging_config.get('file_logging', {}).get('log_dir', 'logs')
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        logger.info("[FOLDER] Storage directories created")

    async def _load_symbols(self):
        """Load symbols to monitor from strategy configuration"""
        try:
            # Load symbols from strategy config file
            strategy_config_path = "config/strategies.yaml"
            if os.path.exists(strategy_config_path):
                with open(strategy_config_path, 'r', encoding='utf-8') as file:
                    strategies = yaml.safe_load(file)

                # Extract unique symbols from strategies
                symbols = set()
                for strategy_name, strategy_data in strategies.items():
                    if isinstance(strategy_data, dict) and 'symbols' in strategy_data:
                        symbols.update(strategy_data['symbols'])

                self.config.market_data_config['symbols'] = list(symbols)
                logger.info(f"[STATUS] Loaded {len(symbols)} symbols for monitoring")
            else:
                logger.warning("[WARN]  Strategy config file not found, using default symbols")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load symbols: {e}")

    # WebSocket event handlers
    def _on_websocket_open(self, wsapp):
        """WebSocket connection opened"""
        logger.info("[CONNECT] WebSocket connection opened")
        self.is_connected = True

        # Schedule symbol subscription in a thread-safe way
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.call_soon_threadsafe(self._schedule_subscription)
            else:
                # If no event loop is running, create a task when one becomes available
                self._subscription_pending = True
        except RuntimeError:
            # No event loop available, mark subscription as pending
            self._subscription_pending = True
            logger.info("[CONNECT] Subscription scheduled for when event loop becomes available")

    def _on_websocket_data(self, wsapp, message):
        """Handle incoming WebSocket data"""
        try:
            # Process market tick data in a thread-safe way
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.call_soon_threadsafe(self._schedule_tick_processing, message)
                else:
                    # Store message for later processing
                    if not hasattr(self, '_pending_messages'):
                        self._pending_messages = []
                    self._pending_messages.append(message)
            except RuntimeError:
                # Store message for later processing
                if not hasattr(self, '_pending_messages'):
                    self._pending_messages = []
                self._pending_messages.append(message)

        except Exception as e:
            logger.error(f"[ERROR] Error processing WebSocket data: {e}")

    def _on_websocket_error(self, wsapp, error):
        """WebSocket error occurred"""
        logger.error(f"[CONNECT] WebSocket error: {error}")
        self.is_connected = False

    def _on_websocket_close(self, wsapp):
        """WebSocket connection closed"""
        logger.warning("[CONNECT] WebSocket connection closed")
        self.is_connected = False

        # Attempt reconnection in a thread-safe way
        if self.is_running:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.call_soon_threadsafe(self._schedule_reconnection)
                else:
                    self._reconnection_pending = True
            except RuntimeError:
                self._reconnection_pending = True

    # Enhanced WebSocket event handlers for RobustWebSocketManager
    def _on_websocket_connected(self):
        """Enhanced WebSocket connection established"""
        logger.info("[CONNECT] Enhanced WebSocket connection established")
        self.is_connected = True

        # Schedule symbol subscription
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                loop.call_soon_threadsafe(self._schedule_subscription)
            else:
                self._subscription_pending = True
        except RuntimeError:
            self._subscription_pending = True

    def _on_websocket_data_enhanced(self, message):
        """Enhanced WebSocket data handler"""
        try:
            # Add debug logging to see if we're receiving data
            logger.info(f"[WEBSOCKET] Received message: {type(message)} - {str(message)[:200]}...")

            # Process market tick data in a thread-safe way
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.call_soon_threadsafe(self._schedule_tick_processing, message)
                else:
                    # Store message for later processing
                    if not hasattr(self, '_pending_messages'):
                        self._pending_messages = []
                    self._pending_messages.append(message)
            except RuntimeError:
                # Store message for later processing
                if not hasattr(self, '_pending_messages'):
                    self._pending_messages = []
                self._pending_messages.append(message)

        except Exception as e:
            logger.error(f"[ERROR] Enhanced data processing error: {e}")

    def _on_websocket_error_enhanced(self, error):
        """Enhanced WebSocket error handler"""
        logger.error(f"[CONNECT] Enhanced WebSocket error: {error}")
        self.is_connected = False

    def _on_websocket_disconnected(self):
        """Enhanced WebSocket disconnection handler"""
        logger.warning("[CONNECT] Enhanced WebSocket disconnected")
        self.is_connected = False

    # Thread-safe scheduling helpers
    def _schedule_subscription(self):
        """Schedule symbol subscription in a thread-safe way"""
        asyncio.create_task(self._subscribe_to_symbols())
        self._subscription_pending = False

    def _schedule_tick_processing(self, message):
        """Schedule market tick processing in a thread-safe way"""
        asyncio.create_task(self._process_market_tick(message))

    def _schedule_reconnection(self):
        """Schedule WebSocket reconnection in a thread-safe way"""
        asyncio.create_task(self._reconnect_websocket())
        self._reconnection_pending = False

    # Enhanced WebSocket Service callbacks
    def _on_enhanced_tick(self, tick_data):
        """Handle tick data from enhanced WebSocket service"""
        try:
            # Update last tick time for monitoring
            self._last_tick_time = datetime.now()

            # Log tick data periodically (every 100th tick to avoid spam)
            if not hasattr(self, '_tick_count'):
                self._tick_count = 0
            self._tick_count += 1

            if self._tick_count % 100 == 0:
                logger.info(f"[ENHANCED_TICK] Received {self._tick_count} ticks. Latest: {tick_data.symbol} @ {tick_data.price}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling enhanced tick: {e}")

    async def _on_enhanced_candle(self, candle_data):
        """Handle completed candle from enhanced WebSocket service"""
        try:
            symbol = candle_data['symbol']
            timeframe = candle_data['timeframe']

            # Convert to OHLCV object and store
            candle = OHLCV(
                symbol=symbol,
                timestamp=candle_data['timestamp'],
                timeframe=timeframe,
                open=candle_data['open'],
                high=candle_data['high'],
                low=candle_data['low'],
                close=candle_data['close'],
                volume=candle_data['volume']
            )

            # Store in market data
            if symbol not in self.market_data:
                self.market_data[symbol] = defaultdict(deque)

            self.market_data[symbol][timeframe].append(candle)

            # Maintain size limit
            max_candles = self.config.market_data_config.get('max_candles_per_timeframe', 1000)
            if len(self.market_data[symbol][timeframe]) > max_candles:
                self.market_data[symbol][timeframe].popleft()

            logger.debug(f"[ENHANCED_CANDLE] {symbol} {timeframe} candle completed: O={candle.open:.2f} H={candle.high:.2f} L={candle.low:.2f} C={candle.close:.2f} V={candle.volume}")

            # Calculate indicators for this symbol and timeframe
            await self._calculate_indicators_for_timeframe(symbol, timeframe)

            # Check for trading signals
            await self._check_trading_signals_for_timeframe(symbol, timeframe)

        except Exception as e:
            logger.error(f"[ERROR] Error handling enhanced candle: {e}")

    def _process_pending_operations(self):
        """Process any pending operations that couldn't be scheduled earlier"""
        # Process pending subscription
        if self._subscription_pending:
            self._schedule_subscription()

        # Process pending reconnection
        if self._reconnection_pending:
            self._schedule_reconnection()

        # Process pending messages
        if hasattr(self, '_pending_messages') and self._pending_messages:
            for message in self._pending_messages:
                self._schedule_tick_processing(message)
            self._pending_messages = []

    async def subscribe_to_symbols(self, symbols: List[str]):
        """Public method to subscribe to specific symbols for continuous trading"""
        try:
            if not symbols:
                logger.warning("[WARN] No symbols provided for subscription")
                return False

            # Check connection status for both enhanced and fallback managers
            is_connected = False
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                status = self.websocket_manager.get_connection_status()
                is_connected = status['is_connected']
            elif self.websocket_client:
                is_connected = self.is_connected

            if not is_connected:
                logger.info("[INFO] WebSocket not connected, storing symbols for later subscription")
                # Store symbols for subscription when connection is available
                if not hasattr(self, '_pending_symbols'):
                    self._pending_symbols = set()
                self._pending_symbols.update(symbols)
                return True

            # Subscribe to the provided symbols
            return await self._subscribe_to_symbol_list(symbols)

        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
            return False

    async def _subscribe_to_symbols(self):
        """Subscribe to market data for configured symbols"""
        try:
            # Check connection status for both enhanced and fallback managers
            is_connected = False
            if hasattr(self, 'websocket_manager') and self.websocket_manager:
                status = self.websocket_manager.get_connection_status()
                is_connected = status['is_connected']
            elif self.websocket_client:
                is_connected = self.is_connected

            if not is_connected:
                return

            symbols = self.config.market_data_config.get('symbols', [])
            if not symbols:
                logger.debug("[DEBUG] No symbols configured in config file - using dynamic symbol selection")
                return

            # Use the helper method for actual subscription
            return await self._subscribe_to_symbol_list(symbols)

        except Exception as e:
            logger.error(f"[ERROR] Subscription failed: {e}")
            return False

    async def _subscribe_to_symbol_list(self, symbols: List[str]) -> bool:
        """Helper method to subscribe to a list of symbols"""
        try:
            # Prepare token list for subscription
            token_list = []
            for symbol in symbols:
                # Get token for symbol (this would need symbol-to-token mapping)
                token = self._get_token_for_symbol(symbol)
                if token:
                    token_list.append(token)

            if token_list:
                subscription_config = self.config.smartapi_config.get('subscription', {})
                mode = subscription_config.get('mode', 1)
                exchange_type = subscription_config.get('exchange_type', 1)

                # Subscribe to tokens using appropriate WebSocket manager
                subscription_data = [{
                    "exchangeType": exchange_type,
                    "tokens": token_list
                }]

                if hasattr(self, 'websocket_manager') and self.websocket_manager:
                    # Use enhanced WebSocket manager
                    success = await self.websocket_manager.subscribe_symbols(symbols)
                    if success:
                        self.subscribed_symbols.update(symbols)
                        logger.info(f"[SIGNAL] Enhanced subscription to {len(symbols)} symbols: {', '.join(symbols)}")
                        return True
                    else:
                        logger.warning(f"[WARN] Enhanced subscription failed for symbols: {', '.join(symbols)}")
                        return False
                elif self.websocket_client:
                    # Use fallback WebSocket client
                    self.websocket_client.subscribe("correlation_id", mode, subscription_data)
                    self.subscribed_symbols.update(symbols)

                logger.info(f"[SIGNAL] Subscribed to {len(token_list)} symbols: {', '.join(symbols)}")
                return True
            else:
                logger.warning(f"[WARN] No valid tokens found for symbols: {', '.join(symbols)}")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Symbol subscription failed: {e}")
            return False

    def _get_token_for_symbol(self, symbol: str) -> Optional[str]:
        """Get token for symbol using comprehensive mapping"""
        # Comprehensive symbol-to-token mapping (from working py_eq system)
        symbol_token_map = {
            'RELIANCE': '2885',
            'HDFCBANK': '1333',
            'INFY': '1594',
            'TCS': '11536',
            'ICICIBANK': '4963',
            'SIEMENS': '3150',
            'AUBANK': '21238',
            'APOLLOTYRE': '163',
            'NMDC': '15332',
            'GODREJPROP': '17875',
            'ASHOKLEY': '212',
            'GODREJCP': '1232',
            'ICICIGI': '21770',
            'ASIANPAINT': '236',
            'BHARTIARTL': '10604',
            'M&M': '519',
            'TATAMOTORS': '3456',
            'NESTLEIND': '17963',
            'INDHOTEL': '1512',
            'CUMMINSIND': '1901',
            'TECHM': '13538',
            'HDFC': '1330',
            'ITC': '424',
            'KOTAKBANK': '492',
            'LT': '11483',
            'SBIN': '3045',
            'HINDUNILVR': '1394',
            'BAJFINANCE': '317',
            'MARUTI': '10999',
            'AXISBANK': '5900',
            'TATASTEEL': '3499',
            'SUNPHARMA': '3351',
            'TITAN': '3506',
            'BAJAJFINSV': '16675',
            'WIPRO': '3787',
            'HCLTECH': '7229',
            'ULTRACEMCO': '11532'
        }

        token = symbol_token_map.get(symbol)
        if token:
            # Store the mapping for reverse lookup
            if not hasattr(self, '_token_to_symbol_map'):
                self._token_to_symbol_map = {}
            self._token_to_symbol_map[token] = symbol

        return token

    async def _process_market_tick(self, message):
        """Process incoming market tick data"""
        try:
            # Track last tick time for monitoring
            self._last_tick_time = datetime.now()

            # Parse WebSocket message (format depends on SmartAPI)
            tick_data = self._parse_websocket_message(message)
            if not tick_data:
                logger.warning(f"[TICK] Failed to parse WebSocket message: {type(message)} - {str(message)[:200]}...")
                return

            logger.info(f"[TICK] Parsed tick data: {tick_data}")

            # Create MarketTick object
            tick = MarketTick(
                symbol=tick_data.get('symbol'),
                token=tick_data.get('token'),
                timestamp=datetime.now(),
                ltp=tick_data.get('ltp'),
                volume=tick_data.get('volume', 0),
                open_price=tick_data.get('open'),
                high_price=tick_data.get('high'),
                low_price=tick_data.get('low'),
                close_price=tick_data.get('close')
            )

            logger.debug(f"[TICK] Processing tick for {tick.symbol}: LTP={tick.ltp}")

            # Update OHLC data
            await self._update_ohlc_data(tick)

            # Calculate indicators
            await self._calculate_indicators(tick.symbol)

            # Detect market regime changes
            await self._detect_regime_changes()

            # Check for trading signals
            await self._check_trading_signals(tick.symbol)

        except Exception as e:
            logger.error(f"[ERROR] Error processing market tick: {e}")
            import traceback
            logger.error(f"[ERROR] Traceback: {traceback.format_exc()}")

    def _parse_websocket_message(self, message) -> Optional[Dict[str, Any]]:
        """Parse WebSocket message to extract tick data"""
        try:
            # This depends on SmartAPI WebSocket message format
            logger.info(f"[PARSE] Parsing message type: {type(message)}")

            if isinstance(message, dict):
                logger.info(f"[PARSE] Message is already a dict: {message}")

                # Extract symbol from token if needed
                if 'tk' in message and 'symbol' not in message:
                    token = message.get('tk')
                    symbol = self._get_symbol_from_token(token)
                    if symbol:
                        message['symbol'] = symbol

                # Map SmartAPI fields to our expected format
                # SmartAPI WebSocket sends: token, last_traded_price, exchange_timestamp, etc.
                token = message.get('token', message.get('tk'))
                ltp = message.get('last_traded_price', message.get('ltp', message.get('lp', 0)))

                # Convert price from paise to rupees if needed
                if ltp > 10000:  # Likely in paise
                    ltp = ltp / 100

                return {
                    'symbol': message.get('symbol'),
                    'token': str(token) if token else None,
                    'ltp': float(ltp),
                    'volume': int(message.get('volume_traded_today', message.get('volume', message.get('v', 0)))),
                    'open': float(message.get('open_price_of_day', message.get('open', message.get('o', 0)))),
                    'high': float(message.get('high_price_of_day', message.get('high', message.get('h', 0)))),
                    'low': float(message.get('low_price_of_day', message.get('low', message.get('l', 0)))),
                    'close': float(ltp)  # Use LTP as close price for tick data
                }
            elif isinstance(message, str):
                try:
                    parsed = json.loads(message)
                    logger.info(f"[PARSE] Parsed JSON message: {parsed}")
                    return self._parse_websocket_message(parsed)  # Recursively parse the JSON
                except json.JSONDecodeError:
                    logger.warning(f"[PARSE] Failed to parse WebSocket message as JSON: {message[:200]}...")
                    return None
            else:
                logger.warning(f"[PARSE] Unsupported message type: {type(message)}")
                return None

        except Exception as e:
            logger.error(f"[ERROR] Error parsing WebSocket message: {e}")
            return None

    def _get_symbol_from_token(self, token: str) -> Optional[str]:
        """Get symbol from token"""
        try:
            # Use the reverse mapping created in _get_token_for_symbol
            if hasattr(self, '_token_to_symbol_map'):
                symbol = self._token_to_symbol_map.get(str(token))
                if symbol:
                    return symbol

            # Fallback: Check if we have a symbols_info mapping
            if hasattr(self, 'symbols_info'):
                for symbol_info in self.symbols_info:
                    if str(symbol_info.get('token')) == str(token):
                        return symbol_info.get('symbol')

            logger.debug(f"[DEBUG] No symbol found for token: {token}")
            return None
        except Exception as e:
            logger.error(f"[ERROR] Error getting symbol from token: {e}")
            return None

    async def _update_ohlc_data(self, tick: MarketTick):
        """Update OHLC data for different timeframes"""
        try:
            symbol = tick.symbol
            timeframes = self.config.market_data_config.get('timeframes', ['5min', '15min', '30min', '1hr'])

            for timeframe in timeframes:
                # Get current candle or create new one
                candle = self._get_or_create_candle(symbol, timeframe, tick.timestamp)

                # Update candle with tick data
                if candle:
                    # Set open price if this is the first tick for this candle
                    if candle.open == 0:
                        candle.open = tick.ltp
                        candle.high = tick.ltp
                        candle.low = tick.ltp
                    else:
                        candle.high = max(candle.high, tick.ltp)
                        candle.low = min(candle.low, tick.ltp)

                    candle.close = tick.ltp
                    candle.volume += tick.volume or 0

                    # Store updated candle
                    self._store_candle(symbol, timeframe, candle)

        except Exception as e:
            logger.error(f"[ERROR] Error updating OHLC data: {e}")

    def _get_or_create_candle(self, symbol: str, timeframe: str, timestamp: datetime) -> Optional[OHLCV]:
        """Get existing candle or create new one for the timeframe"""
        try:
            # Calculate candle timestamp based on timeframe
            candle_timestamp = self._get_candle_timestamp(timestamp, timeframe)

            # Check if candle already exists
            candles = self.market_data[symbol][timeframe]
            if candles and candles[-1].timestamp == candle_timestamp:
                return candles[-1]

            # Create new candle
            new_candle = OHLCV(
                symbol=symbol,
                timestamp=candle_timestamp,
                timeframe=timeframe,
                open=0,  # Will be set from first tick
                high=0,
                low=0,  # Will be set from first tick
                close=0,
                volume=0
            )

            return new_candle

        except Exception as e:
            logger.error(f"[ERROR] Error creating candle: {e}")
            return None

    def _get_candle_timestamp(self, timestamp: datetime, timeframe: str) -> datetime:
        """Get candle timestamp based on timeframe"""
        if timeframe == '1min':
            return timestamp.replace(second=0, microsecond=0)
        elif timeframe == '5min':
            minute = (timestamp.minute // 5) * 5
            return timestamp.replace(minute=minute, second=0, microsecond=0)
        elif timeframe == '15min':
            minute = (timestamp.minute // 15) * 15
            return timestamp.replace(minute=minute, second=0, microsecond=0)
        elif timeframe == '30min':
            minute = (timestamp.minute // 30) * 30
            return timestamp.replace(minute=minute, second=0, microsecond=0)
        elif timeframe == '1hr':
            return timestamp.replace(minute=0, second=0, microsecond=0)
        else:
            # Default to 1min for unknown timeframes
            return timestamp.replace(second=0, microsecond=0)

    def _store_candle(self, symbol: str, timeframe: str, candle: OHLCV):
        """Store candle in memory with size limits"""
        try:
            candles = self.market_data[symbol][timeframe]
            max_candles = self.config.market_data_config.get('max_candles_per_timeframe', 1000)

            # Add or update candle
            if candles and candles[-1].timestamp == candle.timestamp:
                candles[-1] = candle
            else:
                candles.append(candle)

                # Maintain size limit
                if len(candles) > max_candles:
                    candles.popleft()

        except Exception as e:
            logger.error(f"[ERROR] Error storing candle: {e}")

    async def _calculate_indicators(self, symbol: str):
        """Calculate technical indicators for a symbol using Polars and PyArrow"""
        try:
            # Get candle data for calculation
            if symbol not in self.market_data:
                logger.warning(f"[WARN] No market data available for {symbol}")
                return

            # Calculate indicators for each configured timeframe
            timeframes = self.config.market_data_config.get('timeframes', ['5min', '15min', '30min', '1hr'])

            for timeframe in timeframes:
                await self._calculate_indicators_for_timeframe(symbol, timeframe)

        except Exception as e:
            logger.error(f"[ERROR] Error calculating indicators for {symbol}: {e}")

    async def _calculate_indicators_for_timeframe(self, symbol: str, timeframe: str):
        """Calculate technical indicators for a specific timeframe"""
        try:
            # Get candle data for the specific timeframe
            if timeframe not in self.market_data[symbol]:
                logger.debug(f"[DEBUG] No {timeframe} data available for {symbol}")
                return

            candles = list(self.market_data[symbol][timeframe])

            # Determine minimum candles needed based on timeframe
            min_candles_needed = self._get_min_candles_for_timeframe(timeframe)

            if len(candles) < min_candles_needed:
                logger.debug(f"[DEBUG] Insufficient {timeframe} data for {symbol}: {len(candles)} candles (need {min_candles_needed}+)")
                return

            # Convert to Polars DataFrame for fast vectorized operations
            df = pl.DataFrame({
                'timestamp': [c.timestamp for c in candles],
                'open': [c.open for c in candles],
                'high': [c.high for c in candles],
                'low': [c.low for c in candles],
                'close': [c.close for c in candles],
                'volume': [c.volume for c in candles]
            })

            # Calculate indicators using Polars expressions
            indicators = MarketIndicators(
                symbol=symbol,
                timestamp=datetime.now(),
                timeframe=timeframe  # Add timeframe to indicators
            )

            indicator_config = self.config.market_data_config.get('indicators', {})

            # Calculate all indicators in one pass using Polars lazy evaluation
            df_with_indicators = df.lazy()

            # Moving Averages (EMA and SMA)
            for period in indicator_config.get('ema_periods', []):
                if len(df) >= period:
                    if POLARS_TALIB_AVAILABLE:
                        # Use polars-talib for EMA
                        df_with_indicators = df_with_indicators.with_columns(
                            ta.ema(pl.col('close'), period).alias(f'ema_{period}')
                        )
                    else:
                        # Fallback: Manual EMA calculation using Polars
                        alpha = 2.0 / (period + 1)
                        df_with_indicators = df_with_indicators.with_columns(
                            pl.col('close').ewm_mean(alpha=alpha).alias(f'ema_{period}')
                        )

            for period in indicator_config.get('sma_periods', []):
                if len(df) >= period:
                    if POLARS_TALIB_AVAILABLE:
                        # Use polars-talib for SMA
                        df_with_indicators = df_with_indicators.with_columns(
                            ta.sma(pl.col('close'), period).alias(f'sma_{period}')
                        )
                    else:
                        # Fallback: Manual SMA calculation using Polars
                        df_with_indicators = df_with_indicators.with_columns(
                            pl.col('close').rolling_mean(window_size=period).alias(f'sma_{period}')
                        )

            # RSI calculation
            for period in indicator_config.get('rsi_periods', []):
                if len(df) >= period:
                    if POLARS_TALIB_AVAILABLE:
                        # Use polars-talib for RSI
                        df_with_indicators = df_with_indicators.with_columns(
                            ta.rsi(pl.col('close'), period).alias(f'rsi_{period}')
                        )
                    else:
                        # Fallback: Manual RSI calculation using Polars
                        df_with_indicators = df_with_indicators.with_columns([
                            # Calculate price changes
                            (pl.col('close') - pl.col('close').shift(1)).alias('price_change'),
                        ]).with_columns([
                            # Separate gains and losses
                            pl.when(pl.col('price_change') > 0)
                            .then(pl.col('price_change'))
                            .otherwise(0).alias('gain'),
                            pl.when(pl.col('price_change') < 0)
                            .then(-pl.col('price_change'))
                            .otherwise(0).alias('loss')
                        ]).with_columns([
                            # Calculate average gains and losses
                            pl.col('gain').ewm_mean(alpha=1.0/period).alias('avg_gain'),
                            pl.col('loss').ewm_mean(alpha=1.0/period).alias('avg_loss')
                        ]).with_columns([
                            # Calculate RSI
                            (100 - (100 / (1 + (pl.col('avg_gain') / pl.col('avg_loss'))))).alias(f'rsi_{period}')
                        ])

            # MACD calculation
            macd_config = indicator_config.get('macd_config', {})
            fast_period = macd_config.get('fast', 12)
            slow_period = macd_config.get('slow', 26)
            signal_period = macd_config.get('signal', 9)

            if len(df) >= slow_period:
                if POLARS_TALIB_AVAILABLE:
                    # Use polars-talib for MACD
                    df_with_indicators = df_with_indicators.with_columns([
                        ta.macd(pl.col('close'), fast_period, slow_period, signal_period).alias('macd_line'),
                        ta.macd_signal(pl.col('close'), fast_period, slow_period, signal_period).alias('macd_signal'),
                        ta.macd_histogram(pl.col('close'), fast_period, slow_period, signal_period).alias('macd_histogram')
                    ])
                else:
                    # Fallback: Manual MACD calculation using Polars
                    fast_alpha = 2.0 / (fast_period + 1)
                    slow_alpha = 2.0 / (slow_period + 1)
                    signal_alpha = 2.0 / (signal_period + 1)

                    df_with_indicators = df_with_indicators.with_columns([
                        pl.col('close').ewm_mean(alpha=fast_alpha).alias('ema_fast'),
                        pl.col('close').ewm_mean(alpha=slow_alpha).alias('ema_slow')
                    ]).with_columns([
                        (pl.col('ema_fast') - pl.col('ema_slow')).alias('macd_line')
                    ]).with_columns([
                        pl.col('macd_line').ewm_mean(alpha=signal_alpha).alias('macd_signal')
                    ]).with_columns([
                        (pl.col('macd_line') - pl.col('macd_signal')).alias('macd_histogram')
                    ])

            # ATR calculation
            atr_period = indicator_config.get('atr_period', 14)
            if len(df) >= atr_period:
                if POLARS_TALIB_AVAILABLE:
                    df_with_indicators = df_with_indicators.with_columns(
                        ta.atr(pl.col('high'), pl.col('low'), pl.col('close'), atr_period).alias('atr')
                    )
                else:
                    # Manual ATR calculation
                    df_with_indicators = df_with_indicators.with_columns([
                        # True Range calculation
                        pl.max_horizontal([
                            pl.col('high') - pl.col('low'),
                            (pl.col('high') - pl.col('close').shift(1)).abs(),
                            (pl.col('low') - pl.col('close').shift(1)).abs()
                        ]).alias('true_range')
                    ]).with_columns([
                        pl.col('true_range').rolling_mean(window_size=atr_period).alias('atr')
                    ])

            # VWAP calculation (Volume Weighted Average Price)
            df_with_indicators = df_with_indicators.with_columns([
                (pl.col('close') * pl.col('volume')).alias('price_volume'),
                pl.col('volume').cum_sum().alias('cumulative_volume'),
                (pl.col('close') * pl.col('volume')).cum_sum().alias('cumulative_price_volume')
            ]).with_columns([
                (pl.col('cumulative_price_volume') / pl.col('cumulative_volume')).alias('vwap')
            ])

            # Execute lazy evaluation and get the result
            result_df = df_with_indicators.collect()

            # Extract the latest values for each indicator
            if len(result_df) > 0:
                latest_row = result_df.row(-1, named=True)  # Get last row as dict

                # Set EMA values
                for period in indicator_config.get('ema_periods', []):
                    if f'ema_{period}' in latest_row:
                        setattr(indicators, f'ema_{period}', latest_row[f'ema_{period}'])

                # Set SMA values
                for period in indicator_config.get('sma_periods', []):
                    if f'sma_{period}' in latest_row:
                        setattr(indicators, f'sma_{period}', latest_row[f'sma_{period}'])

                # Set RSI values
                for period in indicator_config.get('rsi_periods', []):
                    if f'rsi_{period}' in latest_row:
                        setattr(indicators, f'rsi_{period}', latest_row[f'rsi_{period}'])

                # Set MACD values
                if 'macd_line' in latest_row:
                    indicators.macd = latest_row['macd_line']
                if 'macd_signal' in latest_row:
                    indicators.macd_signal = latest_row['macd_signal']
                if 'macd_histogram' in latest_row:
                    indicators.macd_histogram = latest_row['macd_histogram']

                # Set other indicators
                if 'atr' in latest_row:
                    indicators.atr = latest_row['atr']
                if 'vwap' in latest_row:
                    indicators.vwap = latest_row['vwap']

            # Store indicators with timeframe key
            if symbol not in self.indicators:
                self.indicators[symbol] = {}
            self.indicators[symbol][timeframe] = indicators
            logger.info(f"[INDICATORS] Calculated {timeframe} indicators for {symbol}: RSI={getattr(indicators, 'rsi_14', 'N/A')}, EMA20={getattr(indicators, 'ema_20', 'N/A')}")

        except Exception as e:
            logger.error(f"Error calculating indicators for {symbol} {timeframe}: {e}")

    def _get_min_candles_for_timeframe(self, timeframe: str) -> int:
        """Get minimum candles needed for indicator calculation based on timeframe"""
        # Base requirements for different timeframes
        timeframe_requirements = {
            '1min': 50,   # 50 minutes of data
            '5min': 20,   # 100 minutes of data (20 * 5min)
            '15min': 10,  # 150 minutes of data (10 * 15min)
            '30min': 8,   # 240 minutes of data (8 * 30min)
            '1hr': 6      # 360 minutes of data (6 * 1hr)
        }
        return timeframe_requirements.get(timeframe, 20)

    async def _detect_regime_changes(self):
        """Detect market regime changes"""
        try:
            # Get market breadth data
            breadth_data = await self._calculate_market_breadth()

            # Get volatility data
            volatility_data = await self._calculate_market_volatility()

            # Determine market regime
            new_regime = self._determine_market_regime(breadth_data, volatility_data)

            # Check for regime change
            if self.market_regime is None or self.market_regime.regime != new_regime.regime:
                logger.info(f"[STATUS] Market regime changed: {self.market_regime.regime if self.market_regime else 'None'} -> {new_regime.regime}")

                # Store new regime
                old_regime = self.market_regime
                self.market_regime = new_regime

                # Notify regime change
                await self._notify_regime_change(old_regime, new_regime)

                # Call regime change handlers
                for handler in self.regime_change_handlers:
                    try:
                        await handler(old_regime, new_regime)
                    except Exception as e:
                        logger.error(f"[ERROR] Error in regime change handler: {e}")

        except Exception as e:
            logger.error(f"[ERROR] Error detecting regime changes: {e}")

    async def _calculate_market_breadth(self) -> Dict[str, float]:
        """Calculate market breadth metrics"""
        try:
            breadth_data = {}

            # Calculate percentage of stocks above EMA20
            symbols = self.config.market_data_config.get('symbols', [])
            above_ema20 = 0
            total_symbols = 0

            for symbol in symbols:
                # Use 15min timeframe for market breadth analysis (more stable)
                indicators = self.get_indicators(symbol, '15min')
                if indicators and hasattr(indicators, 'ema_20') and indicators.ema_20:
                    total_symbols += 1
                    # Get current price from 15min data
                    candles = list(self.market_data[symbol].get('15min', []))
                    if candles:
                        current_price = candles[-1].close
                        if current_price > indicators.ema_20:
                            above_ema20 += 1

            if total_symbols > 0:
                breadth_data['above_ema20_percent'] = (above_ema20 / total_symbols) * 100
            else:
                breadth_data['above_ema20_percent'] = 50  # Neutral

            return breadth_data

        except Exception as e:
            logger.error(f"[ERROR] Error calculating market breadth: {e}")
            return {}

    async def _calculate_market_volatility(self) -> Dict[str, float]:
        """Calculate market volatility metrics using PyArrow"""
        try:
            volatility_data = {}

            # Calculate average ATR across symbols using PyArrow (use 15min for stability)
            atr_values = []
            for symbol in self.indicators:
                indicators = self.get_indicators(symbol, '15min')
                if indicators and hasattr(indicators, 'atr') and indicators.atr:
                    atr_values.append(indicators.atr)

            if atr_values:
                # Use PyArrow for fast vectorized operations
                atr_array = pa.array(atr_values)
                volatility_data['avg_atr'] = pc.mean(atr_array).as_py()

                # Calculate percentile using PyArrow
                atr_array = pa.array(atr_values)
                sorted_atr = pc.sort(atr_array)
                target_value = volatility_data['avg_atr']
                count_below = pc.sum(pc.less(sorted_atr, target_value)).as_py()
                volatility_data['volatility_percentile'] = (count_below / len(atr_values)) * 100

            return volatility_data

        except Exception as e:
            logger.error(f"[ERROR] Error calculating market volatility: {e}")
            return {}

    def _determine_market_regime(self, breadth_data: Dict[str, float], volatility_data: Dict[str, float]) -> MarketRegime:
        """Determine current market regime"""
        try:
            env_config = self.config.environment_config.get('regime_detection', {})

            # Get metrics
            breadth_percent = breadth_data.get('above_ema20_percent', 50)
            volatility_percentile = volatility_data.get('volatility_percentile', 50)

            # Determine regime
            breadth_threshold = env_config.get('breadth_threshold', 60) * 100

            if breadth_percent > breadth_threshold:
                regime = 'bull'
                confidence = min((breadth_percent - 50) / 50, 1.0)
            elif breadth_percent < (100 - breadth_threshold):
                regime = 'bear'
                confidence = min((50 - breadth_percent) / 50, 1.0)
            else:
                regime = 'sideways'
                confidence = 1.0 - abs(breadth_percent - 50) / 50

            # Determine volatility level
            vol_config = self.config.environment_config.get('volatility', {})
            if volatility_percentile > vol_config.get('high_vol_threshold', 75):
                volatility_level = 'high'
            elif volatility_percentile < vol_config.get('low_vol_threshold', 25):
                volatility_level = 'low'
            else:
                volatility_level = 'medium'

            return MarketRegime(
                regime=regime,
                confidence=confidence,
                volatility_level=volatility_level,
                trend_strength=abs(breadth_percent - 50) / 50,
                market_breadth=breadth_percent,
                correlation_level=0.5,  # Placeholder
                timestamp=datetime.now()
            )

        except Exception as e:
            logger.error(f"[ERROR] Error determining market regime: {e}")
            return MarketRegime(
                regime='sideways',
                confidence=0.5,
                volatility_level='medium',
                trend_strength=0.0,
                market_breadth=50.0,
                correlation_level=0.5,
                timestamp=datetime.now()
            )

    async def _check_trading_signals(self, symbol: str):
        """Check for trading signals for a symbol"""
        try:
            # Check signals on multiple timeframes
            timeframes = ['5min', '15min', '30min']

            for timeframe in timeframes:
                await self._check_trading_signals_for_timeframe(symbol, timeframe)

        except Exception as e:
            logger.error(f"[ERROR] Error checking trading signals for {symbol}: {e}")

    async def _check_trading_signals_for_timeframe(self, symbol: str, timeframe: str):
        """Check for trading signals for a symbol on a specific timeframe"""
        try:
            indicators = self.get_indicators(symbol, timeframe)
            if not indicators:
                return

            candles = list(self.market_data[symbol].get(timeframe, []))
            if not candles or len(candles) < 20:
                return

            current_candle = candles[-1]

            # Get AI strategy recommendation if available
            strategy_recommendation = None
            if self.ai_agent and self.market_regime:
                strategy_recommendation = await self._get_ai_strategy_recommendation(symbol, indicators)

            # Check entry conditions
            entry_signals = await self._check_entry_conditions(symbol, indicators, current_candle)

            # Generate signals
            for signal_data in entry_signals:
                signal = await self._create_trading_signal(symbol, signal_data, strategy_recommendation)
                if signal:
                    await self._process_trading_signal(signal)

        except Exception as e:
            logger.error(f"[ERROR] Error checking trading signals for {symbol}: {e}")

    async def _get_ai_strategy_recommendation(self, symbol: str, indicators: MarketIndicators) -> Optional[Dict[str, Any]]:
        """Get AI strategy recommendation"""
        try:
            if not self.ai_agent:
                return None

            # Prepare features for AI model
            features = self._prepare_ai_features(symbol, indicators)

            # Get prediction from AI model
            # This would depend on the AI agent implementation
            # prediction = await self.ai_agent.predict(features, self.market_regime.regime)

            # Placeholder implementation
            return {
                'strategy': 'momentum_breakout',
                'confidence': 0.75,
                'expected_return': 0.02
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting AI strategy recommendation: {e}")
            return None

    def _prepare_ai_features(self, symbol: str, indicators: MarketIndicators) -> Dict[str, float]:
        """Prepare features for AI model"""
        features = {}

        # Add indicator values
        for attr_name in dir(indicators):
            if not attr_name.startswith('_') and attr_name not in ['symbol', 'timestamp']:
                value = getattr(indicators, attr_name)
                if isinstance(value, (int, float)) and value is not None:
                    features[attr_name] = value

        # Add market regime features
        if self.market_regime:
            features['market_breadth'] = self.market_regime.market_breadth
            features['volatility_level'] = {'low': 0, 'medium': 1, 'high': 2}[self.market_regime.volatility_level]
            features['regime'] = {'bear': 0, 'sideways': 1, 'bull': 2}[self.market_regime.regime]

        return features

    async def _check_entry_conditions(self, symbol: str, indicators: MarketIndicators, current_candle: OHLCV) -> List[Dict[str, Any]]:
        """Check entry conditions for various strategies"""
        signals = []

        try:
            # Example: RSI oversold/overbought conditions
            if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                if indicators.rsi_14 < 30:  # Oversold
                    signals.append({
                        'strategy': 'rsi_oversold',
                        'action': 'BUY',
                        'confidence': (30 - indicators.rsi_14) / 30
                    })
                elif indicators.rsi_14 > 70:  # Overbought
                    signals.append({
                        'strategy': 'rsi_overbought',
                        'action': 'SELL',
                        'confidence': (indicators.rsi_14 - 70) / 30
                    })

            # Example: EMA crossover
            if (hasattr(indicators, 'ema_5') and hasattr(indicators, 'ema_20') and
                indicators.ema_5 and indicators.ema_20):

                if indicators.ema_5 > indicators.ema_20:  # Bullish crossover
                    signals.append({
                        'strategy': 'ema_crossover',
                        'action': 'BUY',
                        'confidence': min((indicators.ema_5 - indicators.ema_20) / indicators.ema_20, 1.0)
                    })

            # Add more strategy conditions here

        except Exception as e:
            logger.error(f"[ERROR] Error checking entry conditions: {e}")

        return signals

    async def _create_trading_signal(self, symbol: str, signal_data: Dict[str, Any], ai_recommendation: Optional[Dict[str, Any]]) -> Optional[TradingSignal]:
        """Create a trading signal"""
        try:
            candles = list(self.market_data[symbol].get('1min', []))
            if not candles:
                return None

            current_price = candles[-1].close

            # Calculate position size
            risk_config = self.config.strategy_config.get('risk_management', {})
            position_size_percent = risk_config.get('max_position_size_percent', 1.0)

            # Calculate stop loss and target based on ATR or fixed percentage
            # Use 15min indicators for more stable ATR calculation
            indicators = self.get_indicators(symbol, '15min')
            atr = getattr(indicators, 'atr', None) if indicators else None
            atr = atr or current_price * 0.02  # 2% fallback

            if signal_data['action'] == 'BUY':
                stop_loss = current_price - (2 * atr)
                target = current_price + (3 * atr)  # 1.5 R:R ratio
            else:
                stop_loss = current_price + (2 * atr)
                target = current_price - (3 * atr)

            # Calculate quantity (placeholder - needs actual capital amount)
            capital = 100000  # Rs 1,00,000 default
            risk_amount = capital * (position_size_percent / 100)
            quantity = int(risk_amount / abs(current_price - stop_loss))

            signal = TradingSignal(
                symbol=symbol,
                strategy=signal_data['strategy'],
                action=signal_data['action'],
                price=current_price,
                target=target,
                stop_loss=stop_loss,
                quantity=quantity,
                confidence=signal_data['confidence'],
                market_regime=self.market_regime.regime if self.market_regime else 'unknown',
                timestamp=datetime.now(),
                context={
                    'indicators': asdict(indicators),
                    'market_regime': asdict(self.market_regime) if self.market_regime else {},
                    'ai_recommendation': ai_recommendation
                }
            )

            return signal

        except Exception as e:
            logger.error(f"[ERROR] Error creating trading signal: {e}")
            return None

    async def _process_trading_signal(self, signal: TradingSignal):
        """Process and validate trading signal"""
        try:
            # Validate signal
            if not await self._validate_signal(signal):
                return

            # Add to active signals
            self.active_signals.append(signal)

            # Send notifications
            await self._send_signal_notification(signal)

            # Log signal
            await self._log_signal(signal)

            # Call signal handlers
            for handler in self.signal_handlers:
                try:
                    await handler(signal)
                except Exception as e:
                    logger.error(f"[ERROR] Error in signal handler: {e}")

            logger.info(f"[SIGNAL] Signal generated: {signal.symbol} | {signal.strategy} | {signal.action}")

        except Exception as e:
            logger.error(f"[ERROR] Error processing trading signal: {e}")

    async def _validate_signal(self, signal: TradingSignal) -> bool:
        """Validate trading signal against filters"""
        try:
            entry_config = self.config.strategy_config.get('entry_conditions', {})

            # Check confidence threshold
            ai_config = self.config.strategy_config.get('ai_model', {})
            min_confidence = ai_config.get('confidence_threshold', 0.7)
            if signal.confidence < min_confidence:
                return False

            # Check liquidity (placeholder)
            min_liquidity = entry_config.get('min_liquidity', 1000000)
            # Would need actual volume data check

            # Check maximum daily trades
            risk_config = self.config.strategy_config.get('risk_management', {})
            max_daily_trades = risk_config.get('max_daily_trades', 10)

            today_signals = [s for s in self.active_signals
                           if s.timestamp.date() == datetime.now().date()]
            if len(today_signals) >= max_daily_trades:
                return False

            return True

        except Exception as e:
            logger.error(f"[ERROR] Error validating signal: {e}")
            return False

    async def _send_signal_notification(self, signal: TradingSignal):
        """Send signal notification via configured channels"""
        try:
            # Telegram notification
            if self.telegram_bot:
                await self._send_telegram_notification(signal)

            # Add other notification channels here (Slack, Email, etc.)

        except Exception as e:
            logger.error(f"[ERROR] Error sending signal notification: {e}")

    async def _send_telegram_notification(self, signal: TradingSignal):
        """Send Telegram notification"""
        try:
            telegram_config = self.config.notifications_config.get('telegram', {})
            chat_id = telegram_config.get('chat_id')

            if not chat_id or chat_id == "YOUR_CHAT_ID":
                return

            # Format message
            template = telegram_config.get('templates', {}).get('signal',
                "[SIGNAL] {symbol} | {strategy} | {action} | Price: {price} | Target: {target} | SL: {stop_loss}")

            message = template.format(
                symbol=signal.symbol,
                strategy=signal.strategy,
                action=signal.action,
                price=f"Rs.{signal.price:.2f}",
                target=f"Rs.{signal.target:.2f}",
                stop_loss=f"Rs.{signal.stop_loss:.2f}"
            )

            await self.telegram_bot.send_message(chat_id=chat_id, text=message)

        except Exception as e:
            logger.error(f"[ERROR] Error sending Telegram notification: {e}")

    async def _notify_regime_change(self, old_regime: Optional[MarketRegime], new_regime: MarketRegime):
        """Notify about market regime change"""
        try:
            if self.telegram_bot:
                telegram_config = self.config.notifications_config.get('telegram', {})
                chat_id = telegram_config.get('chat_id')

                if chat_id and chat_id != "YOUR_CHAT_ID":
                    template = telegram_config.get('templates', {}).get('regime_change',
                        "[STATUS] REGIME CHANGE: Market shifted to {regime} | Breadth: {breadth}%")

                    message = template.format(
                        regime=new_regime.regime.upper(),
                        breadth=f"{new_regime.market_breadth:.1f}"
                    )

                    await self.telegram_bot.send_message(chat_id=chat_id, text=message)

        except Exception as e:
            logger.error(f"[ERROR] Error notifying regime change: {e}")

    async def _log_signal(self, signal: TradingSignal):
        """Log trading signal to file"""
        try:
            signal_config = self.config.logging_config.get('signal_logging', {})
            if not signal_config.get('enable', True):
                return

            log_file = signal_config.get('signal_log_file', 'logs/signals.log')

            # Prepare log entry
            log_entry = {
                'timestamp': signal.timestamp.isoformat(),
                'symbol': signal.symbol,
                'strategy': signal.strategy,
                'action': signal.action,
                'price': signal.price,
                'target': signal.target,
                'stop_loss': signal.stop_loss,
                'quantity': signal.quantity,
                'confidence': signal.confidence,
                'market_regime': signal.market_regime
            }

            if signal_config.get('include_market_context', True):
                log_entry['context'] = signal.context

            # Write to file
            with open(log_file, 'a') as f:
                f.write(json.dumps(log_entry) + '\n')

        except Exception as e:
            logger.error(f"[ERROR] Error logging signal: {e}")

    # Control methods
    async def start(self):
        """Start the Market Monitoring Agent"""
        try:
            logger.info("[INIT] Starting Market Monitoring Agent...")

            if not self.smartapi_client:
                logger.warning("[WARN]  SmartAPI not configured, running in demo mode")

            self.is_running = True

            # Load all timeframes from live data (not training data)
            await self._load_all_timeframes_from_live_data()

            logger.info("[SUCCESS] Market Monitoring Agent started")

        except Exception as e:
            logger.error(f"[ERROR] Error starting agent: {e}")
            raise

    async def _ensure_historical_data_for_selected_stocks(self):
        """Ensure historical data is available for selected stocks before starting live trading"""
        try:
            logger.info("[HISTORICAL] Ensuring historical data for selected stocks...")

            # Get selected stocks from the system
            selected_stocks = await self._get_selected_stocks_for_monitoring()

            if not selected_stocks:
                logger.warning("[HISTORICAL] No selected stocks found, using default stocks")
                selected_stocks = await self._get_default_stocks_for_monitoring()

            logger.info(f"[HISTORICAL] Selected {len(selected_stocks)} stocks for monitoring: {[s['symbol'] for s in selected_stocks]}")

            # Check if we have sufficient historical data for these stocks
            missing_data_stocks = []
            for stock in selected_stocks:
                symbol = stock['symbol']
                if not await self._has_sufficient_historical_data(symbol):
                    missing_data_stocks.append(stock)

            if missing_data_stocks:
                logger.info(f"[HISTORICAL] Downloading historical data for {len(missing_data_stocks)} stocks...")
                await self._download_historical_data_for_stocks(missing_data_stocks)
            else:
                logger.info("[HISTORICAL] Sufficient historical data already available")

            # Load historical data into memory for all selected stocks
            await self._load_historical_data_for_stocks(selected_stocks)

            # Store selected stocks for WebSocket subscription
            self._selected_stocks_for_monitoring = selected_stocks

        except Exception as e:
            logger.error(f"[ERROR] Error ensuring historical data: {e}")

    async def _get_selected_stocks_for_monitoring(self) -> List[Dict]:
        """Get the selected stocks for monitoring from the stock selection process"""
        try:
            # Check if we have a dynamic stock selection workflow
            if hasattr(self, 'stock_selection_workflow'):
                # Get stocks from dynamic selection
                selected_stocks = await self.stock_selection_workflow.get_selected_stocks()
                if selected_stocks:
                    return [{'symbol': stock.symbol, 'token': stock.token, 'exchange': stock.exchange}
                           for stock in selected_stocks]

            # Check if stocks are configured in config
            configured_symbols = self.config.market_data_config.get('symbols', [])
            if configured_symbols:
                # Convert symbols to stock info format
                selected_stocks = []
                for symbol in configured_symbols:
                    token = self._get_token_for_symbol(symbol)
                    if token:
                        selected_stocks.append({
                            'symbol': symbol,
                            'token': token,
                            'exchange': 'NSE'
                        })
                return selected_stocks

            return []

        except Exception as e:
            logger.error(f"[ERROR] Error getting selected stocks: {e}")
            return []

    async def _get_default_stocks_for_monitoring(self) -> List[Dict]:
        """Get default stocks for monitoring when no selection is available"""
        try:
            # Use top 6 liquid stocks as default
            default_stocks = [
                {"symbol": "RELIANCE", "token": "2885", "exchange": "NSE"},
                {"symbol": "HDFCBANK", "token": "1333", "exchange": "NSE"},
                {"symbol": "INFY", "token": "1594", "exchange": "NSE"},
                {"symbol": "TCS", "token": "11536", "exchange": "NSE"},
                {"symbol": "ICICIBANK", "token": "4963", "exchange": "NSE"},
                {"symbol": "BHARTIARTL", "token": "10604", "exchange": "NSE"}
            ]

            logger.info(f"[DEFAULT] Using default stocks: {[s['symbol'] for s in default_stocks]}")
            return default_stocks

        except Exception as e:
            logger.error(f"[ERROR] Error getting default stocks: {e}")
            return []

    async def start_background_tasks(self):
        """Start all background monitoring and WebSocket tasks"""
        try:
            logger.info("[INIT] Starting Market Monitoring Agent background tasks...")

            # Step 1: Download historical data for selected stocks first
            await self._ensure_historical_data_for_selected_stocks()

            # Step 2: Update Enhanced WebSocket Service with selected stocks
            if hasattr(self, '_selected_stocks_for_monitoring'):
                await self._update_websocket_with_selected_stocks()

            # Step 3: Start Enhanced WebSocket Service (preferred)
            if hasattr(self, 'enhanced_websocket') and self.enhanced_websocket:
                logger.info("[CONNECT] Starting Enhanced WebSocket Service...")
                success = await self.enhanced_websocket.initialize()
                if success:
                    await self.enhanced_websocket.start()
                    logger.info("[SUCCESS] Enhanced WebSocket Service started successfully")
                else:
                    logger.error("[ERROR] Enhanced WebSocket Service initialization failed")

            # Start WebSocket connection (fallback)
            elif hasattr(self, 'websocket_manager') and self.websocket_manager:
                logger.info("[CONNECT] Starting Enhanced WebSocket connection...")
                # Use enhanced WebSocket manager
                connection_success = await self.websocket_manager.connect()
                if connection_success:
                    logger.info("[SUCCESS] Enhanced WebSocket connected successfully")
                else:
                    logger.error("[ERROR] Enhanced WebSocket connection failed")

            elif self.websocket_client:
                logger.info("[CONNECT] Starting fallback WebSocket connection in background thread...")
                # Use asyncio.to_thread for blocking calls
                self._websocket_task = asyncio.create_task(asyncio.to_thread(self.websocket_client.connect))
                # Give it a moment to connect
                await asyncio.sleep(0.5)

            # Process any pending operations that couldn't be scheduled during WebSocket setup
            self._process_pending_operations()

            # Start monitoring tasks as background tasks (don't await them)
            monitoring_tasks = [
                asyncio.create_task(self._performance_monitoring_loop()),
                asyncio.create_task(self._cleanup_loop()),
                asyncio.create_task(self._heartbeat_loop()),
                asyncio.create_task(self._enhanced_signal_generation_loop())  # Add enhanced signal generation
            ]

            logger.info("[SUCCESS] Market Monitoring Agent background tasks started")

            # Store tasks for cleanup later
            self._monitoring_tasks = monitoring_tasks

        except Exception as e:
            logger.error(f"[ERROR] Error starting background tasks: {e}")
            raise

    async def _has_sufficient_historical_data(self, symbol: str) -> bool:
        """Check if we have sufficient historical data for a symbol"""
        try:
            import polars as pl
            from datetime import datetime, timedelta

            # Check for live data first, then historical data
            data_paths = [
                "data/live/live_5min.parquet",
                "data/historical/historical_5min.parquet"
            ]

            for path in data_paths:
                if os.path.exists(path):
                    df = pl.read_parquet(path)
                    symbol_data = df.filter(pl.col('symbol') == symbol)

                    if len(symbol_data) > 0:
                        # Check if we have at least 30 days of data
                        latest_date = symbol_data.select(pl.col('date').max()).item()
                        earliest_date = symbol_data.select(pl.col('date').min()).item()

                        if isinstance(latest_date, str):
                            latest_date = datetime.strptime(latest_date, '%Y-%m-%d').date()
                        if isinstance(earliest_date, str):
                            earliest_date = datetime.strptime(earliest_date, '%Y-%m-%d').date()

                        days_available = (latest_date - earliest_date).days

                        if days_available >= 30:  # At least 30 days of data
                            logger.debug(f"[CHECK] {symbol} has {days_available} days of data - sufficient")
                            return True

            logger.debug(f"[CHECK] {symbol} needs historical data download")
            return False

        except Exception as e:
            logger.error(f"[ERROR] Error checking historical data for {symbol}: {e}")
            return False

    async def _download_historical_data_for_stocks(self, stocks: List[Dict]):
        """Download historical data for specific stocks"""
        try:
            logger.info(f"[DOWNLOAD] Downloading 35 days of historical data for {len(stocks)} stocks...")

            # Use the existing download method but with selected stocks only
            success = await self._download_selected_stocks_data(stocks, days_back=35)

            if success:
                logger.info("[DOWNLOAD] Historical data download completed successfully")
            else:
                logger.error("[DOWNLOAD] Historical data download failed")

        except Exception as e:
            logger.error(f"[ERROR] Error downloading historical data: {e}")

    async def _download_selected_stocks_data(self, stocks: List[Dict], days_back: int = 35) -> bool:
        """Download historical data for selected stocks only"""
        try:
            from datetime import datetime, timedelta
            import polars as pl

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            logger.info(f"[DOWNLOAD] Downloading data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            all_data = []
            successful_downloads = 0

            for stock in stocks:
                symbol = stock['symbol']
                token = stock['token']
                exchange = stock.get('exchange', 'NSE')

                logger.info(f"[DOWNLOAD] Processing {symbol}...")

                # Download with retry logic
                df = await self._download_from_smartapi_with_retry(
                    symbol, token, exchange, start_date, end_date
                )

                if df is not None and len(df) > 0:
                    all_data.append(df)
                    successful_downloads += 1
                    logger.info(f"[SUCCESS] Downloaded {symbol}: {len(df)} records")
                else:
                    logger.warning(f"[FAILED] Failed to download {symbol}")

                # Rate limiting
                await asyncio.sleep(0.75)

            # Save data if we have any
            if all_data:
                await self._save_live_data_parquet(all_data, start_date, end_date)
                logger.info(f"[SUCCESS] Downloaded data for {successful_downloads}/{len(stocks)} stocks")
                return True
            else:
                logger.error("[ERROR] No data downloaded")
                return False

        except Exception as e:
            logger.error(f"[ERROR] Error downloading selected stocks data: {e}")
            return False

    async def _load_historical_data_for_stocks(self, stocks: List[Dict]):
        """Load historical data for selected stocks into memory"""
        try:
            logger.info(f"[LOAD] Loading historical data for {len(stocks)} stocks into memory...")

            # Generate and load all timeframes
            await self._generate_and_load_timeframes()

            # Verify data is loaded for selected stocks
            loaded_symbols = list(self.market_data.keys())
            selected_symbols = [stock['symbol'] for stock in stocks]

            missing_symbols = set(selected_symbols) - set(loaded_symbols)
            if missing_symbols:
                logger.warning(f"[LOAD] Missing data for symbols: {missing_symbols}")
            else:
                logger.info(f"[LOAD] Successfully loaded data for all {len(selected_symbols)} selected stocks")

        except Exception as e:
            logger.error(f"[ERROR] Error loading historical data: {e}")

    async def _update_websocket_with_selected_stocks(self):
        """Update Enhanced WebSocket Service with selected stocks"""
        try:
            if hasattr(self, 'enhanced_websocket') and hasattr(self, '_selected_stocks_for_monitoring'):
                selected_symbols = [stock['symbol'] for stock in self._selected_stocks_for_monitoring]
                selected_tokens = {stock['symbol']: stock['token'] for stock in self._selected_stocks_for_monitoring}

                # Update the WebSocket service with selected stocks only
                self.enhanced_websocket.update_selected_symbols(selected_tokens)

                logger.info(f"[WEBSOCKET] Updated WebSocket service with {len(selected_symbols)} selected stocks: {selected_symbols}")

        except Exception as e:
            logger.error(f"[ERROR] Error updating WebSocket with selected stocks: {e}")

    async def stop(self):
        """Stop the Market Monitoring Agent with enhanced shutdown mechanism"""
        try:
            logger.info("[STOP] Stopping Market Monitoring Agent...")

            self.is_running = False

            # Enhanced task cancellation with better error handling
            if hasattr(self, '_monitoring_tasks'):
                logger.info(f"[STOP] Cancelling {len(self._monitoring_tasks)} background tasks...")

                # Cancel all tasks immediately
                cancelled_tasks = []
                for i, task in enumerate(self._monitoring_tasks):
                    if not task.done():
                        task.cancel()
                        cancelled_tasks.append(task)
                        logger.debug(f"[STOP] Cancelled task {i+1}/{len(self._monitoring_tasks)}")

                # Wait for cancellation with progressive timeout
                if cancelled_tasks:
                    try:
                        # First attempt: 3 seconds
                        await asyncio.wait_for(
                            asyncio.gather(*cancelled_tasks, return_exceptions=True),
                            timeout=3.0
                        )
                        logger.info("[STOP] All background tasks cancelled successfully")
                    except asyncio.TimeoutError:
                        logger.warning("[STOP] Some tasks didn't cancel within 3s, trying force cancellation...")

                        # Force cancellation of remaining tasks
                        for task in cancelled_tasks:
                            if not task.done():
                                try:
                                    task.cancel()
                                    # Give each task a brief moment to cancel
                                    await asyncio.sleep(0.1)
                                except Exception:
                                    pass

                        logger.warning("[STOP] Force cancellation completed")
                    except Exception as e:
                        logger.warning(f"[STOP] Task cancellation error (ignoring): {e}")

            # Close Enhanced WebSocket Service
            if hasattr(self, 'enhanced_websocket') and self.enhanced_websocket:
                try:
                    logger.info("[STOP] Stopping Enhanced WebSocket Service...")
                    await self.enhanced_websocket.stop()
                    logger.info("[STOP] Enhanced WebSocket Service stopped")
                except Exception as e:
                    logger.warning(f"[STOP] Enhanced WebSocket Service stop error (ignoring): {e}")

            # Close WebSocket connection with timeout
            elif hasattr(self, 'websocket_manager') and self.websocket_manager:
                try:
                    logger.info("[STOP] Closing Enhanced WebSocket connection...")
                    await self.websocket_manager.disconnect()
                    logger.info("[STOP] Enhanced WebSocket connection closed")
                except Exception as e:
                    logger.warning(f"[STOP] Enhanced WebSocket close error (ignoring): {e}")

            elif self.websocket_client:
                try:
                    logger.info("[STOP] Closing fallback WebSocket connection...")
                    # Ensure close_connection is called in a thread if it's blocking
                    await asyncio.wait_for(
                        asyncio.to_thread(self.websocket_client.close_connection),
                        timeout=1.0
                    )
                    logger.info("[STOP] Fallback WebSocket connection closed")
                except asyncio.TimeoutError:
                    logger.warning("[STOP] WebSocket close timed out")
                except Exception as e:
                    logger.warning(f"[STOP] WebSocket close error (ignoring): {e}")

            # State saving with timeout (skip in testing mode)
            testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
            if not testing_mode:
                error_config = self.config.error_handling_config.get('shutdown', {})
                if error_config.get('save_state', True):
                    try:
                        logger.info("[STOP] Saving agent state...")
                        await asyncio.wait_for(self._save_state(), timeout=2.0)
                        logger.info("[STOP] Agent state saved")
                    except asyncio.TimeoutError:
                        logger.warning("[STOP] State saving timed out, skipping for faster shutdown")
                    except Exception as e:
                        logger.warning(f"[STOP] State saving failed: {e}")
            else:
                logger.info("[STOP] Skipping state save in testing mode")

            logger.info("[SUCCESS] Market Monitoring Agent stopped gracefully")

        except Exception as e:
            logger.error(f"[ERROR] Error during shutdown: {e}")
            # Don't re-raise to ensure shutdown completes

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        while self.is_running:
            try:
                # Update performance metrics
                self.performance_metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'active_signals': len(self.active_signals),
                    'subscribed_symbols': len(self.subscribed_symbols),
                    'market_regime': self.market_regime.regime if self.market_regime else 'unknown',
                    'system_info': get_system_info()
                }

                # Enhanced logging for debugging
                logger.info(f"[MONITOR] Performance: {len(self.active_signals)} signals, "
                           f"{len(self.subscribed_symbols)} symbols, "
                           f"regime: {self.market_regime.regime if self.market_regime else 'unknown'}, "
                           f"connected: {self.is_connected}")

                # Log market data status
                total_symbols_with_data = len(self.market_data)
                logger.info(f"[MONITOR] Market data loaded for {total_symbols_with_data} symbols")

                # Log recent activity
                if hasattr(self, '_last_tick_time'):
                    time_since_last_tick = (datetime.now() - self._last_tick_time).total_seconds()
                    logger.info(f"[MONITOR] Last tick received {time_since_last_tick:.1f} seconds ago")
                else:
                    logger.info("[MONITOR] No ticks received yet")

                # Log performance metrics
                perf_config = self.config.logging_config.get('performance_logging', {})
                if perf_config.get('enable', True):
                    metrics_file = perf_config.get('metrics_file', 'logs/performance_metrics.log')
                    with open(metrics_file, 'a') as f:
                        f.write(json.dumps(self.performance_metrics) + '\n')

                # Wait for next interval
                interval = perf_config.get('log_interval', 300)
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitoring: {e}")
                await asyncio.sleep(60)

    async def _cleanup_loop(self):
        """Cleanup loop for memory management with optimized intervals"""
        while self.is_running:
            try:
                # Clean up old signals
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.active_signals = [s for s in self.active_signals if s.timestamp > cutoff_time]

                # Clean up old market data if needed
                memory_config = self.config.performance_config.get('memory', {})
                base_cleanup_interval = memory_config.get('cleanup_interval', 3600)

                # Use shorter intervals in testing mode for faster responsiveness
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                cleanup_interval = 30 if testing_mode else base_cleanup_interval

                await asyncio.sleep(cleanup_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in cleanup loop: {e}")
                error_sleep = 10 if os.getenv('TESTING_MODE', 'false').lower() == 'true' else 3600
                await asyncio.sleep(error_sleep)

    async def _heartbeat_loop(self):
        """Heartbeat loop for connection monitoring with optimized intervals"""
        while self.is_running:
            try:
                # Check WebSocket connection
                if self.websocket_client and not self.is_connected:
                    logger.warning("[CONNECT] WebSocket disconnected, attempting reconnection...")
                    await self._reconnect_websocket()

                # Wait for next heartbeat with shorter intervals in testing mode
                ws_config = self.config.smartapi_config.get('websocket', {})
                base_interval = ws_config.get('heartbeat_interval', 30)

                # Use shorter intervals in testing mode for faster responsiveness
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                interval = 5 if testing_mode else base_interval

                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in heartbeat loop: {e}")
                error_sleep = 5 if os.getenv('TESTING_MODE', 'false').lower() == 'true' else 30
                await asyncio.sleep(error_sleep)

    async def _reconnect_websocket(self):
        """Reconnect WebSocket with retry logic"""
        try:
            ws_config = self.config.smartapi_config.get('websocket', {})
            max_attempts = ws_config.get('reconnect_attempts', 5)
            delay = ws_config.get('reconnect_delay', 5)

            for attempt in range(max_attempts):
                try:
                    logger.info(f"[CONNECT] Reconnection attempt {attempt + 1}/{max_attempts}")

                    if self.websocket_client:
                        self.websocket_client.connect()

                        # Wait a bit to see if connection succeeds
                        await asyncio.sleep(2)

                        if self.is_connected:
                            logger.info("[SUCCESS] WebSocket reconnected successfully")
                            return

                except Exception as e:
                    logger.error(f"[ERROR] Reconnection attempt {attempt + 1} failed: {e}")

                if attempt < max_attempts - 1:
                    await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff

            logger.error("[ERROR] All reconnection attempts failed")

        except Exception as e:
            logger.error(f"[ERROR] Error in WebSocket reconnection: {e}")

    async def _save_state(self):
        """Save agent state for recovery"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'market_regime': asdict(self.market_regime) if self.market_regime else None,
                'active_signals': [asdict(signal) for signal in self.active_signals],
                'subscribed_symbols': list(self.subscribed_symbols),
                'performance_metrics': self.performance_metrics
            }

            state_file = 'data/market_context/agent_state.json'
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2, default=str)

            logger.info("[SAVE] Agent state saved")

        except Exception as e:
            logger.error(f"[ERROR] Error saving state: {e}")

    # Event handler registration
    def add_signal_handler(self, handler: Callable[[TradingSignal], None]):
        """Add signal handler"""
        self.signal_handlers.append(handler)

    def add_regime_change_handler(self, handler: Callable[[Optional[MarketRegime], MarketRegime], None]):
        """Add regime change handler"""
        self.regime_change_handlers.append(handler)

    # Getters
    def get_market_regime(self) -> Optional[MarketRegime]:
        """Get current market regime"""
        return self.market_regime

    def get_active_signals(self) -> List[TradingSignal]:
        """Get active trading signals"""
        return self.active_signals.copy()

    def get_indicators(self, symbol: str, timeframe: str = '5min') -> Optional[MarketIndicators]:
        """Get indicators for a symbol and timeframe"""
        symbol_indicators = self.indicators.get(symbol, {})
        if isinstance(symbol_indicators, dict):
            return symbol_indicators.get(timeframe)
        else:
            # Backward compatibility: if old format, return as 5min
            return symbol_indicators if timeframe == '5min' else None

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.performance_metrics.copy()

    async def _enhanced_signal_generation_loop(self):
        """Enhanced signal generation loop that runs every 30 seconds with detailed logging"""
        signal_cycle = 0
        while self.is_running:
            try:
                signal_cycle += 1
                start_time = datetime.now()

                # Enhanced logging for signal generation cycle
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Starting enhanced signal generation cycle")
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Active symbols: {len(self.market_data)}")

                signals_generated_this_cycle = 0
                symbols_analyzed = 0

                # Check if we're in testing mode for enhanced logging
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'

                # Analyze each symbol for signals
                for symbol in list(self.market_data.keys()):
                    try:
                        symbols_analyzed += 1

                        # Enhanced logging for each symbol analysis
                        if testing_mode:
                            logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Analyzing {symbol} ({symbols_analyzed}/{len(self.market_data)})")

                        # Check if we have enough data for signal generation
                        if symbol not in self.indicators:
                            if testing_mode:
                                logger.debug(f"[SIGNAL_CYCLE_{signal_cycle}] No indicators for {symbol}, skipping")
                            continue

                        # Get latest candles for different timeframes
                        candles_5min = list(self.market_data[symbol].get('5min', []))

                        if not candles_5min or len(candles_5min) < 20:
                            if testing_mode:
                                logger.debug(f"[SIGNAL_CYCLE_{signal_cycle}] Insufficient data for {symbol} (only {len(candles_5min)} candles)")
                            continue

                        # Enhanced signal checking
                        current_candle = candles_5min[-1]
                        indicators = self.get_indicators(symbol, '5min')

                        # Log current market state for the symbol
                        if testing_mode:
                            logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] {symbol} - Price: Rs.{current_candle.close:.2f}, "
                                      f"Volume: {current_candle.volume:,}")

                            # Log key indicators if available
                            if hasattr(indicators, 'rsi_14') and indicators.rsi_14:
                                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] {symbol} - RSI(14): {indicators.rsi_14:.2f}")
                            if hasattr(indicators, 'ema_5') and hasattr(indicators, 'ema_20'):
                                if indicators.ema_5 and indicators.ema_20:
                                    logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] {symbol} - EMA(5): Rs.{indicators.ema_5:.2f}, EMA(20): Rs.{indicators.ema_20:.2f}")

                        # Check for trading signals
                        await self._check_trading_signals(symbol)

                        # Count signals generated for this symbol in this cycle
                        current_signals = len([s for s in self.active_signals
                                             if s.symbol == symbol and
                                             (datetime.now() - s.timestamp).total_seconds() < 35])

                        if current_signals > 0:
                            signals_generated_this_cycle += current_signals
                            if testing_mode:
                                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Generated {current_signals} signal(s) for {symbol}")

                        # Small delay between symbols to avoid overwhelming the system
                        await asyncio.sleep(0.1)

                    except Exception as e:
                        logger.error(f"[ERROR] Error analyzing {symbol} in signal cycle {signal_cycle}: {e}")
                        continue

                # Cycle completion logging
                cycle_duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Completed in {cycle_duration:.2f}s")
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Analyzed: {symbols_analyzed} symbols, Generated: {signals_generated_this_cycle} signals")
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Total active signals: {len(self.active_signals)}")

                # Market regime logging
                if self.market_regime:
                    logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Market regime: {self.market_regime.regime} "
                              f"(confidence: {self.market_regime.confidence:.2%})")

                # Wait for next cycle (30 seconds)
                logger.info(f"[SIGNAL_CYCLE_{signal_cycle}] Waiting 30s for next signal generation cycle...")
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced signal generation loop (cycle {signal_cycle}): {e}")
                await asyncio.sleep(30)

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN EXECUTION
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main execution function"""
    try:
        logger.info("[INIT] Starting Market Monitoring Agent...")

        # Create agent
        agent = MarketMonitoringAgent()

        # Setup agent
        await agent.setup()

        # Add example signal handler
        async def signal_handler(signal: TradingSignal):
            print(f"[STATUS] Signal received: {signal.symbol} | {signal.action} | {signal.price}")

        agent.add_signal_handler(signal_handler)

        # Add example regime change handler
        async def regime_handler(old_regime: Optional[MarketRegime], new_regime: MarketRegime):
            print(f"[METRICS] Regime changed: {old_regime.regime if old_regime else 'None'} -> {new_regime.regime}")

        agent.add_regime_change_handler(regime_handler)

        # Start agent
        await agent.start()

    except KeyboardInterrupt:
        logger.info("[STOP] Received interrupt signal")
    except Exception as e:
        logger.error(f"[ERROR] Error in main execution: {e}")
    finally:
        if 'agent' in locals():
            await agent.stop()

if __name__ == "__main__":
    # Setup basic logging for standalone execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Check dependencies
    deps = check_dependencies()
    missing_deps = [dep for dep, available in deps.items() if not available]

    if missing_deps:
        logger.warning(f"[WARN]  Missing dependencies: {missing_deps}")
        logger.info("📦 Install missing dependencies:")
        if 'smartapi' in missing_deps:
            logger.info("   pip install smartapi-python")
        if 'telegram' in missing_deps:
            logger.info("   pip install python-telegram-bot")

    # Run agent
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[EXIT] Market Monitoring Agent stopped by user")
