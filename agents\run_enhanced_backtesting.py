#!/usr/bin/env python3
"""
Runner script for Enhanced Backtesting System with Polars, PyArrow, and AsyncIO
Demonstrates usage and provides command-line interface
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_backtesting_polars import main_async, logger
import asyncio

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('backtesting.log')
        ]
    )

def validate_environment():
    """Validate that required files and directories exist"""
    errors = []

    # Helper function to check paths (try both relative and parent directory)
    def check_path(path):
        if Path(path).exists():
            return path
        elif Path(f"../{path}").exists():
            return f"../{path}"
        else:
            return None

    # Check data directory
    data_dir = check_path("data/features")
    if not data_dir:
        errors.append("data/features directory not found")
    else:
        # Check for feature files
        feature_files = list(Path(data_dir).glob("*.parquet"))
        if not feature_files:
            errors.append("No parquet files found in data/features directory")

    # Check strategies file
    strategies_file = check_path("config/strategies.yaml")
    if not strategies_file:
        errors.append("config/strategies.yaml file not found")

    # Check output directory (create if doesn't exist)
    output_dir = check_path("data/backtest")
    if not output_dir:
        Path("../data/backtest").mkdir(parents=True, exist_ok=True)
    else:
        Path(output_dir).mkdir(parents=True, exist_ok=True)

    if errors:
        logger.error("Environment validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False

    return True

def print_system_info():
    """Print system information and configuration"""
    logger.info("[CONFIG] System Information:")
    logger.info(f"  Python version: {sys.version}")
    
    try:
        import polars as pl
        logger.info(f"  Polars version: {pl.__version__}")
    except ImportError:
        logger.error("  Polars not installed!")
    
    try:
        import pyarrow as pa
        logger.info(f"  PyArrow version: {pa.__version__}")
    except ImportError:
        logger.error("  PyArrow not installed!")
    
    # Check available feature files
    data_dir = "data/features" if Path("data/features").exists() else "../data/features"
    if Path(data_dir).exists():
        feature_files = list(Path(data_dir).glob("*.parquet"))
        logger.info(f"  Available feature files: {len(feature_files)}")
        for file in feature_files:
            file_size = file.stat().st_size / (1024 * 1024 * 1024)  # GB
            logger.info(f"    - {file.name}: {file_size:.2f} GB")
    else:
        logger.info("  Available feature files: 0 (data directory not found)")

async def run_backtesting_with_config(max_symbols: int = None, max_strategies: int = None, 
                                    chunk_size: int = None, concurrent_strategies: int = None,
                                    concurrent_symbols: int = None):
    """Run backtesting with custom configuration"""
    
    # Update global configuration if provided
    if max_symbols is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.MAX_SYMBOLS = max_symbols
        logger.info(f"[CONFIG] Limited to {max_symbols} symbols for testing")
    
    if max_strategies is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.MAX_STRATEGIES = max_strategies
        logger.info(f"[CONFIG] Limited to {max_strategies} strategies for testing")
    
    if chunk_size is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.CHUNK_SIZE = chunk_size
        logger.info(f"[CONFIG] Using chunk size: {chunk_size:,}")
    
    if concurrent_strategies is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.CONCURRENT_STRATEGIES = concurrent_strategies
        logger.info(f"[CONFIG] Concurrent strategies: {concurrent_strategies}")
    
    if concurrent_symbols is not None:
        import enhanced_backtesting_polars
        enhanced_backtesting_polars.CONCURRENT_SYMBOLS = concurrent_symbols
        logger.info(f"[CONFIG] Concurrent symbols: {concurrent_symbols}")
    
    # Run the main backtesting function
    await main_async()

def main():
    """Main entry point with command-line interface"""
    parser = argparse.ArgumentParser(
        description='Enhanced Backtesting System with Polars, PyArrow, and AsyncIO',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full backtesting
  python run_enhanced_backtesting.py
  
  # Test with limited data
  python run_enhanced_backtesting.py --max-symbols 5 --max-strategies 10
  
  # Optimize for memory
  python run_enhanced_backtesting.py --chunk-size 100000 --concurrent-symbols 2
  
  # Debug mode
  python run_enhanced_backtesting.py --log-level DEBUG --max-symbols 2 --max-strategies 5
        """
    )
    
    parser.add_argument('--max-symbols', type=int, 
                       help='Limit number of symbols for testing (default: all)')
    parser.add_argument('--max-strategies', type=int,
                       help='Limit number of strategies for testing (default: all)')
    parser.add_argument('--chunk-size', type=int, default=500000,
                       help='Number of rows per chunk (default: 500000)')
    parser.add_argument('--concurrent-strategies', type=int, default=7,
                       help='Number of strategies to process concurrently (default: 7)')
    parser.add_argument('--concurrent-symbols', type=int, default=10,
                       help='Number of symbols to process concurrently (default: 10)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate environment and exit')
    parser.add_argument('--system-info', action='store_true',
                       help='Print system information and exit')
    parser.add_argument('--disable-multiprocessing', action='store_true',
                       help='Disable multiprocessing for CPU-only comparison')
    parser.add_argument('--disable-gpu', action='store_true',
                       help='Disable GPU acceleration')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    # Print system info if requested
    if args.system_info:
        print_system_info()
        return
    
    # Validate environment
    if not validate_environment():
        logger.error("[ERROR] Environment validation failed. Please fix the issues above.")
        sys.exit(1)
    
    if args.validate_only:
        logger.info("[SUCCESS] Environment validation passed!")
        return
    
    # Print system info
    print_system_info()
    
    try:
        # Apply configuration overrides
        if args.disable_multiprocessing:
            import enhanced_backtesting_polars as ebp
            ebp.USE_MULTIPROCESSING = False
            logger.info("[CONFIG] Multiprocessing disabled for comparison")

        if args.disable_gpu:
            import enhanced_backtesting_polars as ebp
            ebp.USE_GPU_ACCELERATION = False
            logger.info("[CONFIG] GPU acceleration disabled for comparison")

        # Run backtesting
        asyncio.run(run_backtesting_with_config(
            max_symbols=args.max_symbols,
            max_strategies=args.max_strategies,
            chunk_size=args.chunk_size,
            concurrent_strategies=args.concurrent_strategies,
            concurrent_symbols=args.concurrent_symbols
        ))
        
        logger.info("🎉 Backtesting completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("⏹️ Backtesting interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"[ERROR] Backtesting failed: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
