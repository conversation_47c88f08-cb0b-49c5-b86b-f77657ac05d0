#!/usr/bin/env python3
"""
Test Order Service Script

This script tests the order service to see why orders are failing.
"""

import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from py_eq
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.config import config
from services.order_service import SmartAPIOrderService
from models.order import TransactionType


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def test_order_service():
    """Test the order service to see what's failing"""
    logger = setup_logging()
    
    logger.info("🔧 Testing Order Service...")
    logger.info("=" * 60)
    
    # Check configuration
    logger.info("📋 Configuration Check:")
    logger.info(f"Paper Trading: {config.trading.paper_trading}")
    logger.info(f"SmartAPI Config Valid: {config.smartapi.validate()}")
    
    if not config.smartapi.validate():
        logger.error("❌ SmartAPI configuration is invalid!")
        logger.error("Required environment variables:")
        logger.error("- SMARTAPI_API_KEY")
        logger.error("- SMARTAPI_USERNAME") 
        logger.error("- SMARTAPI_PASSWORD")
        logger.error("- SMARTAPI_TOTP_TOKEN")
        return
    
    # Initialize order service
    try:
        logger.info("🔧 Initializing SmartAPI Order Service...")
        order_service = SmartAPIOrderService(config.smartapi, logger)
        
        # Test authentication
        logger.info("🔐 Testing authentication...")
        auth_result = order_service.authenticate()
        logger.info(f"Authentication result: {auth_result}")
        
        if not auth_result:
            logger.error("❌ Authentication failed!")
            return
        
        # Test market hours check
        from utils.helpers import is_market_open
        market_open = is_market_open()
        logger.info(f"Market open: {market_open}")
        
        if not market_open:
            logger.warning("⚠️ Market is currently closed!")
            logger.warning("This is likely why orders are failing.")
            logger.warning("Orders can only be placed during market hours (9:15 AM - 3:30 PM IST, Monday-Friday)")
            return
        
        # Test a simple order placement (this will fail but we can see the error)
        logger.info("🧪 Testing order placement...")
        logger.info("Note: This will fail but we can see the specific error")
        
        test_order = order_service.place_order(
            symbol="RELIANCE",
            token="2885",  # RELIANCE token
            exchange="NSE",
            transaction_type=TransactionType.BUY,
            entry_price=2500.0,
            stop_loss=2450.0,
            target=2600.0,
            quantity=1
        )
        
        if test_order:
            logger.info(f"✅ Test order placed successfully: {test_order.order_id}")
        else:
            logger.warning("❌ Test order failed (this is expected)")
            
    except Exception as e:
        logger.error(f"❌ Error testing order service: {e}")
        import traceback
        traceback.print_exc()


def main():
    test_order_service()


if __name__ == "__main__":
    main()
