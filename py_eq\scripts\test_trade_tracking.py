#!/usr/bin/env python3
"""
Test Trade Tracking Script

This script tests the trade tracking logic to verify our fix works.
"""

import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from py_eq
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.daily_trade_tracker import DailyTradeTracker, TradeStatus
from models.order import Order, OrderStatus, TransactionType, OrderType, ProductType


def setup_logging():
    """Setup basic logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)


def test_trade_tracking_flow():
    """Test the complete trade tracking flow"""
    logger = setup_logging()
    
    logger.info("🧪 Testing Trade Tracking Flow...")
    logger.info("=" * 60)
    
    # Initialize trade tracker
    trade_tracker = DailyTradeTracker(
        logger=logger,
        data_dir="data",
        max_trades_per_day=3
    )
    
    # Test 1: Record a trade attempt
    logger.info("📝 Test 1: Recording trade attempt...")
    trade_id = trade_tracker.record_trade_attempt("TESTSTOCK", "TEST_STRATEGY")
    logger.info(f"Trade ID: {trade_id}")
    
    # Check status
    summary = trade_tracker.get_daily_summary()
    logger.info(f"After recording attempt - Pending: {summary['pending_trades']}, Executed: {summary['executed_trades']}")
    
    # Test 2: Simulate successful order placement
    logger.info("\n✅ Test 2: Simulating successful order placement...")
    
    # Create a mock order response (like what order service would return)
    mock_order = Order(
        symbol="TESTSTOCK",
        symbol_token="12345",
        exchange="NSE",
        transaction_type=TransactionType.BUY,
        order_type=OrderType.MARKET,
        product_type=ProductType.INTRADAY,
        quantity=10,
        price=100.0,
        stop_loss=95.0,
        target=110.0
    )
    mock_order.order_id = "TEST_ORDER_123"
    mock_order.status = OrderStatus.OPEN  # Most orders start as OPEN, not COMPLETED
    
    # Update trade status based on successful order placement
    success = trade_tracker.update_trade_status(
        trade_id=trade_id,
        status=TradeStatus.EXECUTED,  # Mark as executed since order was placed
        order_id=mock_order.order_id,
        quantity=mock_order.quantity,
        price=mock_order.price,
        notes=f"Order placed successfully (Status: {mock_order.status})"
    )
    
    logger.info(f"Update result: {success}")
    
    # Check final status
    final_summary = trade_tracker.get_daily_summary()
    logger.info(f"After order placement - Pending: {final_summary['pending_trades']}, Executed: {final_summary['executed_trades']}")
    
    # Test 3: Check if we can place more trades
    logger.info(f"\n🔍 Test 3: Can place more trades? {final_summary['can_trade_more']}")
    
    # Test 4: Simulate a failed order
    logger.info("\n❌ Test 4: Simulating failed order...")
    failed_trade_id = trade_tracker.record_trade_attempt("FAILEDSTOCK", "TEST_STRATEGY")
    
    # Mark as rejected (order placement failed)
    trade_tracker.update_trade_status(
        trade_id=failed_trade_id,
        status=TradeStatus.REJECTED,
        notes="Order placement failed"
    )
    
    # Final status
    final_summary = trade_tracker.get_daily_summary()
    logger.info("=" * 60)
    logger.info("📊 FINAL SUMMARY:")
    logger.info(f"Total trades: {final_summary['total_trades']}")
    logger.info(f"Executed trades: {final_summary['executed_trades']}")
    logger.info(f"Pending trades: {final_summary['pending_trades']}")
    logger.info(f"Rejected trades: {final_summary['rejected_trades']}")
    logger.info(f"Can trade more: {final_summary['can_trade_more']}")
    
    if final_summary['pending_trades'] == 0:
        logger.info("✅ SUCCESS: No pending trades remaining!")
        logger.info("✅ The fix is working correctly!")
    else:
        logger.warning(f"⚠️ WARNING: {final_summary['pending_trades']} pending trades still exist")
    
    return final_summary['pending_trades'] == 0


def main():
    success = test_trade_tracking_flow()
    if success:
        print("\n🎉 All tests passed! The trade tracking fix is working correctly.")
    else:
        print("\n❌ Tests failed! There are still issues with trade tracking.")


if __name__ == "__main__":
    main()
