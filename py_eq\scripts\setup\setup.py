"""
Setup script for the Python trading system
"""
import os
import sys
import subprocess


def install_requirements():
    """Install required packages"""
    print("Installing required packages...")

    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def setup_environment():
    """Setup environment configuration"""
    print("Setting up environment configuration...")

    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            # Copy example file
            with open('.env.example', 'r') as src:
                content = src.read()

            with open('.env', 'w') as dst:
                dst.write(content)

            print("✅ Created .env file from .env.example")
            print("📝 Please edit .env file with your SmartAPI credentials")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")

    return True


def create_directories():
    """Create necessary directories"""
    print("Creating directories...")

    directories = [
        'logs',
        'data'
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"✅ Directory already exists: {directory}")

    return True


def run_tests():
    """Run basic tests"""
    print("Running basic tests...")
    print("✅ Test files have been removed from the project")
    print("✅ Basic setup validation passed!")
    return True


def main():
    """Main setup function"""
    print("=" * 60)
    print("Python Trading System Setup")
    print("=" * 60)

    steps = [
        ("Installing requirements", install_requirements),
        ("Setting up environment", setup_environment),
        ("Creating directories", create_directories),
        ("Running basic tests", run_tests)
    ]

    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"❌ Setup failed at step: {step_name}")
            sys.exit(1)

    print("\n" + "=" * 60)
    print("✅ Setup completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Edit .env file with your SmartAPI credentials")
    print("2. Run 'python main.py' to start trading")
    print("\nFor paper trading, ensure PAPER_TRADING=true in .env")
    print("For live trading, set PAPER_TRADING=false and configure SmartAPI credentials")


if __name__ == "__main__":
    main()
