"""
Moving Average Crossover Strategy with RSI Filter
Based on the Golang implementation
"""
import logging
from typing import List, Optional, Tuple, Dict
import math

from models.candle import Candle
from models.signal import Signal
from models.order import Order, TransactionType
from services.order_service import OrderServiceInterface
from services.market_data_service import MarketDataServiceInterface
from utils.helpers import (
    is_market_open, calculate_ema, calculate_rsi,
    calculate_quantity, calculate_average_volume
)
import numpy as np
from config.config import TradingConfig


class MACrossoverStrategy:
    """Moving Average Crossover Strategy with RSI Filter"""

    def __init__(
        self,
        logger: logging.Logger,
        market_data_service: MarketDataServiceInterface,
        order_service: OrderServiceInterface,
        config: TradingConfig
    ):
        self.logger = logger
        self.market_data_service = market_data_service
        self.order_service = order_service
        self.config = config

        # Strategy-specific data
        self.candles_cache = {}
        self.last_update = {}

    def calculate_atr(self, candles: List[Candle], period: int = 14) -> float:
        """Calculate Average True Range for dynamic stop loss"""
        if len(candles) < period + 1:
            return 0.0

        true_ranges = []
        for i in range(1, len(candles)):
            current = candles[i]
            previous = candles[i-1]

            high_low = current.high - current.low
            high_close = abs(current.high - previous.close)
            low_close = abs(current.low - previous.close)

            true_range = max(high_low, high_close, low_close)
            true_ranges.append(true_range)

        # Calculate ATR as simple moving average of true ranges
        if len(true_ranges) >= period:
            return sum(true_ranges[-period:]) / period
        else:
            return sum(true_ranges) / len(true_ranges) if true_ranges else 0.0

    def calculate_intraday_stop_loss(self, entry_price: float, candles: List[Candle], is_long: bool) -> float:
        """Calculate intraday-appropriate stop loss using ATR and percentage methods"""

        # Method 1: ATR-based stop loss (dynamic)
        if self.config.use_atr_based_stops and len(candles) > 14:
            atr = self.calculate_atr(candles)
            if atr > 0:
                atr_multiplier = self.config.atr_stop_loss_multiplier
                if is_long:
                    atr_stop = entry_price - (atr * atr_multiplier)
                else:
                    atr_stop = entry_price + (atr * atr_multiplier)
            else:
                atr_stop = None
        else:
            atr_stop = None

        # Method 2: Percentage-based stop loss (fixed)
        if is_long:
            percentage_stop = entry_price * (1 - self.config.intraday_stop_loss_percent)
            max_stop = entry_price * (1 - self.config.max_intraday_stop_loss_percent)
        else:
            percentage_stop = entry_price * (1 + self.config.intraday_stop_loss_percent)
            max_stop = entry_price * (1 + self.config.max_intraday_stop_loss_percent)

        # Choose the tighter of ATR or percentage stop, but not tighter than max stop
        if atr_stop is not None:
            if is_long:
                # For long positions, use the higher (less aggressive) of the two stops
                # but not higher than max stop loss
                stop_loss = max(atr_stop, max_stop)
                stop_loss = min(stop_loss, percentage_stop)
            else:
                # For short positions, use the lower (less aggressive) of the two stops
                # but not lower than max stop loss
                stop_loss = min(atr_stop, max_stop)
                stop_loss = max(stop_loss, percentage_stop)
        else:
            # Fallback to percentage-based stop
            stop_loss = percentage_stop

        return stop_loss

    def analyze_signal(self, signal: Signal) -> Optional[Dict]:
        """
        Analyze a signal and return entry conditions without placing order
        Used by Enhanced Strategy Manager for virtual account trading
        """
        try:
            # Get historical data for analysis
            candles = self.market_data_service.get_historical_data(signal.symbol, signal.timeframe, days=3)
            if not candles or len(candles) < 50:
                return None

            # Calculate indicators
            indicators = self._calculate_indicators(candles)
            if not indicators:
                return None

            # Check entry conditions using existing method
            entry_result = self._check_entry_conditions(indicators, signal.symbol)
            if not entry_result[4]:  # should_enter is False
                return None

            # Get entry details from the result tuple
            entry_price, stop_loss, target, direction, should_enter, reason = entry_result

            # Convert direction to string format
            if direction == 1:
                direction_str = 'LONG'
            elif direction == -1:
                direction_str = 'SHORT'
            else:
                direction_str = 'LONG'  # Default to LONG

            return {
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'target': target,
                'direction': direction_str,
                'confidence': 0.7,  # Default confidence
                'indicators': indicators,
                'reason': reason
            }

        except Exception as e:
            self.logger.error(f"Error analyzing signal for {signal.symbol}: {e}")
            return None

    def process_signal(self, signal: Signal) -> Optional[Order]:
        """Process a trading signal using MA Crossover strategy"""

        # Check if market is open (using user's preferred function)
        if not is_market_open():
            self.logger.info(f"Market is closed. Skipping signal for {signal.symbol}")
            return None

        # Get symbol token and exchange
        token_info = self.market_data_service.get_symbol_token(signal.symbol)
        if not token_info:
            self.logger.error(f"No token found for symbol: {signal.symbol}")
            return None

        token, exchange = token_info

        # Get last price to verify symbol exists
        last_price = self.market_data_service.get_last_price(signal.symbol)
        if last_price is None:
            self.logger.error(f"Could not get last price for {signal.symbol}")
            return None

        # Get historical data (only need 3 days for intraday trading)
        candles = self.market_data_service.get_historical_data(
            signal.symbol, signal.timeframe, days=3
        )

        if len(candles) < 50:
            self.logger.warning(f"Not enough historical data for {signal.symbol}: {len(candles)} candles")
            return None

        # Calculate technical indicators
        candles_with_indicators = self._calculate_indicators(candles)

        # Check entry conditions
        entry_result = self._check_entry_conditions(candles_with_indicators, signal.symbol)

        if not entry_result[4]:  # should_enter is False
            self.logger.info(f"No entry conditions met for {signal.symbol}: {entry_result[5]}")
            return None

        entry_price, stop_loss, target, direction, should_enter, reason = entry_result

        # Calculate quantity using the formula: 800/(entry_price - stop_loss) * 4x margin
        quantity = calculate_quantity(
            entry_price,
            stop_loss,
            self.config.max_risk_per_trade,
            self.config.margin_multiplier
        )

        # Determine transaction type
        transaction_type = TransactionType.BUY if direction > 0 else TransactionType.SELL

        self.logger.info(
            f"Placing {transaction_type.value} order for {signal.symbol}: "
            f"Entry: {entry_price:.2f}, Stop-Loss: {stop_loss:.2f}, "
            f"Target: {target:.2f}, Quantity: {quantity}"
        )

        # Place order
        order = self.order_service.place_order(
            symbol=signal.symbol,
            token=token,
            exchange=exchange,
            transaction_type=transaction_type,
            entry_price=entry_price,
            stop_loss=stop_loss,
            target=target,
            quantity=quantity
        )

        if order:
            self.logger.info(f"Order placed successfully for {signal.symbol}: {order.order_id}")
        else:
            self.logger.error(f"Failed to place order for {signal.symbol}")

        return order

    def _calculate_indicators(self, candles: List[Candle]) -> List[Candle]:
        """Calculate technical indicators for candles"""
        if len(candles) < 20:
            return candles

        # Extract close prices and volumes
        close_prices = [candle.close for candle in candles]
        volumes = [candle.volume for candle in candles]

        # Calculate EMAs
        ema9_values = calculate_ema(close_prices, self.config.ema_fast_period)
        ema20_values = calculate_ema(close_prices, self.config.ema_slow_period)
        ema50_values = calculate_ema(close_prices, self.config.ema_trend_period)

        # Calculate RSI
        rsi_values = calculate_rsi(close_prices, self.config.rsi_period)

        # Calculate average volume
        avg_volume_values = calculate_average_volume(volumes, self.config.volume_period)

        # Update candles with indicators
        result_candles = candles.copy()

        for i, candle in enumerate(result_candles):
            # EMA9 (starts from index 8)
            if i >= self.config.ema_fast_period - 1 and i - self.config.ema_fast_period + 1 < len(ema9_values):
                candle.ema9 = ema9_values[i - self.config.ema_fast_period + 1]

            # EMA20 (starts from index 19)
            if i >= self.config.ema_slow_period - 1 and i - self.config.ema_slow_period + 1 < len(ema20_values):
                candle.ema20 = ema20_values[i - self.config.ema_slow_period + 1]

            # EMA50 (starts from index 49)
            if i >= self.config.ema_trend_period - 1 and i - self.config.ema_trend_period + 1 < len(ema50_values):
                candle.ema50 = ema50_values[i - self.config.ema_trend_period + 1]

            # RSI (starts from index 14)
            if i >= self.config.rsi_period and i - self.config.rsi_period < len(rsi_values):
                candle.rsi = rsi_values[i - self.config.rsi_period]

            # Average Volume (starts from index 9)
            if i >= self.config.volume_period - 1 and i - self.config.volume_period + 1 < len(avg_volume_values):
                candle.avg_volume = avg_volume_values[i - self.config.volume_period + 1]

        return result_candles

    def _check_entry_conditions(self, candles: List[Candle], symbol: str) -> Tuple[float, float, float, int, bool, str]:
        """
        Check if entry conditions are met for MA Crossover strategy

        Returns:
            Tuple of (entry_price, stop_loss, target, direction, should_enter, reason)
        """
        if len(candles) < 50:
            return 0, 0, 0, 0, False, f"not enough candles: have {len(candles)}, need at least 50"

        # Get the last two candles
        current = candles[-1]
        previous = candles[-2]

        # Check if we have all required indicators
        if (current.ema9 is None or current.ema20 is None or current.ema50 is None or
            current.rsi is None or current.avg_volume is None or
            previous.ema9 is None or previous.ema20 is None):
            return 0, 0, 0, 0, False, "missing technical indicators"

        # Check for 9 EMA crossing above 20 EMA (bullish)
        bullish_crossover = previous.ema9 <= previous.ema20 and current.ema9 > current.ema20

        # Check for 9 EMA crossing below 20 EMA (bearish)
        bearish_crossover = previous.ema9 >= previous.ema20 and current.ema9 < current.ema20

        # Check if price is above/below 50 EMA (trend filter)
        above_ema50 = current.close > current.ema50
        below_ema50 = current.close < current.ema50

        # Check RSI conditions
        rsi_bullish = current.rsi > self.config.rsi_neutral
        rsi_bearish = current.rsi < self.config.rsi_neutral

        # Volume confirmation (made more lenient - 80% of average volume)
        volume_confirmation = current.volume > (current.avg_volume * 0.8)

        # Log indicator values for debugging
        self.logger.info(
            f"Indicators for {symbol}: EMA9={current.ema9:.2f}, EMA20={current.ema20:.2f}, "
            f"EMA50={current.ema50:.2f}, RSI={current.rsi:.2f}, "
            f"Volume={current.volume:.0f}, AvgVolume={current.avg_volume:.0f}"
        )

        self.logger.info(
            f"Conditions for {symbol}: BullishCrossover={bullish_crossover}, "
            f"BearishCrossover={bearish_crossover}, AboveEMA50={above_ema50}, "
            f"BelowEMA50={below_ema50}, RSIBullish={rsi_bullish}, "
            f"RSIBearish={rsi_bearish}, VolumeConfirmation={volume_confirmation}"
        )

        # Bullish entry conditions
        if bullish_crossover and above_ema50 and rsi_bullish and volume_confirmation:
            entry_price = current.close

            # Stop-loss: Use intraday-appropriate ATR/percentage-based stop loss
            stop_loss = self.calculate_intraday_stop_loss(entry_price, candles, is_long=True)

            # Log stop loss calculation details
            atr = self.calculate_atr(candles) if len(candles) > 14 else 0
            stop_loss_percent = ((entry_price - stop_loss) / entry_price) * 100
            self.logger.info(f"📊 INTRADAY SL: {signal.symbol} Long - Entry: ₹{entry_price:.2f}, "
                           f"SL: ₹{stop_loss:.2f} ({stop_loss_percent:.2f}%), ATR: {atr:.2f}")

            # Calculate risk
            risk = entry_price - stop_loss

            # Calculate target based on risk-reward ratio
            target = entry_price + (risk * self.config.risk_reward_ratio)

            return entry_price, stop_loss, target, 1, True, "bullish entry conditions met"

        # Bearish entry conditions
        if bearish_crossover and below_ema50 and rsi_bearish and volume_confirmation:
            entry_price = current.close

            # Stop-loss: Use intraday-appropriate ATR/percentage-based stop loss
            stop_loss = self.calculate_intraday_stop_loss(entry_price, candles, is_long=False)

            # Log stop loss calculation details
            atr = self.calculate_atr(candles) if len(candles) > 14 else 0
            stop_loss_percent = ((stop_loss - entry_price) / entry_price) * 100
            self.logger.info(f"📊 INTRADAY SL: {signal.symbol} Short - Entry: ₹{entry_price:.2f}, "
                           f"SL: ₹{stop_loss:.2f} ({stop_loss_percent:.2f}%), ATR: {atr:.2f}")

            # Calculate risk
            risk = stop_loss - entry_price

            # Calculate target based on risk-reward ratio
            target = entry_price - (risk * self.config.risk_reward_ratio)

            return entry_price, stop_loss, target, -1, True, "bearish entry conditions met"

        # Determine the reason why entry conditions weren't met
        if not bullish_crossover and not bearish_crossover:
            reason = "no EMA crossover detected"
        elif bullish_crossover and not above_ema50:
            reason = "bullish crossover detected but price not above EMA50"
        elif bullish_crossover and not rsi_bullish:
            reason = "bullish crossover detected but RSI not bullish"
        elif bullish_crossover and not volume_confirmation:
            reason = "bullish crossover detected but volume not confirming"
        elif bearish_crossover and not below_ema50:
            reason = "bearish crossover detected but price not below EMA50"
        elif bearish_crossover and not rsi_bearish:
            reason = "bearish crossover detected but RSI not bearish"
        elif bearish_crossover and not volume_confirmation:
            reason = "bearish crossover detected but volume not confirming"
        else:
            reason = "unknown reason"

        return 0, 0, 0, 0, False, reason
