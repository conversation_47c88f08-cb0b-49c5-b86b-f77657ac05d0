#!/usr/bin/env python3
"""
EMERGENCY POSITION MONITOR
Critical script to monitor existing positions when main system has issues
"""

import sys
import os
import logging
import time
from datetime import datetime

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from services.order_service import SmartAPIOrderService
from config.config import config

def setup_emergency_logging():
    """Setup emergency logging"""
    logger = logging.getLogger('EmergencyMonitor')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s - EMERGENCY - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)
    
    # File handler
    file_handler = logging.FileHandler('emergency_monitor.log')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - EMERGENCY - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
    
    return logger

def get_emergency_positions(order_service, logger):
    """Get positions using emergency methods"""
    positions = []
    
    try:
        # Method 1: Try to get positions directly
        logger.info("🚨 EMERGENCY: Attempting to fetch positions from broker...")
        raw_positions = order_service.get_real_positions()
        
        if raw_positions:
            logger.info(f"✅ Found {len(raw_positions)} raw positions")
            for pos in raw_positions:
                net_qty = int(pos.get('netqty', 0))
                if net_qty != 0:
                    symbol = pos.get('tradingsymbol', '').replace('-EQ', '')
                    avg_price = pos.get('averageprice', 0)
                    
                    position_info = {
                        'symbol': symbol,
                        'quantity': abs(net_qty),
                        'side': 'BUY' if net_qty > 0 else 'SELL',
                        'avg_price': avg_price,
                        'order_id': pos.get('orderid', ''),
                        'raw_data': pos
                    }
                    positions.append(position_info)
                    
                    logger.info(f"📊 Position: {symbol} {position_info['side']} {position_info['quantity']} @ ₹{avg_price}")
        else:
            logger.warning("⚠️ No positions found in direct fetch")
            
    except Exception as e:
        logger.error(f"❌ Error fetching positions: {e}")
    
    return positions

def calculate_emergency_risk(position, current_price, morning_balance=4730.0):
    """Calculate risk for emergency monitoring"""
    max_risk = morning_balance * 0.01  # 1% = ₹47.3
    
    if position['avg_price'] and float(position['avg_price']) > 0:
        # We have entry price - calculate actual P&L
        entry_price = float(position['avg_price'])
        quantity = position['quantity']
        
        if position['side'] == 'BUY':
            pnl = (current_price - entry_price) * quantity
        else:
            pnl = (entry_price - current_price) * quantity
            
        risk_exceeded = pnl < -max_risk
        
        return {
            'entry_price': entry_price,
            'current_price': current_price,
            'pnl': pnl,
            'max_risk': max_risk,
            'risk_exceeded': risk_exceeded,
            'action_needed': risk_exceeded
        }
    else:
        # No entry price - use conservative emergency rules
        position_value = current_price * position['quantity']
        max_position_value = morning_balance * 0.5  # 50% of balance
        
        return {
            'entry_price': 'UNKNOWN',
            'current_price': current_price,
            'position_value': position_value,
            'max_position_value': max_position_value,
            'risk_exceeded': position_value > max_position_value,
            'action_needed': True,  # Always needs manual check
            'manual_intervention_required': True
        }

def emergency_monitor_loop(order_service, logger):
    """Main emergency monitoring loop"""
    logger.info("🚨 STARTING EMERGENCY POSITION MONITOR")
    logger.info("=" * 60)
    
    while True:
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            logger.info(f"🔍 [{current_time}] Emergency position check...")
            
            # Get positions
            positions = get_emergency_positions(order_service, logger)
            
            if not positions:
                logger.warning("⚠️ No positions found - system may have issues")
                time.sleep(30)
                continue
            
            logger.info(f"📊 Monitoring {len(positions)} positions")
            
            # Check each position
            for pos in positions:
                try:
                    symbol = pos['symbol']
                    
                    # For emergency monitoring, use a simple current price (you'd get this from market data)
                    # This is a placeholder - in real implementation, fetch current market price
                    current_price = 100.0  # PLACEHOLDER - REPLACE WITH ACTUAL MARKET PRICE
                    
                    risk_analysis = calculate_emergency_risk(pos, current_price)
                    
                    logger.info(f"📈 {symbol}: {pos['side']} {pos['quantity']} shares")
                    logger.info(f"   Entry: ₹{risk_analysis.get('entry_price', 'UNKNOWN')}")
                    logger.info(f"   Current: ₹{risk_analysis['current_price']}")
                    
                    if 'pnl' in risk_analysis:
                        logger.info(f"   P&L: ₹{risk_analysis['pnl']:.2f}")
                        
                    if risk_analysis['action_needed']:
                        logger.error(f"🚨 ACTION NEEDED for {symbol}!")
                        if risk_analysis.get('risk_exceeded'):
                            logger.error(f"🚨 RISK LIMIT EXCEEDED - CONSIDER CLOSING POSITION")
                        if risk_analysis.get('manual_intervention_required'):
                            logger.error(f"🚨 MANUAL INTERVENTION REQUIRED - CHECK ENTRY PRICE")
                            
                except Exception as e:
                    logger.error(f"Error analyzing position {pos.get('symbol', 'UNKNOWN')}: {e}")
            
            logger.info("=" * 60)
            
            # Wait before next check
            time.sleep(60)  # Check every minute
            
        except KeyboardInterrupt:
            logger.info("🛑 Emergency monitor stopped by user")
            break
        except Exception as e:
            logger.error(f"❌ Error in emergency monitor loop: {e}")
            time.sleep(30)

def main():
    """Main emergency monitor function"""
    print("🚨 EMERGENCY POSITION MONITOR STARTING...")
    print("This script will monitor your positions when the main system has issues")
    print("Press Ctrl+C to stop")
    print()
    
    # Setup logging
    logger = setup_emergency_logging()
    
    # Create order service
    try:
        order_service = SmartAPIOrderService(config.smartapi, logger)
        logger.info("✅ Order service initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize order service: {e}")
        return
    
    # Start monitoring
    emergency_monitor_loop(order_service, logger)

if __name__ == "__main__":
    main()
