"""
WebSocket Price Service for Real-Time Market Data

This service connects to SmartAPI WebSocket for real-time price updates,
reducing the need for frequent API calls and providing more timely data.
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime
import json

from smartapi import SmartWebSocket

class WebSocketPriceService:
    """
    WebSocket service for real-time price updates from Angel One
    
    Features:
    - Real-time tick data for subscribed symbols
    - Price caching for quick access
    - Automatic reconnection on disconnection
    - Thread-safe operations
    """
    
    def __init__(self, api_key: str, client_id: str, feed_token: str, logger=None):
        """
        Initialize WebSocket Price Service
        
        Args:
            api_key: SmartAPI key
            client_id: Client ID
            feed_token: Feed token for WebSocket
            logger: Logger instance
        """
        self.api_key = api_key
        self.client_id = client_id
        self.feed_token = feed_token
        self.logger = logger or logging.getLogger(__name__)
        
        # Price cache
        self.price_cache: Dict[str, Dict] = {}
        self.price_cache_lock = threading.RLock()
        
        # Subscription management
        self.subscribed_symbols: Dict[str, Dict] = {}
        self.subscription_lock = threading.RLock()
        
        # WebSocket connection
        self.websocket = None
        self.is_connected = False
        self.reconnect_thread = None
        self.stop_reconnect = False
        
        # Callbacks
        self.on_tick_callbacks: List[Callable] = []
        
        # Initialize WebSocket
        self._initialize_websocket()
        
    def _initialize_websocket(self):
        """Initialize WebSocket connection"""
        try:
            self.websocket = SmartWebSocket(self.api_key)
            
            # Set callbacks
            self.websocket.on_open = self._on_open
            self.websocket.on_message = self._on_message
            self.websocket.on_error = self._on_error
            self.websocket.on_close = self._on_close
            
            self.logger.info("WebSocket initialized")
        except Exception as e:
            self.logger.error(f"Error initializing WebSocket: {e}")
            self.websocket = None
            
    def connect(self):
        """Connect to WebSocket"""
        if not self.websocket:
            self._initialize_websocket()
            
        if self.websocket:
            try:
                self.websocket.connect(
                    self.feed_token,
                    self.client_id
                )
                self.logger.info("WebSocket connection initiated")
            except Exception as e:
                self.logger.error(f"Error connecting to WebSocket: {e}")
                self._start_reconnect_thread()
                
    def disconnect(self):
        """Disconnect from WebSocket"""
        if self.websocket:
            try:
                self.stop_reconnect = True
                if self.reconnect_thread and self.reconnect_thread.is_alive():
                    self.reconnect_thread.join(timeout=1.0)
                    
                self.websocket.close()
                self.logger.info("WebSocket disconnected")
            except Exception as e:
                self.logger.error(f"Error disconnecting WebSocket: {e}")
                
            self.is_connected = False
            
    def subscribe_symbols(self, symbols: List[str], exchange: str = "NSE"):
        """
        Subscribe to symbols for real-time updates
        
        Args:
            symbols: List of symbol tokens to subscribe
            exchange: Exchange (default: NSE)
        """
        if not self.websocket or not self.is_connected:
            self.logger.warning("WebSocket not connected, cannot subscribe")
            return False
            
        try:
            # Format symbols for subscription
            formatted_symbols = []
            for symbol in symbols:
                with self.subscription_lock:
                    if symbol not in self.subscribed_symbols:
                        self.subscribed_symbols[symbol] = {
                            'exchange': exchange,
                            'last_update': datetime.now()
                        }
                        formatted_symbols.append(f"{exchange}|{symbol}")
            
            if formatted_symbols:
                # Subscribe to mode 1 (LTP)
                self.websocket.subscribe(formatted_symbols, 1)
                self.logger.info(f"Subscribed to {len(formatted_symbols)} symbols: {formatted_symbols[:5]}...")
                return True
            else:
                self.logger.info("No new symbols to subscribe")
                return True
                
        except Exception as e:
            self.logger.error(f"Error subscribing to symbols: {e}")
            return False
            
    def unsubscribe_symbols(self, symbols: List[str], exchange: str = "NSE"):
        """
        Unsubscribe from symbols
        
        Args:
            symbols: List of symbol tokens to unsubscribe
            exchange: Exchange (default: NSE)
        """
        if not self.websocket or not self.is_connected:
            self.logger.warning("WebSocket not connected, cannot unsubscribe")
            return False
            
        try:
            # Format symbols for unsubscription
            formatted_symbols = []
            for symbol in symbols:
                with self.subscription_lock:
                    if symbol in self.subscribed_symbols:
                        del self.subscribed_symbols[symbol]
                        formatted_symbols.append(f"{exchange}|{symbol}")
            
            if formatted_symbols:
                # Unsubscribe
                self.websocket.unsubscribe(formatted_symbols)
                self.logger.info(f"Unsubscribed from {len(formatted_symbols)} symbols")
                return True
            else:
                self.logger.info("No symbols to unsubscribe")
                return True
                
        except Exception as e:
            self.logger.error(f"Error unsubscribing from symbols: {e}")
            return False
            
    def get_last_price(self, symbol: str) -> Optional[float]:
        """
        Get last price for a symbol from cache
        
        Args:
            symbol: Symbol token
            
        Returns:
            Last price if available, None otherwise
        """
        with self.price_cache_lock:
            if symbol in self.price_cache:
                return self.price_cache[symbol].get('ltp')
        return None
        
    def get_price_data(self, symbol: str) -> Optional[Dict]:
        """
        Get all price data for a symbol from cache
        
        Args:
            symbol: Symbol token
            
        Returns:
            Price data dictionary if available, None otherwise
        """
        with self.price_cache_lock:
            return self.price_cache.get(symbol)
            
    def register_tick_callback(self, callback: Callable):
        """
        Register a callback function for tick data
        
        Args:
            callback: Function to call on each tick
        """
        if callback not in self.on_tick_callbacks:
            self.on_tick_callbacks.append(callback)
            
    def unregister_tick_callback(self, callback: Callable):
        """
        Unregister a tick callback function
        
        Args:
            callback: Function to remove
        """
        if callback in self.on_tick_callbacks:
            self.on_tick_callbacks.remove(callback)
            
    def _on_open(self, ws, response):
        """WebSocket open callback"""
        self.is_connected = True
        self.logger.info(f"WebSocket connected: {response}")
        
        # Resubscribe to all symbols
        with self.subscription_lock:
            symbols = list(self.subscribed_symbols.keys())
            
        if symbols:
            self.logger.info(f"Resubscribing to {len(symbols)} symbols")
            self.subscribe_symbols(symbols)
            
    def _on_message(self, ws, message):
        """WebSocket message callback"""
        try:
            # Parse message
            data = json.loads(message)
            
            # Process tick data
            if isinstance(data, dict) and 'tk' in data:
                symbol = data.get('tk')
                ltp = data.get('lp')
                
                if symbol and ltp:
                    # Update price cache
                    with self.price_cache_lock:
                        self.price_cache[symbol] = {
                            'ltp': float(ltp),
                            'volume': data.get('v', 0),
                            'timestamp': datetime.now(),
                            'change': data.get('c', 0),
                            'change_percent': data.get('cp', 0),
                            'high': data.get('h', 0),
                            'low': data.get('l', 0),
                            'open': data.get('o', 0),
                            'close': data.get('c', 0)
                        }
                    
                    # Call registered callbacks
                    for callback in self.on_tick_callbacks:
                        try:
                            callback(symbol, self.price_cache[symbol])
                        except Exception as e:
                            self.logger.error(f"Error in tick callback: {e}")
                            
        except Exception as e:
            self.logger.error(f"Error processing WebSocket message: {e}")
            
    def _on_error(self, ws, error):
        """WebSocket error callback"""
        self.logger.error(f"WebSocket error: {error}")
        self.is_connected = False
        self._start_reconnect_thread()
        
    def _on_close(self, ws, code, reason):
        """WebSocket close callback"""
        self.logger.warning(f"WebSocket closed: {code} - {reason}")
        self.is_connected = False
        self._start_reconnect_thread()
        
    def _start_reconnect_thread(self):
        """Start reconnection thread"""
        if not self.stop_reconnect and (not self.reconnect_thread or not self.reconnect_thread.is_alive()):
            self.reconnect_thread = threading.Thread(target=self._reconnect_worker)
            self.reconnect_thread.daemon = True
            self.reconnect_thread.start()
            
    def _reconnect_worker(self):
        """Reconnection worker thread"""
        retry_count = 0
        max_retries = 10
        base_delay = 1.0
        
        while not self.stop_reconnect and retry_count < max_retries:
            retry_count += 1
            delay = base_delay * (2 ** min(retry_count - 1, 5))  # Exponential backoff, max 32 seconds
            
            self.logger.info(f"Attempting to reconnect WebSocket in {delay:.1f}s (attempt {retry_count}/{max_retries})")
            time.sleep(delay)
            
            try:
                # Reinitialize WebSocket
                self._initialize_websocket()
                self.connect()
                
                # Wait for connection to establish
                for _ in range(5):
                    if self.is_connected:
                        self.logger.info("WebSocket reconnected successfully")
                        return
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Error reconnecting WebSocket: {e}")
                
        if not self.is_connected:
            self.logger.error(f"Failed to reconnect WebSocket after {max_retries} attempts")