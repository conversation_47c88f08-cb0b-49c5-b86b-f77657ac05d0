"""
Telegram Bot Service for Trading Reports

This service provides a comprehensive Telegram bot for trading reporting
with real-time updates, interactive commands, and beautiful charts.
"""

import os
import logging
import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import telebot
from telebot import types
import io

# Handle imports for both standalone and package execution
try:
    from ..config.telegram_config import telegram_config, BOT_COMMANDS, MESSAGE_TEMPLATES, EMOJIS
    from .report_generator import TradingReportGenerator, TradeRecord, TradingMetrics
    from .chart_generator import TelegramChartGenerator
    from ..utils.safe_logging import SafeLogger
except ImportError:
    # Fallback for standalone execution
    import sys
    from pathlib import Path

    # Add parent directory to path
    parent_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(parent_dir))

    from config.telegram_config import telegram_config, BOT_COMMANDS, MESSAGE_TEMPLATES, EMOJIS
    from services.report_generator import TradingReportGenerator, TradeRecord, TradingMetrics
    from services.chart_generator import TelegramChartGenerator
    from utils.safe_logging import get_safe_logger


class TradingTelegramBot:
    """
    Comprehensive Telegram bot for trading reports and monitoring
    """
    
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or get_safe_logger(__name__)
        self.config = telegram_config

        # Reload configuration from environment to ensure latest values
        self.config.reload_from_env()

        # Validate configuration
        try:
            self.config.validate()
        except ValueError as e:
            self.logger.error(f"Invalid Telegram configuration: {e}")
            raise
        
        # Initialize bot
        self.bot = telebot.TeleBot(self.config.bot_token)
        
        # Initialize services
        self.report_generator = TradingReportGenerator(logger=self.logger)
        self.chart_generator = TelegramChartGenerator(logger=self.logger)
        
        # Bot state
        self.is_running = False
        self.scheduler_thread = None
        
        # Setup bot handlers
        self._setup_handlers()
        
        # Setup scheduled tasks
        self._setup_scheduler()
        
        self.logger.info("🤖 Telegram bot initialized successfully")
    
    def start(self):
        """Start the Telegram bot"""
        if self.is_running:
            self.logger.warning("Bot is already running")
            return
        
        self.is_running = True
        
        # Start scheduler in separate thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        # Send startup message
        self._send_startup_message()
        
        self.logger.info("🚀 Telegram bot started")
        
        try:
            # Start polling
            self.bot.infinity_polling(timeout=10, long_polling_timeout=5)
        except Exception as e:
            self.logger.error(f"Bot polling error: {e}")
            self.stop()
    
    def stop(self):
        """Stop the Telegram bot"""
        self.is_running = False
        if self.bot:
            self.bot.stop_polling()
        self.logger.info("🛑 Telegram bot stopped")
    
    def _setup_handlers(self):
        """Setup bot command and message handlers"""
        
        @self.bot.message_handler(commands=['start'])
        def handle_start(message):
            self._handle_start(message)
        
        @self.bot.message_handler(commands=['help'])
        def handle_help(message):
            self._handle_help(message)
        
        @self.bot.message_handler(commands=['status'])
        def handle_status(message):
            self._handle_status(message)
        
        @self.bot.message_handler(commands=['daily'])
        def handle_daily(message):
            self._handle_daily_report(message)
        
        @self.bot.message_handler(commands=['weekly'])
        def handle_weekly(message):
            self._handle_weekly_report(message)
        
        @self.bot.message_handler(commands=['monthly'])
        def handle_monthly(message):
            self._handle_monthly_report(message)
        
        @self.bot.message_handler(commands=['trades'])
        def handle_trades(message):
            self._handle_recent_trades(message)
        
        @self.bot.message_handler(commands=['balance'])
        def handle_balance(message):
            self._handle_balance(message)
        
        @self.bot.message_handler(commands=['strategies'])
        def handle_strategies(message):
            self._handle_strategies(message)
        
        @self.bot.message_handler(commands=['alerts'])
        def handle_alerts(message):
            self._handle_alerts(message)
        
        @self.bot.message_handler(commands=['settings'])
        def handle_settings(message):
            self._handle_settings(message)
        
        @self.bot.callback_query_handler(func=lambda call: True)
        def handle_callback(call):
            self._handle_callback_query(call)
        
        @self.bot.message_handler(func=lambda message: True)
        def handle_unknown(message):
            self._handle_unknown_command(message)
    
    def _handle_start(self, message):
        """Handle /start command"""
        # Debug: Log the actual user ID for troubleshooting
        self.logger.info(f"🔍 User {message.from_user.id} ({message.from_user.first_name}) trying to access bot")
        self.logger.info(f"🔍 Authorized users: {self.config.authorized_users}")
        self.logger.info(f"🔍 Admin users: {self.config.admin_users}")

        if not self._is_authorized(message.from_user.id):
            self.logger.warning(f"❌ Unauthorized access attempt by user {message.from_user.id}")
            self._send_unauthorized_message(message.chat.id)
            return
        
        # Create main menu keyboard
        keyboard = self._create_main_menu_keyboard()
        
        self.bot.send_message(
            message.chat.id,
            MESSAGE_TEMPLATES["welcome"],
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    
    def _handle_help(self, message):
        """Handle /help command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return
        
        help_text = "🤖 *Available Commands:*\n\n"
        for command, description in BOT_COMMANDS.items():
            help_text += f"/{command} - {description}\n"
        
        self.bot.send_message(message.chat.id, help_text, parse_mode='Markdown')
    
    def _handle_status(self, message):
        """Handle /status command - show current trading status"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return
        
        # Send processing message
        processing_msg = self.bot.send_message(
            message.chat.id, 
            MESSAGE_TEMPLATES["processing"],
            parse_mode='Markdown'
        )
        
        try:
            # Get today's data
            today = datetime.now()
            trades = self.report_generator._load_trades_for_date(today)
            metrics = self.report_generator._calculate_metrics(trades, today)
            
            # Create status message
            status_text = self._format_status_message(metrics, today)
            
            # Create status chart
            chart_buffer = self.chart_generator.create_daily_pnl_chart(trades, metrics)
            
            # Delete processing message
            self.bot.delete_message(message.chat.id, processing_msg.message_id)
            
            # Send chart and status
            self.bot.send_photo(
                message.chat.id,
                chart_buffer,
                caption=status_text,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            self.logger.error(f"Error in status command: {e}")
            self.bot.edit_message_text(
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                message.chat.id,
                processing_msg.message_id,
                parse_mode='Markdown'
            )
    
    def _handle_daily_report(self, message):
        """Handle /daily command - generate daily report"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return
        
        # Send processing message with animation
        processing_msg = self.bot.send_message(
            message.chat.id,
            f"{EMOJIS['chart']} Generating daily report...",
            parse_mode='Markdown'
        )
        
        try:
            # Generate daily report
            report_path = self.report_generator.generate_daily_report()
            
            # Get today's data for charts
            today = datetime.now()
            trades = self.report_generator._load_trades_for_date(today)
            metrics = self.report_generator._calculate_metrics(trades, today)
            
            # Create charts
            pnl_chart = self.chart_generator.create_daily_pnl_chart(trades, metrics)
            strategy_chart = self.chart_generator.create_strategy_performance_chart(metrics)
            
            # Create summary message
            summary_text = self._format_daily_summary(metrics, today)
            
            # Delete processing message
            self.bot.delete_message(message.chat.id, processing_msg.message_id)
            
            # Send summary
            self.bot.send_message(message.chat.id, summary_text, parse_mode='Markdown')
            
            # Send charts
            self.bot.send_photo(message.chat.id, pnl_chart, caption="📊 Daily P&L Breakdown")
            
            if metrics.strategy_performance:
                self.bot.send_photo(message.chat.id, strategy_chart, caption="🎯 Strategy Performance")
            
            # Send HTML report as document
            if os.path.exists(report_path):
                with open(report_path, 'rb') as report_file:
                    self.bot.send_document(
                        message.chat.id,
                        report_file,
                        caption="📄 Detailed HTML Report"
                    )
            
        except Exception as e:
            self.logger.error(f"Error in daily report command: {e}")
            self.bot.edit_message_text(
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                message.chat.id,
                processing_msg.message_id,
                parse_mode='Markdown'
            )
    
    def _create_main_menu_keyboard(self):
        """Create main menu inline keyboard"""
        keyboard = types.InlineKeyboardMarkup(row_width=2)
        
        buttons = [
            types.InlineKeyboardButton("📊 Status", callback_data="status"),
            types.InlineKeyboardButton("📈 Daily Report", callback_data="daily"),
            types.InlineKeyboardButton("📅 Weekly Report", callback_data="weekly"),
            types.InlineKeyboardButton("📆 Monthly Report", callback_data="monthly"),
            types.InlineKeyboardButton("📋 Recent Trades", callback_data="trades"),
            types.InlineKeyboardButton("💰 Balance", callback_data="balance"),
            types.InlineKeyboardButton("🎯 Strategies", callback_data="strategies"),
            types.InlineKeyboardButton("🔔 Alerts", callback_data="alerts"),
        ]
        
        # Add buttons in rows of 2
        for i in range(0, len(buttons), 2):
            if i + 1 < len(buttons):
                keyboard.row(buttons[i], buttons[i + 1])
            else:
                keyboard.row(buttons[i])
        
        return keyboard
    
    def _format_status_message(self, metrics: TradingMetrics, date: datetime) -> str:
        """Format current trading status message"""
        status_emoji = EMOJIS["profit"] if metrics.total_pnl >= 0 else EMOJIS["loss"]
        
        message = f"""
{status_emoji} *Live Trading Status*
📅 {date.strftime('%A, %B %d, %Y')}

💰 *Today's P&L:* ₹{metrics.total_pnl:,.2f}
📊 *Total Trades:* {metrics.total_trades}
🎯 *Win Rate:* {metrics.win_rate:.1f}%
⚡ *Profit Factor:* {metrics.profit_factor:.2f}

{EMOJIS["trend_up"] if metrics.total_pnl >= 0 else EMOJIS["trend_down"]} *Performance:* {'Profitable' if metrics.total_pnl >= 0 else 'Loss'} Day
"""
        
        if metrics.total_trades > 0:
            message += f"\n🔥 *Largest Win:* ₹{metrics.largest_win:,.2f}"
            message += f"\n💔 *Largest Loss:* ₹{metrics.largest_loss:,.2f}"
        
        return message
    
    def _format_daily_summary(self, metrics: TradingMetrics, date: datetime) -> str:
        """Format daily summary message"""
        summary_emoji = EMOJIS["rocket"] if metrics.total_pnl >= 0 else EMOJIS["trend_down"]
        
        message = f"""
{summary_emoji} *Daily Trading Summary*
📅 {date.strftime('%A, %B %d, %Y')}

💰 *Net P&L:* ₹{metrics.total_pnl:,.2f}
📈 *Gross Profit:* ₹{metrics.gross_profit:,.2f}
📉 *Gross Loss:* ₹{metrics.gross_loss:,.2f}

📊 *Trading Stats:*
• Total Trades: {metrics.total_trades}
• Winning Trades: {metrics.winning_trades}
• Losing Trades: {metrics.losing_trades}
• Win Rate: {metrics.win_rate:.1f}%

⚡ *Performance Metrics:*
• Profit Factor: {metrics.profit_factor:.2f}
• Expectancy: ₹{metrics.expectancy:,.2f}
• Avg Trade Duration: {metrics.average_trade_duration:.0f} min
"""
        
        if metrics.strategy_performance:
            message += "\n🎯 *Top Strategy:*"
            best_strategy = max(metrics.strategy_performance.items(), 
                              key=lambda x: x[1]['pnl'])
            message += f"\n• {best_strategy[0]}: ₹{best_strategy[1]['pnl']:,.2f}"
        
        return message

    def _handle_weekly_report(self, message):
        """Handle /weekly command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        processing_msg = self.bot.send_message(
            message.chat.id,
            f"{EMOJIS['calendar']} Generating weekly report...",
            parse_mode='Markdown'
        )

        try:
            report_path = self.report_generator.generate_weekly_report()

            if report_path:
                # Get week data for charts
                today = datetime.now()
                week_start, week_end = self.report_generator._get_week_range(today)
                trades = self.report_generator._load_trades_for_period(week_start, week_end)
                metrics = self.report_generator._calculate_metrics(trades, week_start, week_end)

                # Create weekly trend chart
                daily_pnl = {}
                current_date = week_start
                while current_date <= week_end:
                    daily_trades = self.report_generator._load_trades_for_date(current_date)
                    daily_metrics = self.report_generator._calculate_metrics(daily_trades, current_date)
                    daily_pnl[current_date.strftime('%Y-%m-%d')] = daily_metrics.total_pnl
                    current_date += timedelta(days=1)

                trend_chart = self.chart_generator.create_weekly_trend_chart(daily_pnl)

                # Create summary
                week_summary = self._format_weekly_summary(metrics, week_start, week_end)

                # Delete processing message
                self.bot.delete_message(message.chat.id, processing_msg.message_id)

                # Send summary and chart
                self.bot.send_message(message.chat.id, week_summary, parse_mode='Markdown')
                self.bot.send_photo(message.chat.id, trend_chart, caption="📈 Weekly P&L Trend")

                # Send HTML report
                if os.path.exists(report_path):
                    with open(report_path, 'rb') as report_file:
                        self.bot.send_document(message.chat.id, report_file, caption="📄 Weekly Report")
            else:
                self.bot.edit_message_text(
                    MESSAGE_TEMPLATES["no_data"],
                    message.chat.id,
                    processing_msg.message_id,
                    parse_mode='Markdown'
                )

        except Exception as e:
            self.logger.error(f"Error in weekly report: {e}")
            self.bot.edit_message_text(
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                message.chat.id,
                processing_msg.message_id,
                parse_mode='Markdown'
            )

    def _handle_monthly_report(self, message):
        """Handle /monthly command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        processing_msg = self.bot.send_message(
            message.chat.id,
            f"{EMOJIS['calendar']} Generating monthly report...",
            parse_mode='Markdown'
        )

        try:
            report_path = self.report_generator.generate_monthly_report()

            if report_path:
                # Get month data
                today = datetime.now()
                month_start, month_end = self.report_generator._get_month_range(today)
                trades = self.report_generator._load_trades_for_period(month_start, month_end)
                metrics = self.report_generator._calculate_metrics(trades, month_start, month_end)

                # Create risk metrics chart
                risk_chart = self.chart_generator.create_risk_metrics_chart(metrics)

                # Create summary
                month_summary = self._format_monthly_summary(metrics, month_start, month_end)

                # Delete processing message
                self.bot.delete_message(message.chat.id, processing_msg.message_id)

                # Send summary and chart
                self.bot.send_message(message.chat.id, month_summary, parse_mode='Markdown')
                self.bot.send_photo(message.chat.id, risk_chart, caption="🎯 Risk Metrics Analysis")

                # Send HTML report
                if os.path.exists(report_path):
                    with open(report_path, 'rb') as report_file:
                        self.bot.send_document(message.chat.id, report_file, caption="📄 Monthly Report")
            else:
                self.bot.edit_message_text(
                    MESSAGE_TEMPLATES["no_data"],
                    message.chat.id,
                    processing_msg.message_id,
                    parse_mode='Markdown'
                )

        except Exception as e:
            self.logger.error(f"Error in monthly report: {e}")
            self.bot.edit_message_text(
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                message.chat.id,
                processing_msg.message_id,
                parse_mode='Markdown'
            )

    def _handle_recent_trades(self, message):
        """Handle /trades command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        try:
            # Get recent trades (last 3 days)
            recent_trades = []
            for i in range(3):
                date = datetime.now() - timedelta(days=i)
                daily_trades = self.report_generator._load_trades_for_date(date)
                recent_trades.extend(daily_trades)

            if not recent_trades:
                self.bot.send_message(message.chat.id, MESSAGE_TEMPLATES["no_data"], parse_mode='Markdown')
                return

            # Format trades message
            trades_text = self._format_trades_message(recent_trades)

            # Create trades keyboard for pagination
            keyboard = self._create_trades_keyboard(len(recent_trades))

            self.bot.send_message(
                message.chat.id,
                trades_text,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

        except Exception as e:
            self.logger.error(f"Error in trades command: {e}")
            self.bot.send_message(
                message.chat.id,
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                parse_mode='Markdown'
            )

    def _handle_balance(self, message):
        """Handle /balance command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        try:
            # Get current balance info
            today = datetime.now()
            trades = self.report_generator._load_trades_for_date(today)
            metrics = self.report_generator._calculate_metrics(trades, today)

            # Calculate balance
            opening_balance = 320000.0  # From config
            current_balance = opening_balance + metrics.total_pnl

            # Create balance message
            balance_text = self._format_balance_message(opening_balance, current_balance, metrics)

            # Create balance trend chart (mock data for now)
            balance_history = [
                (today - timedelta(days=i), opening_balance + (metrics.total_pnl * (i+1)/7))
                for i in range(7, 0, -1)
            ]
            balance_chart = self.chart_generator.create_balance_trend_chart(balance_history)

            self.bot.send_message(message.chat.id, balance_text, parse_mode='Markdown')
            self.bot.send_photo(message.chat.id, balance_chart, caption="💰 Balance Trend")

        except Exception as e:
            self.logger.error(f"Error in balance command: {e}")
            self.bot.send_message(
                message.chat.id,
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                parse_mode='Markdown'
            )

    def _handle_strategies(self, message):
        """Handle /strategies command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        try:
            # Get today's strategy data
            today = datetime.now()
            trades = self.report_generator._load_trades_for_date(today)
            metrics = self.report_generator._calculate_metrics(trades, today)

            if not metrics.strategy_performance:
                self.bot.send_message(message.chat.id, MESSAGE_TEMPLATES["no_data"], parse_mode='Markdown')
                return

            # Create strategy performance chart
            strategy_chart = self.chart_generator.create_strategy_performance_chart(metrics)

            # Format strategy message
            strategy_text = self._format_strategy_message(metrics)

            self.bot.send_message(message.chat.id, strategy_text, parse_mode='Markdown')
            self.bot.send_photo(message.chat.id, strategy_chart, caption="🎯 Strategy Performance")

        except Exception as e:
            self.logger.error(f"Error in strategies command: {e}")
            self.bot.send_message(
                message.chat.id,
                MESSAGE_TEMPLATES["error"].format(error=str(e)),
                parse_mode='Markdown'
            )

    def _handle_alerts(self, message):
        """Handle /alerts command"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        # Create alerts keyboard
        keyboard = self._create_alerts_keyboard()

        alerts_text = f"""
🔔 *Trading Alerts Settings*

Current alert thresholds:
• Max Daily Loss: ₹{self.config.max_daily_loss_alert:,.2f}
• Max Drawdown: ₹{self.config.max_drawdown_alert:,.2f}
• Min Balance: ₹{self.config.min_balance_alert:,.2f}

Use the buttons below to configure alerts:
"""

        self.bot.send_message(
            message.chat.id,
            alerts_text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    def _handle_settings(self, message):
        """Handle /settings command (admin only)"""
        if not self._is_admin(message.from_user.id):
            self.bot.send_message(
                message.chat.id,
                "❌ Admin access required for settings.",
                parse_mode='Markdown'
            )
            return

        # Create settings keyboard
        keyboard = self._create_settings_keyboard()

        settings_text = f"""
⚙️ *Bot Settings* (Admin Only)

Current configuration:
• Daily Reports: {'✅' if self.config.send_daily_reports else '❌'}
• Weekly Reports: {'✅' if self.config.send_weekly_reports else '❌'}
• Monthly Reports: {'✅' if self.config.send_monthly_reports else '❌'}
• Trade Alerts: {'✅' if self.config.send_trade_alerts else '❌'}

Report Times:
• Daily: {self.config.daily_report_time}
• Weekly: {self.config.weekly_report_time}
• Monthly: {self.config.monthly_report_time}
"""

        self.bot.send_message(
            message.chat.id,
            settings_text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    def _handle_callback_query(self, call):
        """Handle inline keyboard callbacks"""
        # Debug: Log the actual user ID for troubleshooting
        self.logger.info(f"🔍 Callback from user {call.from_user.id} ({call.from_user.first_name}) - Action: {call.data}")
        self.logger.info(f"🔍 Authorized users: {self.config.authorized_users}")
        self.logger.info(f"🔍 Admin users: {self.config.admin_users}")

        if not self._is_authorized(call.from_user.id):
            self.logger.warning(f"❌ Unauthorized callback attempt by user {call.from_user.id}")
            self.bot.answer_callback_query(call.id, "Access denied")
            return

        # Route callback to appropriate handler
        if call.data == "status":
            self._handle_status(call.message)
        elif call.data == "daily":
            self._handle_daily_report(call.message)
        elif call.data == "weekly":
            self._handle_weekly_report(call.message)
        elif call.data == "monthly":
            self._handle_monthly_report(call.message)
        elif call.data == "trades":
            self._handle_recent_trades(call.message)
        elif call.data == "balance":
            self._handle_balance(call.message)
        elif call.data == "strategies":
            self._handle_strategies(call.message)
        elif call.data == "alerts":
            self._handle_alerts(call.message)
        elif call.data.startswith("alert_"):
            self._handle_alert_callback(call)
        elif call.data.startswith("setting_"):
            self._handle_setting_callback(call)

        # Answer callback query
        self.bot.answer_callback_query(call.id)

    def _handle_unknown_command(self, message):
        """Handle unknown commands"""
        if not self._is_authorized(message.from_user.id):
            self._send_unauthorized_message(message.chat.id)
            return

        unknown_text = f"""
❓ *Unknown Command*

I didn't understand that command.
Use /help to see available commands or /start for the main menu.
"""

        self.bot.send_message(message.chat.id, unknown_text, parse_mode='Markdown')

    # Helper methods
    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        # Log access attempts for debugging
        self.logger.info(f"🔍 Authorization check for user {user_id}")
        self.logger.info(f"🔍 Configured authorized users: {self.config.authorized_users}")

        # Check authorization
        is_auth = self.config.is_authorized(str(user_id))
        if not is_auth:
            self.logger.warning(f"❌ Unauthorized access attempt by user {user_id}")
            # Also log to console for immediate visibility
            print(f"❌ Unauthorized user {user_id} tried to access bot")
            print(f"🔍 Authorized users: {self.config.authorized_users}")

        return is_auth

    def _is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        return self.config.is_admin(str(user_id))

    def _send_unauthorized_message(self, chat_id: int):
        """Send unauthorized access message"""
        self.bot.send_message(
            chat_id,
            MESSAGE_TEMPLATES["unauthorized"],
            parse_mode='Markdown'
        )

    def _send_startup_message(self):
        """Send startup notification to authorized users"""
        startup_text = f"""
🚀 *Trading Bot Started*

Bot is now online and ready to serve trading reports!
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Use /start to access the main menu.
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, startup_text, parse_mode='Markdown')
            except Exception as e:
                self.logger.warning(f"Failed to send startup message to {user_id}: {e}")

    def _setup_scheduler(self):
        """Setup scheduled tasks for reports"""
        if self.config.send_daily_reports:
            schedule.every().day.at(self.config.daily_report_time).do(self._send_scheduled_daily_report)

        if self.config.send_weekly_reports:
            schedule.every().friday.at(self.config.weekly_report_time).do(self._send_scheduled_weekly_report)

        if self.config.send_monthly_reports:
            schedule.every().day.at(self.config.monthly_report_time).do(self._check_monthly_report)

    def _run_scheduler(self):
        """Run the scheduler in a separate thread"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

    def _send_scheduled_daily_report(self):
        """Send scheduled daily report"""
        try:
            self.logger.info("📊 Sending scheduled daily report")

            # Generate report
            report_path = self.report_generator.generate_daily_report()

            # Get today's data
            today = datetime.now()
            trades = self.report_generator._load_trades_for_date(today)
            metrics = self.report_generator._calculate_metrics(trades, today)

            # Create summary message
            summary_text = f"""
📊 *Scheduled Daily Report*
{self._format_daily_summary(metrics, today)}

Report generated automatically at {datetime.now().strftime('%H:%M')}
"""

            # Send to all authorized users
            all_users = set(self.config.authorized_users + self.config.admin_users)
            for user_id in all_users:
                try:
                    self.bot.send_message(user_id, summary_text, parse_mode='Markdown')

                    # Send chart
                    chart_buffer = self.chart_generator.create_daily_pnl_chart(trades, metrics)
                    self.bot.send_photo(user_id, chart_buffer, caption="📊 Daily P&L Chart")

                except Exception as e:
                    self.logger.error(f"Failed to send daily report to {user_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error in scheduled daily report: {e}")

    def _send_scheduled_weekly_report(self):
        """Send scheduled weekly report"""
        try:
            self.logger.info("📅 Sending scheduled weekly report")

            report_path = self.report_generator.generate_weekly_report()

            if report_path:
                # Get week data
                today = datetime.now()
                week_start, week_end = self.report_generator._get_week_range(today)
                trades = self.report_generator._load_trades_for_period(week_start, week_end)
                metrics = self.report_generator._calculate_metrics(trades, week_start, week_end)

                summary_text = f"""
📅 *Scheduled Weekly Report*
{self._format_weekly_summary(metrics, week_start, week_end)}

Report generated automatically at {datetime.now().strftime('%H:%M')}
"""

                # Send to all authorized users
                all_users = set(self.config.authorized_users + self.config.admin_users)
                for user_id in all_users:
                    try:
                        self.bot.send_message(user_id, summary_text, parse_mode='Markdown')
                    except Exception as e:
                        self.logger.error(f"Failed to send weekly report to {user_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error in scheduled weekly report: {e}")

    def _check_monthly_report(self):
        """Check if monthly report should be generated"""
        today = datetime.now()

        # Check if it's the last day of the month
        tomorrow = today + timedelta(days=1)
        if tomorrow.month != today.month:
            self._send_scheduled_monthly_report()

    def _send_scheduled_monthly_report(self):
        """Send scheduled monthly report"""
        try:
            self.logger.info("📆 Sending scheduled monthly report")

            report_path = self.report_generator.generate_monthly_report()

            if report_path:
                # Get month data
                today = datetime.now()
                month_start, month_end = self.report_generator._get_month_range(today)
                trades = self.report_generator._load_trades_for_period(month_start, month_end)
                metrics = self.report_generator._calculate_metrics(trades, month_start, month_end)

                summary_text = f"""
📆 *Scheduled Monthly Report*
{self._format_monthly_summary(metrics, month_start, month_end)}

Report generated automatically at {datetime.now().strftime('%H:%M')}
"""

                # Send to all authorized users
                all_users = set(self.config.authorized_users + self.config.admin_users)
                for user_id in all_users:
                    try:
                        self.bot.send_message(user_id, summary_text, parse_mode='Markdown')
                    except Exception as e:
                        self.logger.error(f"Failed to send monthly report to {user_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error in scheduled monthly report: {e}")

    # Import helper methods from telegram_helpers
    def _format_weekly_summary(self, metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.format_weekly_summary(metrics, start_date, end_date)

    def _format_monthly_summary(self, metrics: TradingMetrics, start_date: datetime, end_date: datetime) -> str:
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.format_monthly_summary(metrics, start_date, end_date)

    def _format_trades_message(self, trades: List[TradeRecord]) -> str:
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.format_trades_message(trades)

    def _format_balance_message(self, opening_balance: float, current_balance: float, metrics: TradingMetrics) -> str:
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.format_balance_message(opening_balance, current_balance, metrics)

    def _format_strategy_message(self, metrics: TradingMetrics) -> str:
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.format_strategy_message(metrics)

    def _create_trades_keyboard(self, total_trades: int):
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.create_trades_keyboard(total_trades)

    def _create_alerts_keyboard(self):
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.create_alerts_keyboard()

    def _create_settings_keyboard(self):
        from ..utils.telegram_helpers import TelegramHelpers
        return TelegramHelpers.create_settings_keyboard()

    def _handle_alert_callback(self, call):
        """Handle alert-related callbacks"""
        # Placeholder for alert configuration
        self.bot.answer_callback_query(call.id, "Alert settings coming soon!")

    def _handle_setting_callback(self, call):
        """Handle setting-related callbacks"""
        # Placeholder for settings configuration
        self.bot.answer_callback_query(call.id, "Settings configuration coming soon!")

    def send_trade_alert(self, trade: TradeRecord, alert_type: str = "trade_executed"):
        """Send trade alert to authorized users"""
        if not self.config.send_trade_alerts:
            return

        alert_emoji = "🔔"
        if alert_type == "trade_executed":
            alert_emoji = "✅" if trade.is_winner else "❌"
        elif alert_type == "stop_loss":
            alert_emoji = "🛑"
        elif alert_type == "target_hit":
            alert_emoji = "🎯"

        alert_text = f"""
{alert_emoji} *Trade Alert*

📊 *{trade.symbol}* - {trade.strategy}
💰 Entry: ₹{trade.price:.2f}
🎯 Target: ₹{trade.target:.2f}
🛑 Stop Loss: ₹{trade.stop_loss:.2f}
📈 P&L: ₹{trade.pnl:,.2f}

⏰ {datetime.now().strftime('%H:%M:%S')}
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
            except Exception as e:
                self.logger.error(f"Failed to send trade alert to {user_id}: {e}")

    def send_balance_alert(self, current_balance: float, alert_type: str):
        """Send balance-related alerts"""
        if not self.config.send_balance_updates:
            return

        alert_emoji = "⚠️"
        if alert_type == "low_balance":
            alert_emoji = "🔴"
        elif alert_type == "high_drawdown":
            alert_emoji = "📉"
        elif alert_type == "daily_loss_limit":
            alert_emoji = "🛑"

        alert_text = f"""
{alert_emoji} *Balance Alert*

Current Balance: ₹{current_balance:,.2f}
Alert Type: {alert_type.replace('_', ' ').title()}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
            except Exception as e:
                self.logger.error(f"Failed to send balance alert to {user_id}: {e}")

    def send_order_placement_alert(self, order, strategy: str, account_name: str = ""):
        """Send immediate order placement notification"""
        if not self.config.send_trade_alerts:
            return

        # Determine order type and emoji
        order_emoji = "📈" if hasattr(order, 'transaction_type') and order.transaction_type.value == "BUY" else "📉"

        # Calculate order value
        order_value = order.price * order.quantity if hasattr(order, 'price') and hasattr(order, 'quantity') else 0

        # Calculate risk-reward ratio
        risk = abs(order.price - order.stop_loss) if hasattr(order, 'stop_loss') else 0
        reward = abs(order.target - order.price) if hasattr(order, 'target') else 0
        rr_ratio = reward / risk if risk > 0 else 0

        alert_text = f"""
{order_emoji} *ORDER PLACED*

🏷️ *Symbol:* {order.symbol}
📊 *Strategy:* {strategy}
{f"🏦 *Account:* {account_name}" if account_name else ""}

💰 *Entry Price:* ₹{order.price:.2f}
📊 *Quantity:* {order.quantity:,}
💵 *Order Value:* ₹{order_value:,.2f}

🎯 *Target:* ₹{order.target:.2f}
🛑 *Stop Loss:* ₹{order.stop_loss:.2f}
⚖️ *Risk:Reward:* 1:{rr_ratio:.2f}

🆔 *Order ID:* {getattr(order, 'order_id', 'Pending')}
⏰ *Time:* {datetime.now().strftime('%H:%M:%S')}

📈 *Trade Direction:* {getattr(order, 'transaction_type', 'N/A').value if hasattr(getattr(order, 'transaction_type', None), 'value') else 'N/A'}
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                self.logger.info(f"📱 Order placement alert sent to user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to send order placement alert to {user_id}: {e}")

    def send_position_update_alert(self, symbol: str, action: str, price: float, pnl: float, strategy: str, account_name: str = ""):
        """Send position update notifications (exit, stop loss hit, target reached)"""
        if not self.config.send_trade_alerts:
            return

        # Determine action emoji
        action_emoji = "✅" if pnl >= 0 else "❌"
        if action == "TARGET_HIT":
            action_emoji = "🎯"
        elif action == "STOP_LOSS":
            action_emoji = "🛑"
        elif action == "TIME_EXIT":
            action_emoji = "⏰"
        elif action == "MANUAL_SQUARE_OFF":
            action_emoji = "🔄"

        # Format action text
        action_text = action.replace('_', ' ').title()

        alert_text = f"""
{action_emoji} *POSITION {action_text.upper()}*

🏷️ *Symbol:* {symbol}
📊 *Strategy:* {strategy}
{f"🏦 *Account:* {account_name}" if account_name else ""}

💰 *Exit Price:* ₹{price:.2f}
📈 *P&L:* ₹{pnl:,.2f}
📊 *Result:* {'Profit' if pnl >= 0 else 'Loss'}

⏰ *Time:* {datetime.now().strftime('%H:%M:%S')}
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                self.logger.info(f"📱 Position update alert sent to user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to send position update alert to {user_id}: {e}")

    def send_daily_summary_alert(self, total_trades: int, total_pnl: float, win_rate: float, open_positions: int):
        """Send end-of-day summary alert"""
        if not self.config.send_daily_reports:
            return

        summary_emoji = "🚀" if total_pnl >= 0 else "📉"

        alert_text = f"""
{summary_emoji} *END OF DAY SUMMARY*

📅 *Date:* {datetime.now().strftime('%A, %B %d, %Y')}

📊 *Trading Stats:*
• Total Trades: {total_trades}
• Win Rate: {win_rate:.1f}%
• Open Positions: {open_positions}

💰 *Daily P&L:* ₹{total_pnl:,.2f}
📈 *Performance:* {'Profitable' if total_pnl >= 0 else 'Loss'} Day

⏰ *Market Close:* {datetime.now().strftime('%H:%M:%S')}

Use /daily for detailed report with charts.
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                self.logger.info(f"📱 Daily summary alert sent to user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to send daily summary alert to {user_id}: {e}")

    def send_system_status_alert(self, status: str, message: str):
        """Send system status alerts (startup, shutdown, errors)"""
        status_emoji = {
            "startup": "🚀",
            "shutdown": "🛑",
            "error": "❌",
            "warning": "⚠️",
            "info": "ℹ️"
        }.get(status.lower(), "📢")

        alert_text = f"""
{status_emoji} *SYSTEM {status.upper()}*

{message}

⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # Send to all authorized users
        all_users = set(self.config.authorized_users + self.config.admin_users)
        for user_id in all_users:
            try:
                self.bot.send_message(user_id, alert_text, parse_mode='Markdown')
                self.logger.info(f"📱 System status alert sent to user {user_id}")
            except Exception as e:
                self.logger.error(f"Failed to send system status alert to {user_id}: {e}")
