#!/usr/bin/env python3
"""
Test script for Stock Symbol Integration Service
This script tests the integration between stocks_to_monitor.csv and MongoDB symbol mappings
"""

import os
import sys
import logging

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.stock_symbol_integration_service import StockSymbolIntegrationService

def setup_logging():
    """Setup logging configuration"""
    logger = logging.getLogger('SymbolIntegrationTest')
    logger.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(console_handler)
    
    return logger

def test_symbol_integration():
    """Test the Stock Symbol Integration Service"""
    logger = setup_logging()
    
    logger.info("=" * 50)
    logger.info("TESTING STOCK SYMBOL INTEGRATION SERVICE")
    logger.info("=" * 50)
    
    # Initialize service
    service = StockSymbolIntegrationService(data_directory="../data", logger=logger)
    
    # Test getting monitored stocks
    stocks = service.get_monitored_stocks()
    logger.info(f"Found {len(stocks)} monitored stocks")
    
    if stocks:
        # Show first 5 stocks
        logger.info("Sample stocks:")
        for i, stock in enumerate(stocks[:5]):
            logger.info(f"  {i+1}. {stock.symbol} - {stock.strategy} - {stock.timeframe}")
        
        # Test getting symbol tokens
        logger.info("\nTesting symbol token lookup:")
        for i, stock in enumerate(stocks[:5]):
            token_info = service.get_symbol_token(stock.symbol)
            if token_info:
                token, exchange = token_info
                logger.info(f"  {stock.symbol}: Token={token}, Exchange={exchange}")
            else:
                logger.warning(f"  {stock.symbol}: No token found")
    
    # Test MongoDB connection status
    connection_status = service.get_connection_status()
    logger.info("\nMongoDB Connection Status:")
    for key, value in connection_status.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("=" * 50)
    logger.info("TEST COMPLETED")
    logger.info("=" * 50)

if __name__ == "__main__":
    test_symbol_integration()